<script>
  // #ifdef MP-WEIXIN
  // 在App启动前就初始化polyfill
  import initWxPolyfill from './platform/mp-weixin/wx-polyfill';
  initWxPolyfill();
  // #endif

  export default {
    onLaunch: function () {
      console.log('App Launch');
    },
    onShow: function () {
      console.log('App Show');
    },
    onHide: function () {
      console.log('App Hide');
    },
  };
</script>

<style lang="scss">

  /* 全局样式 */
  page {
    font-family:
      'PingFang SC',
      /* iOS/macOS */ 'HarmonyOS Sans',
      /* 华为/部分安卓 */ 'Source Han Sans SC',
      /* 思源黑体，部分安卓/谷歌 */ 'Noto Sans SC',
      /* 谷歌 */ 'Microsoft YaHei',
      /* Windows */ 'Heiti SC',
      /* 老macOS */ Arial,
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #f8f4e9;
  }

  /* 去除默认边距 */
  view,
  text {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
</style>
