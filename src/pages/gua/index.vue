<template>
  <PageWrapper title="卦典" :show-logo="true" :show-back="false" >
    <view class="container">
      <!-- 主要学习模块 -->
      <view class="main-modules">
        
        <view class="module-item" @tap="navigateToHetu">
          <view class="module-icon">🌊</view>
          <view class="module-content">
            <view class="module-name">河图洛书</view>
            <view class="module-desc">古代数理哲学的源头</view>
          </view>
          <view class="module-arrow">›</view>
        </view>

        <view class="module-item" @tap="navigateToYinyang">
          <view class="module-icon">☯️</view>
          <view class="module-content">
            <view class="module-name">阴阳八卦</view>
            <view class="module-desc">宇宙万物的基本规律</view>
          </view>
          <view class="module-arrow">›</view>
        </view>

        <view class="module-item" @tap="navigateToHexagrams">
          <view class="module-icon">📖</view>
          <view class="module-content">
            <view class="module-name">六十四卦</view>
            <view class="module-desc">完整的卦象体系</view>
          </view>
          <view class="module-arrow">›</view>
        </view>

        <view class="module-item" @tap="navigateToPigua">
          <view class="module-icon">🌙</view>
          <view class="module-content">
            <view class="module-name">十二辟卦</view>
            <view class="module-desc">月令卦象与时节变化</view>
          </view>
          <view class="module-arrow">›</view>
        </view>

        <view class="module-item" @tap="navigateToWuxing">
          <view class="module-icon">🌟</view>
          <view class="module-content">
            <view class="module-name">五行基础</view>
            <view class="module-desc">五行相生相克的奥秘</view>
          </view>
          <view class="module-arrow">›</view>
        </view>

        <view class="module-item" @tap="navigateToGanzhi">
          <view class="module-icon">🗓️</view>
          <view class="module-content">
            <view class="module-name">天干地支</view>
            <view class="module-desc">时间与空间的符号系统</view>
          </view>
          <view class="module-arrow">›</view>
        </view>
      </view>

      <!-- 快速功能 -->
      <view class="quick-functions">
        <view class="section-title">快捷入口</view>
        <view class="functions-grid">
          <view class="function-item" @tap="navigateToLibrary">
            <view class="function-icon">📚</view>
            <view class="function-text">藏经阁</view>
          </view>

          <view class="function-item" @tap="navigateToQuickDivination">
            <view class="function-icon">⚡</view>
            <view class="function-text">快速取卦</view>
          </view>

          <view class="function-item" @tap="navigateToCulture">
            <view class="function-icon">🏛️</view>
            <view class="function-text">易经文化</view>
          </view>

          <view class="function-item" @tap="navigateToGuide">
            <view class="function-icon">📖</view>
            <view class="function-text">学习指南</view>
          </view>
        </view>
      </view>
    </view>

    <YiTabBar />
  </PageWrapper>
</template>

<script setup lang="ts">
  import PageWrapper from '@/components/PageWrapper.vue';
  import YiTabBar from '@/components/YiTabBar.vue';

  // 河图洛书
  const navigateToHetu = () => {
    uni.showToast({
      title: '河图洛书功能开发中',
      icon: 'none'
    });
  };

  // 阴阳八卦
  const navigateToYinyang = () => {
    uni.navigateTo({
      url: '/pages/gua/bagua/bagua'
    });
  };

  // 六十四卦
  const navigateToHexagrams = () => {
    uni.navigateTo({
      url: '/pages/gua/hexagram/hexagram-list'
    });
  };

  // 十二辟卦
  const navigateToPigua = () => {
    uni.showToast({
      title: '十二辟卦功能开发中',
      icon: 'none'
    });
  };

  // 五行基础
  const navigateToWuxing = () => {
    uni.navigateTo({
      url: '/pages/gua/wuxing/wuxing'
    });
  };

  // 天干地支
  const navigateToGanzhi = () => {
    uni.navigateTo({
      url: '/pages/gua/ganzhi/ganzhi'
    });
  };

  // 藏经阁
  const navigateToLibrary = () => {
    uni.showToast({
      title: '藏经阁功能开发中',
      icon: 'none'
    });
  };

  // 快速取卦
  const navigateToQuickDivination = () => {
    uni.navigateTo({
      url: '/pages/gua/shortcuts/quick-hexagram'
    });
  };

  // 易经文化
  const navigateToCulture = () => {
    uni.navigateTo({
      url: '/pages/gua/shortcuts/culture'
    });
  };

  // 学习指南
  const navigateToGuide = () => {
    uni.navigateTo({
      url: '/pages/gua/shortcuts/guide'
    });
  };
</script>

<style lang="scss" scoped>
  .container {
    padding: 40rpx;
    padding-bottom: 120rpx;
  }

  // 章节标题
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #8b4513;
    margin-bottom: 24rpx;
    padding-bottom: 12rpx;
    border-bottom: 2rpx solid #e8e8e8;
  }

  // 主要学习模块
  .main-modules {
    margin-bottom: 48rpx;

    .module-item {
      display: flex;
      align-items: center;
      padding: 24rpx;
      margin-bottom: 20rpx;
      background: white;
      border-radius: 12rpx;
      border: 1rpx solid #e8e8e8;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
      transition: all 0.2s ease;

      &:active {
        transform: scale(0.98);
        background: #f8f9fa;
      }

      &:last-child {
        margin-bottom: 0;
      }

      .module-icon {
        font-size: 42rpx;
        width: 80rpx;
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(139, 69, 19, 0.1);
        border-radius: 12rpx;
        margin-right: 40rpx;
        flex-shrink: 0;
      }

      .module-content {
        flex: 1;

        .module-name {
          font-size: 32rpx;
          font-weight: 600;
          color: #8b4513;
          margin-bottom: 6rpx;
        }

        .module-desc {
          font-size: 22rpx;
          color: #666;
          line-height: 1.4;
        }
      }

      .module-arrow {
        font-size: 32rpx;
        color: #ccc;
        font-weight: 300;
      }
    }
  }

  // 快速功能
  .quick-functions {
    .functions-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 12rpx;

      .function-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 24rpx 12rpx;
        background: white;
        border-radius: 12rpx;
        border: 1rpx solid #e8e8e8;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
        transition: all 0.2s ease;

        &:active {
          transform: scale(0.95);
          background: #f8f9fa;
        }

        .function-icon {
          font-size: 38rpx;
          margin-bottom: 8rpx;
          opacity: 0.8;
        }

        .function-text {
          font-size: 24rpx;
          font-weight: 500;
          color: #8b4513;
          text-align: center;
        }
      }
    }
  }

  // 响应式适配
  @media (max-width: 750rpx) {
    .container {
      padding: 24rpx 16rpx;
    }

    .section-title {
      font-size: 28rpx;
      margin-bottom: 20rpx;
    }

    .main-modules {
      .module-item {
        padding: 20rpx;

        .module-icon {
          font-size: 28rpx;
          width: 48rpx;
          height: 48rpx;
          margin-right: 16rpx;
        }

        .module-content {
          .module-name {
            font-size: 24rpx;
          }

          .module-desc {
            font-size: 20rpx;
          }
        }

        .module-arrow {
          font-size: 28rpx;
        }
      }
    }

    .quick-functions {
      .functions-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 16rpx;

        .function-item {
          padding: 28rpx 16rpx;

          .function-icon {
            font-size: 36rpx;
          }

          .function-text {
            font-size: 22rpx;
          }
        }
      }
    }
  }
</style>
