<template>
  <PageWrapper title="八卦图" :show-back="true">
    <view class="container">

      <!-- 环绕式八卦图 -->
      <view class="bagua-circle-section">
        <BaguaCircle
          size="medium"
          :layout="currentLayout"
          :show-family="false"
          :show-direction="true"
          :show-layout-switch="true"
          @layout-change="onLayoutChange"
        />

        <!-- 详细信息按钮 -->
        <view class="detail-button-container">
          <view class="detail-button" @tap="goToDetail">
            {{ currentLayout === 'xiantian' ? '查看先天八卦次序图' : '查看详细属性表' }}
          </view>
        </view>
      </view>



      <!-- 先天八卦介绍 -->
      <view v-if="currentLayout === 'xiantian'" class="layout-description">
        <view class="description-content">
          <view class="description-title">先天八卦（伏羲八卦）</view>
          <view class="description-text">
            先天八卦由伏羲所创，体现天地未分时的原始状态，按照阴阳消长的自然规律排列。
            乾坤定南北，离坎列东西，是宇宙生成演化的根本模式，重在体现事物的本质属性。
          </view>
        </view>
      </view>

      <!-- 后天八卦介绍 -->
      <view v-if="currentLayout === 'houtian'" class="layout-description">
        <view class="description-content">
          <view class="description-title">后天八卦（文王八卦）</view>
          <view class="description-text">
            后天八卦由文王演绎，根据万物运行规律排列，体现事物发展变化过程。
            离南坎北震东兑西，反映四季轮回、万物生长的自然规律，重在实际应用。
          </view>
        </view>
      </view>

    </view>
  </PageWrapper>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import PageWrapper from '@/components/PageWrapper.vue';
  import BaguaCircle from '@/components/BaguaCircle.vue';


  // 当前布局状态
  const currentLayout = ref<'xiantian' | 'houtian'>('xiantian');



  // 布局切换
  const onLayoutChange = (newLayout: 'xiantian' | 'houtian') => {
    currentLayout.value = newLayout;
  };

  // 跳转到详细页面
  const goToDetail = () => {
    if (currentLayout.value === 'xiantian') {
      // 跳转到先天八卦次序图页面
      uni.navigateTo({
        url: '/pages/gua/bagua/bagua-sequence'
      });
    } else {
      // 跳转到详细属性表页面
      uni.navigateTo({
        url: '/pages/gua/bagua/bagua-attributes'
      });
    }
  };
</script>

<style lang="scss" scoped>
  .container {
    min-height: 100vh;
    padding: 30rpx;
  }

  .intro-section {
    text-align: center;
    margin-bottom: 60rpx;
    padding: 40rpx 32rpx;
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-radius: 24rpx;
    box-shadow: 0 8rpx 32rpx rgba(139, 69, 19, 0.1);

    .intro-title {
      font-size: 40rpx;
      font-weight: bold;
      color: #2c3e50;
      margin-bottom: 24rpx;
    }

    .intro-text {
      font-size: 28rpx;
      color: #7f8c8d;
      line-height: 1.6;
    }
  }

  .bagua-circle-section {
    text-align: center;
    margin-bottom: 60rpx;
    padding-top: 200rpx;
    position: relative; // 为Tab定位提供参考
    overflow: visible; // 允许Tab显示在容器外

    .section-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #2c3e50;
      margin-bottom: 40rpx;
    }

    .detail-button-container {
      margin-top: 30rpx;
      text-align: center;

      .detail-button {
        display: inline-block;
        padding: 16rpx 32rpx;
        // 统一使用全站主色系按钮样式
        background: linear-gradient(135deg, #8b4513, #a0522d);
        color: white;
        border-radius: 25rpx;
        font-size: 26rpx;
        font-weight: 500;
        box-shadow: 0 6rpx 20rpx rgba(139, 69, 19, 0.3);
        transition: all 0.3s ease;

        &:active {
          transform: translateY(2rpx);
          box-shadow: 0 4rpx 15rpx rgba(139, 69, 19, 0.4);
        }
      }
    }
  }

  .bagua-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24rpx;
    margin-bottom: 60rpx;
  }

  .bagua-card {
    background: white;
    border-radius: 20rpx;
    padding: 32rpx 24rpx;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
    border: 2rpx solid rgba(139, 69, 19, 0.1);
    transition: all 0.3s ease;
    text-align: center;

    &:active {
      transform: scale(0.95);
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.12);
    }

    .bagua-symbol {
      font-size: 64rpx;
      color: #8B4513;
      margin-bottom: 20rpx;
      font-weight: bold;
    }

    .bagua-info {
      margin-bottom: 20rpx;

      .bagua-name {
        font-size: 32rpx;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 8rpx;
      }

      .bagua-nature {
        font-size: 24rpx;
        color: #8B4513;
        margin-bottom: 4rpx;
      }

      .bagua-element {
        font-size: 22rpx;
        color: #95a5a6;
      }
    }

    .bagua-binary {
      font-size: 20rpx;
      color: #bdc3c7;
      font-family: 'Courier New', monospace;
      letter-spacing: 4rpx;
    }
  }

  .attributes-section {
    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #2c3e50;
      margin-bottom: 32rpx;
      text-align: center;
    }

    .attributes-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 20rpx;
    }

    .attribute-item {
      background: white;
      border-radius: 16rpx;
      padding: 32rpx 24rpx;
      text-align: center;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);

      .attribute-label {
        font-size: 28rpx;
        font-weight: bold;
        color: #8B4513;
        margin-bottom: 12rpx;
      }

      .attribute-desc {
        font-size: 24rpx;
        color: #7f8c8d;
      }
    }
  }

  .layout-description {
    margin-top: 60rpx;

    .description-content {
      background: rgba(248, 248, 250, 0.6);
      border: 1rpx solid rgba(139, 69, 19, 0.08);
      border-radius: 16rpx;
      padding: 32rpx 28rpx;
      box-shadow: 0 2rpx 12rpx rgba(139, 69, 19, 0.04);
      backdrop-filter: blur(10rpx);

      .description-title {
        font-size: 26rpx;
        font-weight: 500;
        color: #6d4c1a;
        margin-bottom: 20rpx;
        text-align: center;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: -10rpx;
          left: 50%;
          transform: translateX(-50%);
          width: 60rpx;
          height: 2rpx;
          background: linear-gradient(90deg, transparent, rgba(139, 69, 19, 0.2), transparent);
        }
      }

      .description-text {
        font-size: 24rpx;
        color: rgba(100, 100, 100, 0.75);
        line-height: 1.8;
        text-align: left;
        letter-spacing: 0.5rpx;
        margin-top: 16rpx;
      }
    }
  }


</style>
