<template>
  <PageWrapper title="先天八卦次序图" :show-back="true">
    <view class="container">
      <!-- 先天八卦次序图 -->
      <view class="xiantian-sequence-section">
        <view class="sequence-diagram">
          <!-- 八卦层 -->
          <view class="sequence-row bagua-row">
            <view class="sequence-label">八卦</view>
            <view 
              v-for="gua in xiantianSequence" 
              :key="gua.name"
              class="sequence-cell bagua-cell"
            >
              <view class="gua-symbol">{{ gua.symbol }}</view>
              <view class="gua-name">{{ gua.name }}</view>
            </view>
          </view>

          <!-- 四象层 -->
          <view class="sequence-row sixiang-row">
            <view class="sequence-label">四象</view>
            <view class="sequence-cell sixiang-cell taiyang">
              <view class="sixiang-symbol">⚌</view>
              <view class="sixiang-name">太阳</view>
            </view>
            <view class="sequence-cell sixiang-cell shaoyin">
              <view class="sixiang-symbol">⚍</view>
              <view class="sixiang-name">少阴</view>
            </view>
            <view class="sequence-cell sixiang-cell shaoyang">
              <view class="sixiang-symbol">⚎</view>
              <view class="sixiang-name">少阳</view>
            </view>
            <view class="sequence-cell sixiang-cell taiyin">
              <view class="sixiang-symbol">⚏</view>
              <view class="sixiang-name">太阴</view>
            </view>
          </view>

          <!-- 两仪层 -->
          <view class="sequence-row liangyi-row">
            <view class="sequence-label">两仪</view>
            <view class="sequence-cell liangyi-cell yang">
              <view class="liangyi-symbol">—</view>
              <view class="liangyi-name">阳</view>
            </view>
            <view class="sequence-cell liangyi-cell yin">
              <view class="liangyi-symbol">- -</view>       
              <view class="liangyi-name">阴</view>
            </view>
          </view>

          <!-- 太极层 -->
          <view class="sequence-row taiji-row">
            <view class="sequence-label">太极</view>
            <view class="sequence-cell taiji-cell">
              <view class="taiji-name">太极</view>
            </view>
          </view>
        </view>

      </view>

      <!-- 先天八卦的哲学内涵 -->
      <view class="philosophy-explanation">
        <view class="philosophy-title">先天八卦的哲学内涵</view>

        <!-- 演化说明 -->
        <view class="evolution-section">
          <view class="evolution-title">演化过程</view>
          <view class="evolution-content">
            <view class="evolution-item">
              <text class="evolution-label">太极：</text>
              <text class="evolution-text">万物之源，阴阳未分的原始状态</text>
            </view>
            <view class="evolution-item">
              <text class="evolution-label">两仪：</text>
              <text class="evolution-text">阴阳二气，天地之始</text>
            </view>
            <view class="evolution-item">
              <text class="evolution-label">四象：</text>
              <text class="evolution-text">太阳、少阴、少阳、太阴，四时之象</text>
            </view>
            <view class="evolution-item">
              <text class="evolution-label">八卦：</text>
              <text class="evolution-text">天地雷风水火山泽，万物之象</text>
            </view>
          </view>
        </view>

        <!-- 哲学内涵 -->
        <view class="philosophy-content">
          <view class="philosophy-item">
            <view class="philosophy-item-title">🎯 太极生两仪</view>
            <view class="philosophy-item-content">
              太极是宇宙的本源，包含阴阳两种基本属性。太极分化产生阴阳两仪，代表天地、日月、男女等对立统一的概念。
            </view>
          </view>
          <view class="philosophy-item">
            <view class="philosophy-item-title">⚡ 两仪生四象</view>
            <view class="philosophy-item-content">
              阴阳两仪进一步分化，产生太阳、少阴、少阳、太阴四象，对应春夏秋冬四季，体现了事物发展的四个阶段。
            </view>
          </view>
          <view class="philosophy-item">
            <view class="philosophy-item-title">🌟 四象生八卦</view>
            <view class="philosophy-item-content">
              四象再次分化，形成八卦。每卦代表一种自然现象和人文概念，构成了完整的宇宙模型和认知体系。
            </view>
          </view>
        </view>
      </view>
    </view>
  </PageWrapper>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import PageWrapper from '@/components/PageWrapper.vue';

  // 先天八卦次序（按照伏羲八卦顺序）
  const xiantianSequence = ref([
    { name: '乾', symbol: '☰' },
    { name: '兑', symbol: '☱' },
    { name: '离', symbol: '☲' },
    { name: '震', symbol: '☳' },
    { name: '巽', symbol: '☴' },
    { name: '坎', symbol: '☵' },
    { name: '艮', symbol: '☶' },
    { name: '坤', symbol: '☷' }
  ]);
</script>

<style lang="scss" scoped>
  .container {
    min-height: 100vh;
    padding: 30rpx;
  }

  .page-header {
    text-align: center;
    margin-bottom: 40rpx;
    padding: 40rpx 32rpx;
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-radius: 24rpx;
    box-shadow: 0 8rpx 32rpx rgba(139, 69, 19, 0.1);

    .title {
      font-size: 40rpx;
      font-weight: bold;
      color: #2c3e50;
      margin-bottom: 16rpx;
    }

    .subtitle {
      font-size: 26rpx;
      color: #7f8c8d;
      line-height: 1.6;
    }
  }

  .xiantian-sequence-section {
    padding: 40rpx 32rpx;
    background: #ffffff;
    border-radius: 24rpx;
    box-shadow: 0 8rpx 32rpx rgba(139, 69, 19, 0.1);
    margin-bottom: 40rpx;

    .sequence-diagram {
      display: flex;
      flex-direction: column;
      gap: 20rpx;
      margin-bottom: 10rpx;
    }

    .sequence-row {
      display: flex;
      align-items: center;
      gap: 12rpx;

      .sequence-label {
        width: 80rpx;
        font-size: 24rpx;
        font-weight: bold;
        color: #8b4513;
        text-align: center;
      }
    }

    // 八卦行样式
    .bagua-row {
      .bagua-cell {
        flex: 1;
        padding: 12rpx 8rpx;
        background: rgba(248, 244, 233, 0.4);
        border: 3rpx solid rgba(139, 69, 19, 0.1);
        border-radius: 12rpx;
        text-align: center;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2rpx);
          box-shadow: 0 4rpx 12rpx rgba(139, 69, 19, 0.2);
          background: rgba(248, 244, 233, 0.4);
        }

        .gua-symbol {
          font-size: 28rpx;
          color: rgba(139, 69, 19, 0.9);
          margin-bottom: 4rpx;
        }

        .gua-name {
          font-size: 20rpx;
          color: rgba(44, 62, 80, 0.9);
          font-weight: bold;
        }
      }
    }

    // 四象行样式
    .sixiang-row {
      .sixiang-cell {
        flex: 2;
        padding: 12rpx 12rpx;
        border-radius: 12rpx;
        text-align: center;
        transition: all 0.3s ease;

        .sixiang-symbol {
          font-size: 32rpx;
          margin-bottom: 6rpx;
          font-weight: bold;
          letter-spacing: 2rpx;
        }

        .sixiang-name {
          font-size: 22rpx;
          font-weight: bold;
          margin-bottom: 4rpx;
          color: rgba(44, 62, 80, 0.9);
        }

        .sixiang-desc {
          font-size: 18rpx;
          opacity: 0.8;
        }

        &.taiyang {
          background: rgba(248, 244, 233, 0.4);
          border: 2rpx solid rgba(139, 69, 19, 0.2);
          color: rgba(139, 69, 19, 0.9);
        }

        &.shaoyin {
          background: rgba(248, 244, 233, 0.3);
          border: 2rpx solid rgba(139, 69, 19, 0.15);
          color: rgba(139, 69, 19, 0.8);
        }

        &.shaoyang {
          background: rgba(248, 244, 233, 0.25);
          border: 2rpx solid rgba(139, 69, 19, 0.12);
          color: rgba(139, 69, 19, 0.75);
        }

        &.taiyin {
          background: rgba(248, 244, 233, 0.2);
          border: 2rpx solid rgba(139, 69, 19, 0.1);
          color: rgba(139, 69, 19, 0.7);
        }
      }
    }

    // 两仪行样式
    .liangyi-row {
      .liangyi-cell {
        flex: 4;
        padding: 4rpx 0 16rpx;
        border-radius: 12rpx;
        text-align: center;
        transition: all 0.3s ease;

        .liangyi-name {
          font-size: 22rpx;
          font-weight: bold;
          color: rgba(44, 62, 80, 0.9);
        }

        &.yang {
          background: rgba(248, 244, 233, 0.5);
          border: 2rpx solid rgba(139, 69, 19, 0.3);
          color: rgba(139, 69, 19, 0.9);
        }

        &.yin {
          background: rgba(248, 244, 233, 0.35);
          border: 2rpx solid rgba(139, 69, 19, 0.25);
          color: rgba(139, 69, 19, 0.8);
        }
      }
    }

    // 太极行样式
    .taiji-row {
      .taiji-cell {
        flex: 8;
        padding: 12rpx 20rpx;
        background: rgba(248, 244, 233, 0.6);
        border: 2rpx solid rgba(139, 69, 19, 0.4);
        border-radius: 12rpx;
        text-align: center;
        transition: all 0.3s ease;

        .taiji-symbol {
          font-size: 40rpx;
          color: rgba(139, 69, 19, 0.9);
        }
        .taiji-name {
          font-size: 22rpx;
          font-weight: bold;
          color: rgba(44, 62, 80, 0.9);
        }
      }
    }

  }

  .philosophy-explanation {
    margin-top: 40rpx;
    padding: 32rpx;

    .philosophy-title {
      font-size: 30rpx;
      font-weight: bold;
      color: #2c3e50;
      text-align: center;
      margin-bottom: 32rpx;
    }

    .evolution-section {
      margin-bottom: 32rpx;
      padding: 24rpx;
      background: rgba(248, 244, 233, 0.3);
      border-radius: 16rpx;
      border: 1rpx solid rgba(139, 69, 19, 0.06);

      .evolution-title {
        font-size: 26rpx;
        font-weight: 600;
        color: rgba(139, 69, 19, 0.8);
        margin-bottom: 20rpx;
        text-align: center;
      }

      .evolution-content {
        display: flex;
        flex-direction: column;
        gap: 16rpx;
      }

      .evolution-item {
        display: flex;
        align-items: flex-start;
        gap: 12rpx;

        .evolution-label {
          font-size: 24rpx;
          font-weight: 600;
          color: rgba(139, 69, 19, 0.7);
          min-width: 80rpx;
        }

        .evolution-text {
          font-size: 24rpx;
          color: #666;
          flex: 1;
        }
      }
    }

    .philosophy-content {
      display: flex;
      flex-direction: column;
      gap: 20rpx;
    }

    .philosophy-item {
      padding: 24rpx;
      background: rgba(248, 244, 233, 0.15);
      border-radius: 16rpx;
      border: 1rpx solid rgba(139, 69, 19, 0.05);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2rpx);
        box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.1);
      }

      .philosophy-item-title {
        font-size: 26rpx;
        font-weight: 600;
        color: rgba(139, 69, 19, 0.8);
        margin-bottom: 12rpx;
        display: flex;
        align-items: center;
        gap: 8rpx;
      }

      .philosophy-item-content {
        font-size: 24rpx;
        color: rgba(102, 102, 102, 0.9);
        line-height: 1.8;
        text-align: justify;
      }
    }
  }
</style>
