<template>
  <PageWrapper title="八卦属性对照表" :show-back="true">
    <view class="detail-page">
      <!-- 属性选择 -->
      <view class="attribute-section">
        <view class="layout-tabs">
          <view
            v-for="attr in attributeList"
            :key="attr.key"
            :class="['tab-item', { active: selectedAttribute === attr.key }]"
            @tap="selectAttribute(attr.key)"
          >
            {{ attr.name }}
          </view>
        </view>
      </view>

      <!-- 属性对照表 -->
      <view class="content-section">
        <view class="bagua-list">
          <view
            v-for="gua in sortedBaguaList"
            :key="gua.name"
            class="bagua-item"
          >
            <view class="bagua-info">
              <view class="bagua-symbol">{{ gua.symbol }}</view>
              <view class="bagua-name">{{ gua.name }}</view>
            </view>
            <view class="bagua-content">
              {{ gua[selectedAttribute as keyof typeof gua] || '-' }}
            </view>
          </view>
        </view>
      </view>
    </view>
  </PageWrapper>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';
  import PageWrapper from '@/components/PageWrapper.vue';
  import { baguaList, houtianPositions } from '@/utils/bagua';

  // 属性列表
  const attributeList = ref([
    { key: 'phenomena', name: '天象' },
    { key: 'geography', name: '地理' },
    { key: 'character', name: '人物' },
    { key: 'personality', name: '人事' },
    { key: 'soma', name: '身体' },
  ]);

  // 当前选中的属性
  const selectedAttribute = ref('phenomena');

  // 选择属性
  const selectAttribute = (attributeKey: string) => {
    selectedAttribute.value = attributeKey;
  };

  // 中文数字转换为阿拉伯数字
  const chineseToNumber = (chinese: string): number => {
    const map: Record<string, number> = {
      '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
      '六': 6, '七': 7, '八': 8, '九': 9
    };
    return map[chinese] ?? 999;
  };

  // 按照后天八卦顺序排列的八卦列表
  const sortedBaguaList = computed(() => {
    return baguaList.sort((a, b) => {
      const aSequence = houtianPositions[a.name as keyof typeof houtianPositions]?.sequence ?? '999';
      const bSequence = houtianPositions[b.name as keyof typeof houtianPositions]?.sequence ?? '999';
      return chineseToNumber(aSequence) - chineseToNumber(bSequence);
    });
  });

</script>

<style lang="scss" scoped>
  .detail-page {
    min-height: 100vh;
    padding: 30rpx;
  }

  .attribute-section {
    margin-bottom: 40rpx;

    .layout-tabs {
      display: flex;
      justify-content: center;
      gap: 0;
      background: transparent;
      border-radius: 0;
      padding: 12rpx 2rpx 6rpx 2rpx;
      box-shadow: none;
      border: none;

      .tab-item {
        flex: 1;
        padding: 8rpx 24rpx;
        text-align: center;
        font-size: 22rpx;
        font-weight: 500;
        background: #f8f9fa;
        color: #6c757d;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid #865404;

        &:first-child {
          border-radius: 12rpx 0 0 12rpx;
        }

        &:last-child {
          border-radius: 0 12rpx 12rpx 0;
        }

        &:not(:first-child) {
          border-left: none;
        }

        &.active {
          background: #865404;
          color: white;
        }

        &:not(.active):hover {
          background: #e9ecef;
        }

        &:active {
          transform: scale(0.98);
        }
      }
    }
  }

  .content-section {
    .bagua-list {
      display: flex;
      flex-direction: column;
      gap: 12rpx;
    }

    .bagua-item {
      display: flex;
      align-items: center;
      background: #ffffff;
      border-radius: 8rpx;
      padding: 16rpx 20rpx;
      box-shadow: 0 2rpx 6rpx rgba(139, 69, 19, 0.04);
      border: 1rpx solid rgba(139, 69, 19, 0.06);
      transition: all 0.2s ease;

      &:hover {
        box-shadow: 0 3rpx 8rpx rgba(139, 69, 19, 0.08);
        border-color: rgba(139, 69, 19, 0.12);
      }

      .bagua-info {
        display: flex;
        align-items: center;
        gap: 12rpx;
        flex-shrink: 0;

        .bagua-symbol {
          font-size: 26rpx;
          color: rgba(139, 69, 19, 0.9);
          font-weight: bold;
          width: 32rpx;
          text-align: center;
        }

        .bagua-name {
          font-size: 24rpx;
          font-weight: 600;
          color: rgba(139, 69, 19, 0.8);
          width: 40rpx;
        }
      }

      .bagua-content {
        flex: 1;
        font-size: 24rpx;
        color: #666;
        line-height: 1.5;
        padding-left: 20rpx;
        word-break: break-all;
      }
    }
  }
</style>
