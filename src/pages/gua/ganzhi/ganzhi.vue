<template>
  <PageWrapper title="天干地支" :show-back="true">
    <view class="container">
      <!-- 天干地支Tab切换 -->
      <view class="ganzhi-tabs">
        <view class="layout-tabs">
          <view
            :class="['tab-item', { active: currentTab === 'tiangan' }]"
            @tap="switchTab('tiangan')"
          >
            十天干
          </view>
          <view
            :class="['tab-item', { active: currentTab === 'dizhi' }]"
            @tap="switchTab('dizhi')"
          >
            十二地支
          </view>
          <view
            :class="['tab-item', { active: currentTab === 'jiazi' }]"
            @tap="switchTab('jiazi')"
          >
            六十甲子
          </view>
          <view
            :class="['tab-item', { active: currentTab === 'shichen' }]"
            @tap="switchTab('shichen')"
          >
            十二时辰
          </view>
        </view>
      </view>

      <!-- Tab内容区域 -->
      <view class="tab-content">
        <!-- 十天干内容 -->
        <view v-if="currentTab === 'tiangan'" class="content-section">
          <view class="tiangan-list">
            <view
              v-for="tiangan in tianganList"
              :key="tiangan.id"
              class="tiangan-item"
            >
              <view class="item-header">
                <view class="item-name">{{ tiangan.name }}</view>
                <view class="item-attrs">
                  <span class="attr-value wuxing" :class="tiangan.wuxing">{{ tiangan.wuxing }}</span>
                  <span class="attr-value yinyang" :class="tiangan.yinyang">{{ tiangan.yinyang }}</span>
                  <span class="attr-value season" :class="tiangan.season">{{ tiangan.season }}</span>
                  <span class="attr-value direction" :class="tiangan.direction">{{ tiangan.direction }}</span>
                  <span class="attr-value color" :class="getColorClass(tiangan.color)">{{ tiangan.color }}</span>
                </view>
              </view>
              <view class="item-description">
                {{ tiangan.description }}
              </view>
            </view>
          </view>
        </view>

        <!-- 十二地支内容 -->
        <view v-if="currentTab === 'dizhi'" class="content-section">
          <view class="dizhi-list">
            <view
              v-for="dizhi in dizhiList"
              :key="dizhi.id"
              class="dizhi-item"
            >
              <view class="item-header">
                <view class="item-name">{{ dizhi.name }}</view>
                <view class="zodiac-emoji">{{ dizhi.icon }}</view>
                <view class="item-attrs">
                  <span class="attr-value wuxing" :class="dizhi.wuxing">{{ dizhi.wuxing }}</span>
                  <span class="attr-value yinyang" :class="dizhi.yinyang">{{ dizhi.yinyang }}</span>
                  <span class="attr-value season" :class="dizhi.season">{{ dizhi.season }}</span>
                  <span class="attr-value direction" :class="dizhi.direction">{{ dizhi.direction }}</span>
                </view>
              </view>
              <view class="item-info">
                <view class="info-row">
                  <span class="info-label">生肖：</span>
                  <span class="info-value">{{ dizhi.zodiac }}</span>
                </view>
                <view class="info-row">
                  <span class="info-label">时辰：</span>
                  <span class="info-value">{{ dizhi.shichen }}</span>
                </view>
                <view class="info-row">
                  <span class="info-label">月份：</span>
                  <span class="info-value">农历{{ dizhi.month }}月</span>
                </view>
              </view>
              <view class="item-description">
                {{ dizhi.description }}
              </view>
            </view>
          </view>
        </view>

        <!-- 六十甲子内容 -->
        <view v-if="currentTab === 'jiazi'" class="content-section">

          <!-- 甲子表格 - 纳音配对布局 -->
          <view class="jiazi-nayin-table">
            <view class="table-header">
              <view class="header-cell nayin-cell">纳音五行</view>
              <view class="header-cell pair-cell">年号</view>
              <view class="header-cell year-cell">对应年份</view>
            </view>

            <view class="table-body">
              <view
                v-for="(pair, index) in nayinPairs"
                :key="index"
                class="nayin-row"
              >
                <view class="cell nayin-cell">
                  <span class="nayin-text">{{ pair.nayin }}</span>
                </view>
                <view class="cell pair-cell">
                  <view class="ganzhi-pair">
                    <view class="ganzhi-item first">
                      <span class="ganzhi-name" :class="pair.first.tiangan.wuxing">{{ pair.first.name }}</span>
                    </view>
                    <view class="ganzhi-item second">
                      <span class="ganzhi-name" :class="pair.second.tiangan.wuxing">{{ pair.second.name }}</span>
                    </view>
                  </view>
                </view>
                <view class="cell year-cell">
                  <div class="year-group">
                    <div class="year-row">
                      <span class="year-text">{{ getYearFromGanzhi(pair.first, 0) }}</span>
                      <span class="year-text">{{ getYearFromGanzhi(pair.first, 1) }}</span>
                      <span class="year-text">{{ getYearFromGanzhi(pair.first, 2) }}</span>
                    </div>
                    <div class="year-row">
                      <span class="year-text">{{ getYearFromGanzhi(pair.second, 0) }}</span>
                      <span class="year-text">{{ getYearFromGanzhi(pair.second, 1) }}</span>
                      <span class="year-text">{{ getYearFromGanzhi(pair.second, 2) }}</span>
                    </div>
                  </div>
                </view>
              </view>
            </view>
          </view>

          <!-- 学习提示 -->
          <view class="learning-tips">
            <view class="tip-title">📚 学习要点</view>
            <view class="tip-content">
              <view class="tip-item">• 每两个相邻干支对应一个纳音五行</view>
              <view class="tip-item">• 天干十个，地支十二个，最小公倍数为六十</view>
              <view class="tip-item">• 相同颜色标记的为同一纳音组合</view>
            </view>
          </view>
        </view>

        <!-- 十二时辰内容 -->
        <view v-if="currentTab === 'shichen'" class="content-section">

          <view class="shichen-detail-list">
            <view
              v-for="dizhi in dizhiList"
              :key="dizhi.id"
              class="shichen-detail-item"
              :class="{ current: currentDizhi?.id === dizhi.id }"
            >
              <view class="shichen-header">
                <view class="shichen-name">{{ dizhi.name }}时</view>
                <view class="shichen-zodiac">{{ dizhi.icon }} {{ dizhi.zodiac }}</view>
                <view class="shichen-time">{{ dizhi.shichen }}</view>
              </view>

              <view class="shichen-description">
                {{ dizhi.description }}
              </view>
            </view>
          </view>

          <!-- 时辰知识 -->
          <view class="shichen-knowledge">
            <view class="knowledge-title">📖 时辰知识</view>
            <view class="knowledge-content">
              <view class="knowledge-item">• 古代一昼夜分为十二时辰，每时辰相当于现在的两小时</view>
              <view class="knowledge-item">• 时辰以地支命名，从子时开始，依次为子丑寅卯辰巳午未申酉戌亥</view>
              <view class="knowledge-item">• 每个时辰都有对应的生肖、五行、方位等属性</view>
              <view class="knowledge-item">• 子时（23:00-01:00）为一天的开始，称为"夜半"</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </PageWrapper>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, onUnmounted } from 'vue';
  import {
    getCurrentDizhi,
    getYearGanzhi,
    tianganList,
    dizhiList,
    generateJiaziTable,
    type Dizhi,
    type Ganzhi
  } from '@/utils/ganzhi';
  import PageWrapper from '@/components/PageWrapper.vue';

  // 响应式数据
  const currentTime = ref('');
  const currentDizhi = ref<Dizhi | null>(null);
  const currentYear = ref<Ganzhi | null>(null);
  const currentTab = ref('tiangan');
  let timeInterval: ReturnType<typeof setInterval> | null = null;

  // 六十甲子数据
  const jiaziTable = generateJiaziTable();

  // 纳音配对数据 - 每两个甲子为一对，共30对
  const nayinPairs = computed(() => {
    const pairs = [];
    for (let i = 0; i < jiaziTable.length; i += 2) {
      pairs.push({
        first: jiaziTable[i],
        second: jiaziTable[i + 1],
        nayin: jiaziTable[i].nayin
      });
    }
    return pairs;
  });

  // 更新当前时间信息
  const updateCurrentTime = () => {
    const now = new Date();
    const hour = now.getHours();
    const minute = now.getMinutes();
    const year = now.getFullYear();

    // 格式化时间显示
    currentTime.value = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;

    // 获取当前时辰
    currentDizhi.value = getCurrentDizhi(hour);

    // 获取当前年份干支
    currentYear.value = getYearGanzhi(year);
  };

  // 切换tab
  const switchTab = (tab: string) => {
    currentTab.value = tab;
  };



  // 获取颜色对应的CSS类名
  const getColorClass = (color: string): string => {
    const colorMap: { [key: string]: string } = {
      '青绿': 'color-qinglv',
      '翠绿': 'color-cuilv',
      '红': 'color-hong',
      '橙红': 'color-chenghong',
      '黄': 'color-huang',
      '黄褐': 'color-huanghe',
      '白': 'color-bai',
      '银白': 'color-yinbai',
      '黑': 'color-hei',
      '深蓝': 'color-shenlan'
    };
    return colorMap[color] || '';
  };

  // 根据甲子获取对应年份（以1924年甲子为基准）
  const getYearFromGanzhi = (ganzhi: any, yearIndex: number = 0): string => {
    // 1924年是甲子年，作为基准年
    const baseYear = 1924;
    const ganzhiId = ganzhi.id;

    // 计算对应年份，yearIndex: 0=1864-1923, 1=1924-1983, 2=1984-2043
    const baseYearForGanzhi = baseYear + (ganzhiId - 1);
    const targetYear = baseYearForGanzhi + (yearIndex - 1) * 60;

    return targetYear.toString();
  };



  onMounted(() => {
    updateCurrentTime();
    // 每分钟更新一次时间
    timeInterval = setInterval(updateCurrentTime, 60000);
  });

  onUnmounted(() => {
    if (timeInterval) {
      clearInterval(timeInterval);
    }
  });
</script>

<style lang="scss" scoped>
  .container {
    padding: 30rpx;
    background: #f8f4e9;
  }

  // 天干地支Tab样式
  .ganzhi-tabs {
    margin-bottom: 40rpx;

    .layout-tabs {
      display: flex;
      justify-content: center;
      gap: 0;
      background: transparent;
      border-radius: 0;
      padding: 12rpx 2rpx 6rpx 2rpx;
      box-shadow: none;
      border: none;

      .tab-item {
        flex: 1;
        padding: 12rpx 16rpx;
        text-align: center;
        font-size: 24rpx;
        font-weight: 500;
        background: #f8f9fa;
        color: #6c757d;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid #865404;
        border-right: none; // 移除右边框，避免叠加

        &:first-child {
          border-radius: 12rpx 0 0 12rpx;
        }

        &:last-child {
          border-radius: 0 12rpx 12rpx 0;
          border-right: 1px solid #865404; // 最后一个恢复右边框
        }

        &.active {
          background: #865404;
          color: white;
        }

        &:not(.active):hover {
          background: #e9ecef;
        }

        &:active {
          transform: scale(0.98);
        }
      }
    }
  }

  // Tab内容区域
  .tab-content {
    .content-section {
      animation: fadeIn 0.3s ease-in-out;
    }

    .section-header {
      text-align: center;
      margin-bottom: 32rpx;

      .section-title {
        font-size: 36rpx;
        font-weight: 700;
        color: #6d4c1a;
        margin-bottom: 8rpx;
      }

      .section-subtitle {
        font-size: 24rpx;
        color: #8b4513;
        letter-spacing: 2rpx;
      }
    }
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(20rpx); }
    to { opacity: 1; transform: translateY(0); }
  }

  // 十天干样式
  .tiangan-list {
    display: flex;
    flex-direction: column;
    gap: 12rpx;
    margin-bottom: 24rpx;
  }

  .tiangan-item {
    background: white;
    border-radius: 12rpx;
    padding: 20rpx 24rpx;
    box-shadow: 0 2rpx 8rpx rgba(139, 69, 19, 0.06);
    border: 2rpx solid rgba(139, 69, 19, 0.03);

    .item-header {
      display: flex;
      align-items: center;
      gap: 24rpx;
      margin-bottom: 12rpx;

      .item-name {
        font-size: 32rpx;
        font-weight: 700;
        color: #6d4c1a;
        min-width: 60rpx;
        text-align: center;
      }

      .item-attrs {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 20rpx;
        flex-wrap: wrap;

        .attr-value {
          font-size: 18rpx;
          font-weight: 500;
          padding: 4rpx 12rpx;
          border-radius: 6rpx;
          border: 1rpx solid rgba(139, 69, 19, 0.1);

          // 五行样式 - 柔和的颜色
          &.wuxing {
            &.木 {
              color: #2e7d32;
              background: rgba(76, 175, 80, 0.1);
              border-color: rgba(76, 175, 80, 0.2);
            }
            &.火 {
              color: #c62828;
              background: rgba(244, 67, 54, 0.1);
              border-color: rgba(244, 67, 54, 0.2);
            }
            &.土 {
              color: #ef6c00;
              background: rgba(255, 152, 0, 0.1);
              border-color: rgba(255, 152, 0, 0.2);
            }
            &.金 {
              color: #f57c00;
              background: rgba(255, 193, 7, 0.1);
              border-color: rgba(255, 193, 7, 0.2);
            }
            &.水 {
              color: #1565c0;
              background: rgba(33, 150, 243, 0.1);
              border-color: rgba(33, 150, 243, 0.2);
            }
          }

          // 阴阳样式 - 柔和的对比
          &.yinyang {
            &.阳 {
              color: #e65100;
              background: rgba(255, 111, 0, 0.1);
              border-color: rgba(255, 111, 0, 0.2);
            }
            &.阴 {
              color: #1565c0;
              background: rgba(21, 101, 192, 0.1);
              border-color: rgba(21, 101, 192, 0.2);
            }
          }

          // 季节样式 - 柔和的季节色彩
          &.season {
            &.春 {
              color: #558b2f;
              background: rgba(139, 195, 74, 0.1);
              border-color: rgba(139, 195, 74, 0.2);
            }
            &.夏 {
              color: #d84315;
              background: rgba(255, 87, 34, 0.1);
              border-color: rgba(255, 87, 34, 0.2);
            }
            &.长夏 {
              color: #f57c00;
              background: rgba(255, 167, 38, 0.1);
              border-color: rgba(255, 167, 38, 0.2);
            }
            &.秋 {
              color: #ef6c00;
              background: rgba(255, 152, 0, 0.1);
              border-color: rgba(255, 152, 0, 0.2);
            }
            &.冬 {
              color: #455a64;
              background: rgba(96, 125, 139, 0.1);
              border-color: rgba(96, 125, 139, 0.2);
            }
          }

          // 方位样式 - 柔和的方位色彩
          &.direction {
            &.东 {
              color: #2e7d32;
              background: rgba(76, 175, 80, 0.1);
              border-color: rgba(76, 175, 80, 0.2);
            }
            &.南 {
              color: #c62828;
              background: rgba(244, 67, 54, 0.1);
              border-color: rgba(244, 67, 54, 0.2);
            }
            &.中 {
              color: #ef6c00;
              background: rgba(255, 152, 0, 0.1);
              border-color: rgba(255, 152, 0, 0.2);
            }
            &.西 {
              color: #f57c00;
              background: rgba(255, 193, 7, 0.1);
              border-color: rgba(255, 193, 7, 0.2);
            }
            &.北 {
              color: #1565c0;
              background: rgba(33, 150, 243, 0.1);
              border-color: rgba(33, 150, 243, 0.2);
            }
          }

          // 颜色样式 - 柔和的实际颜色
          &.color {
            &.color-qinglv {
              color: #2e7d32;
              background: rgba(76, 175, 80, 0.1);
              border-color: rgba(76, 175, 80, 0.2);
            }
            &.color-cuilv {
              color: #1b5e20;
              background: rgba(46, 125, 50, 0.1);
              border-color: rgba(46, 125, 50, 0.2);
            }
            &.color-hong {
              color: #c62828;
              background: rgba(244, 67, 54, 0.1);
              border-color: rgba(244, 67, 54, 0.2);
            }
            &.color-chenghong {
              color: #d84315;
              background: rgba(255, 87, 34, 0.1);
              border-color: rgba(255, 87, 34, 0.2);
            }
            &.color-huang {
              color: #f57c00;
              background: rgba(255, 235, 59, 0.1);
              border-color: rgba(255, 235, 59, 0.2);
            }
            &.color-huanghe {
              color: #5d4037;
              background: rgba(141, 110, 99, 0.1);
              border-color: rgba(141, 110, 99, 0.2);
            }
            &.color-bai {
              color: #424242;
              background: rgba(250, 250, 250, 0.8);
              border-color: rgba(224, 224, 224, 0.5);
            }
            &.color-yinbai {
              color: #546e7a;
              background: rgba(236, 239, 241, 0.8);
              border-color: rgba(176, 190, 197, 0.3);
            }
            &.color-hei {
              color: #212121;
              background: rgba(66, 66, 66, 0.1);
              border-color: rgba(66, 66, 66, 0.2);
            }
            &.color-shenlan {
              color: #0d47a1;
              background: rgba(21, 101, 192, 0.1);
              border-color: rgba(21, 101, 192, 0.2);
            }
          }
        }
      }
    }

    .item-description {
      font-size: 24rpx;
      color: #8b4513;
      line-height: 1.5;
      padding-left: 84rpx;
      opacity: 0.9;
    }
  }

  // 十二地支样式
  .dizhi-list {
    display: flex;
    flex-direction: column;
    gap: 12rpx;
    margin-bottom: 24rpx;
  }

  .dizhi-item {
    background: white;
    border-radius: 12rpx;
    padding: 20rpx 24rpx;
    box-shadow: 0 2rpx 8rpx rgba(139, 69, 19, 0.06);
    border: 2rpx solid rgba(139, 69, 19, 0.03);

    .item-header {
      display: flex;
      align-items: center;
      gap: 16rpx;
      margin-bottom: 12rpx;

      .item-name {
        font-size: 32rpx;
        font-weight: 700;
        color: #6d4c1a;
        min-width: 60rpx;
        text-align: center;
      }

      .zodiac-emoji {
        font-size: 28rpx;
        min-width: 40rpx;
      }

      .item-attrs {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 12rpx;
        flex-wrap: wrap;

        .attr-value {
          font-size: 18rpx;
          font-weight: 500;
          padding: 4rpx 12rpx;
          border-radius: 6rpx;
          border: 1rpx solid rgba(139, 69, 19, 0.1);

          // 复用天干的样式
          &.wuxing {
            &.木 {
              color: #2e7d32;
              background: rgba(76, 175, 80, 0.1);
              border-color: rgba(76, 175, 80, 0.2);
            }
            &.火 {
              color: #c62828;
              background: rgba(244, 67, 54, 0.1);
              border-color: rgba(244, 67, 54, 0.2);
            }
            &.土 {
              color: #ef6c00;
              background: rgba(255, 152, 0, 0.1);
              border-color: rgba(255, 152, 0, 0.2);
            }
            &.金 {
              color: #f57c00;
              background: rgba(255, 193, 7, 0.1);
              border-color: rgba(255, 193, 7, 0.2);
            }
            &.水 {
              color: #1565c0;
              background: rgba(33, 150, 243, 0.1);
              border-color: rgba(33, 150, 243, 0.2);
            }
          }

          &.yinyang {
            &.阳 {
              color: #e65100;
              background: rgba(255, 111, 0, 0.1);
              border-color: rgba(255, 111, 0, 0.2);
            }
            &.阴 {
              color: #1565c0;
              background: rgba(21, 101, 192, 0.1);
              border-color: rgba(21, 101, 192, 0.2);
            }
          }

          &.season {
            &.春 {
              color: #558b2f;
              background: rgba(139, 195, 74, 0.1);
              border-color: rgba(139, 195, 74, 0.2);
            }
            &.夏 {
              color: #d84315;
              background: rgba(255, 87, 34, 0.1);
              border-color: rgba(255, 87, 34, 0.2);
            }
            &.长夏 {
              color: #f57c00;
              background: rgba(255, 167, 38, 0.1);
              border-color: rgba(255, 167, 38, 0.2);
            }
            &.秋 {
              color: #ef6c00;
              background: rgba(255, 152, 0, 0.1);
              border-color: rgba(255, 152, 0, 0.2);
            }
            &.冬 {
              color: #455a64;
              background: rgba(96, 125, 139, 0.1);
              border-color: rgba(96, 125, 139, 0.2);
            }
          }

          &.direction {
            &.东 {
              color: #2e7d32;
              background: rgba(76, 175, 80, 0.1);
              border-color: rgba(76, 175, 80, 0.2);
            }
            &.南 {
              color: #c62828;
              background: rgba(244, 67, 54, 0.1);
              border-color: rgba(244, 67, 54, 0.2);
            }
            &.中 {
              color: #ef6c00;
              background: rgba(255, 152, 0, 0.1);
              border-color: rgba(255, 152, 0, 0.2);
            }
            &.西 {
              color: #f57c00;
              background: rgba(255, 193, 7, 0.1);
              border-color: rgba(255, 193, 7, 0.2);
            }
            &.北 {
              color: #1565c0;
              background: rgba(33, 150, 243, 0.1);
              border-color: rgba(33, 150, 243, 0.2);
            }
            &.东北 {
              color: #558b2f;
              background: rgba(139, 195, 74, 0.1);
              border-color: rgba(139, 195, 74, 0.2);
            }
            &.东南 {
              color: #d84315;
              background: rgba(255, 87, 34, 0.1);
              border-color: rgba(255, 87, 34, 0.2);
            }
            &.西南 {
              color: #f57c00;
              background: rgba(255, 167, 38, 0.1);
              border-color: rgba(255, 167, 38, 0.2);
            }
            &.西北 {
              color: #455a64;
              background: rgba(96, 125, 139, 0.1);
              border-color: rgba(96, 125, 139, 0.2);
            }
          }
        }
      }
    }

    .item-info {
      display: flex;
      gap: 24rpx;
      margin-bottom: 12rpx;
      padding-left: 68rpx;
      flex-wrap: wrap;

      .info-row {
        display: flex;
        align-items: center;
        gap: 4rpx;

        .info-label {
          font-size: 20rpx;
          color: #a97c50;
          min-width: 60rpx;
        }

        .info-value {
          font-size: 20rpx;
          color: #6d4c1a;
          font-weight: 500;
        }
      }
    }

    .item-description {
      font-size: 24rpx;
      color: #8b4513;
      line-height: 1.5;
      padding-left: 68rpx;
      opacity: 0.9;
    }
  }

  // 六十甲子纳音配对表格样式
  .jiazi-nayin-table {
    background: white;
    border-radius: 12rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.06);
    margin-bottom: 24rpx;

    .table-header {
      display: grid;
      grid-template-columns: 1.5fr 1fr 2.5fr;
      background: rgba(139, 69, 19, 0.03);
      border-bottom: 2rpx solid rgba(139, 69, 19, 0.1);

      .header-cell {
        padding: 16rpx 12rpx;
        font-size: 22rpx;
        font-weight: 600;
        color: #6d4c1a;
        text-align: center;
        border-right: 1rpx solid rgba(255, 255, 255, 0.2);

        &:last-child {
          border-right: none;
        }
      }
    }

    .table-body {
      .nayin-row {
        display: grid;
        grid-template-columns: 1.5fr 1fr 2.5fr;
        border-bottom: 1rpx solid rgba(139, 69, 19, 0.05);
        transition: background-color 0.2s ease;

        &:hover {
          background: rgba(139, 69, 19, 0.02);
        }

        &:nth-child(odd) {
          background: rgba(139, 69, 19, 0.01);
        }

        &:last-child {
          border-bottom: none;
        }

        .cell {
          padding: 12rpx 12rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          border-right: 1rpx solid rgba(139, 69, 19, 0.05);

          &:last-child {
            border-right: none;
          }

          &.pair-cell {
            .ganzhi-pair {
              display: flex;
              flex-direction: column;
              gap: 12rpx;
              width: 100%;

              .ganzhi-item {
                display: flex;
                align-items: center;
                justify-content: center;

                .ganzhi-name {
                  font-size: 22rpx;
                  font-weight: 500;

                  &.木 {
                    color: #2e7d32;
                    border-color: rgba(76, 175, 80, 0.3);
                    background: rgba(76, 175, 80, 0.05);
                  }
                  &.火 {
                    color: #c62828;
                    border-color: rgba(244, 67, 54, 0.3);
                    background: rgba(244, 67, 54, 0.05);
                  }
                  &.土 {
                    color: #ef6c00;
                    border-color: rgba(255, 152, 0, 0.3);
                    background: rgba(255, 152, 0, 0.05);
                  }
                  &.金 {
                    color: #f57c00;
                    border-color: rgba(255, 193, 7, 0.3);
                    background: rgba(255, 193, 7, 0.05);
                  }
                  &.水 {
                    color: #1565c0;
                    border-color: rgba(33, 150, 243, 0.3);
                    background: rgba(33, 150, 243, 0.05);
                  }
                }


              }
            }
          }

          &.nayin-cell {
            .nayin-text {
              font-size: 22rpx;
              font-weight: 500;
              color: #865404;
              text-align: center;
              padding: 8rpx 12rpx;
              border-radius: 8rpx;
              background: rgba(212, 175, 55, 0.1);
              border: 1rpx solid rgba(212, 175, 55, 0.2);
            }
          }

          &.year-cell {
            .year-group {
              display: flex;
              flex-direction: column;
              gap: 8rpx;
              width: 100%;

              .year-row {
                display: flex;
                gap: 8rpx;
                justify-content: center;
                flex-wrap: wrap;

                .year-text {
                  font-size: 18rpx;
                  font-weight: 600;
                  color: #6d4c1a;
                  padding: 4rpx 8rpx;
                  border-radius: 6rpx;
                  background: rgba(139, 69, 19, 0.05);
                  border: 1rpx solid rgba(139, 69, 19, 0.1);
                  text-align: center;
                  min-width: 70rpx;
                }
              }
            }
          }
        }
      }
    }
  }

  // 学习提示样式
  .learning-tips {
    background: white;
    border-radius: 12rpx;
    padding: 24rpx;
    box-shadow: 0 2rpx 8rpx rgba(139, 69, 19, 0.06);
    border-left: 4rpx solid #d4af37;

    .tip-title {
      font-size: 26rpx;
      font-weight: 600;
      color: #6d4c1a;
      margin-bottom: 16rpx;
    }

    .tip-content {
      .tip-item {
        font-size: 22rpx;
        color: #8b4513;
        line-height: 1.6;
        margin-bottom: 8rpx;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  // 十二时辰样式
  .current-time-info {
    background: white;
    border-radius: 16rpx;
    padding: 24rpx;
    text-align: center;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.06);

    .time-display {
      font-size: 48rpx;
      font-weight: 700;
      color: #8b4513;
      font-family: 'Courier New', monospace;
      margin-bottom: 8rpx;
    }

    .current-shichen {
      font-size: 24rpx;
      color: #a97c50;
    }
  }

  .shichen-detail-list {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
    margin-bottom: 24rpx;

    .shichen-detail-item {
      background: white;
      border-radius: 12rpx;
      padding: 20rpx 24rpx;
      box-shadow: 0 2rpx 8rpx rgba(139, 69, 19, 0.06);
      border: 2rpx solid rgba(139, 69, 19, 0.03);

      &.current {
        border-color: #8b4513;
        background: #fefcf8;
        box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.1);
      }

      .shichen-header {
        display: flex;
        align-items: center;
        gap: 16rpx;
        margin-bottom: 12rpx;
        flex-wrap: wrap;

        .shichen-name {
          font-size: 28rpx;
          font-weight: 700;
          color: #6d4c1a;
          min-width: 80rpx;
        }

        .shichen-zodiac {
          font-size: 22rpx;
          color: #8b4513;
          display: flex;
          align-items: center;
          gap: 4rpx;
        }

        .shichen-time {
          font-size: 22rpx;
          color: #8b4513;
          font-family: 'Courier New', monospace;
          background: rgba(139, 69, 19, 0.05);
          padding: 4rpx 12rpx;
          border-radius: 8rpx;
        }
      }

      .shichen-attrs {
        display: flex;
        gap: 20rpx;
        margin-bottom: 12rpx;
        flex-wrap: wrap;

        .attr-row {
          display: flex;
          align-items: center;
          gap: 4rpx;

          .attr-label {
            font-size: 20rpx;
            color: #a97c50;
            min-width: 60rpx;
          }

          .attr-value {
            font-size: 20rpx;
            font-weight: 500;
            color: #6d4c1a;

            &.wuxing {
              &.木 { color: #2e7d32; }
              &.火 { color: #c62828; }
              &.土 { color: #ef6c00; }
              &.金 { color: #f57c00; }
              &.水 { color: #1565c0; }
            }

            &.yinyang {
              &.阳 { color: #e65100; }
              &.阴 { color: #1565c0; }
            }

            &.direction {
              &.东 { color: #2e7d32; }
              &.南 { color: #c62828; }
              &.中 { color: #ef6c00; }
              &.西 { color: #f57c00; }
              &.北 { color: #1565c0; }
              &.东北 { color: #558b2f; }
              &.东南 { color: #d84315; }
              &.西南 { color: #f57c00; }
              &.西北 { color: #455a64; }
            }
          }
        }
      }

      .shichen-description {
        font-size: 22rpx;
        color: #8b4513;
        line-height: 1.5;
        opacity: 0.9;
        padding-left: 68rpx;
      }
    }
  }

  // 时辰知识样式
  .shichen-knowledge {
    background: white;
    border-radius: 12rpx;
    padding: 24rpx;
    box-shadow: 0 2rpx 8rpx rgba(139, 69, 19, 0.06);
    border-left: 4rpx solid #d4af37;

    .knowledge-title {
      font-size: 26rpx;
      font-weight: 600;
      color: #6d4c1a;
      margin-bottom: 16rpx;
    }

    .knowledge-content {
      .knowledge-item {
        font-size: 22rpx;
        color: #8b4513;
        line-height: 1.6;
        margin-bottom: 8rpx;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }





  // 响应式适配
  @media (max-width: 750rpx) {
    .tiangan-item {
      .item-header {
        gap: 16rpx;

        .item-name {
          min-width: 50rpx;
          font-size: 28rpx;
        }

        .item-attrs {
          gap: 10rpx;

          .attr-value {
            font-size: 20rpx;
            padding: 5rpx 10rpx;
            border-radius: 10rpx;
          }
        }
      }

      .item-description {
        font-size: 22rpx;
        padding-left: 66rpx;
      }
    }

    .dizhi-item {
      .item-header {
        gap: 12rpx;

        .item-name {
          min-width: 50rpx;
          font-size: 28rpx;
        }

        .zodiac-emoji {
          font-size: 24rpx;
          min-width: 32rpx;
        }

        .item-attrs {
          gap: 8rpx;

          .attr-value {
            font-size: 16rpx;
            padding: 3rpx 8rpx;
            border-radius: 4rpx;
          }
        }
      }

      .item-info {
        gap: 16rpx;

        .info-row {
          .info-label {
            font-size: 18rpx;
            min-width: 50rpx;
          }

          .info-value {
            font-size: 18rpx;
          }
        }
      }

      .item-description {
        font-size: 22rpx;
        padding-left: 82rpx;
      }
    }

    .jiazi-nayin-table {
      .table-header {
        grid-template-columns: 1.2fr 2fr 2fr;

        .header-cell {
          padding: 12rpx 8rpx;
          font-size: 20rpx;
        }
      }

      .table-body {
        .nayin-row {
          grid-template-columns: 1.2fr 2fr 2fr;

          .cell {
            padding: 12rpx 8rpx;

            &.nayin-cell {
              .nayin-text {
                font-size: 18rpx;
                padding: 6rpx 8rpx;
              }
            }

            &.pair-cell {
              .ganzhi-pair {
                gap: 6rpx;

                .ganzhi-item {
                  padding: 8rpx 12rpx;

                  .ganzhi-name {
                    font-size: 20rpx;
                  }
                }
              }
            }

            &.year-cell {
              .year-group {
                gap: 6rpx;

                .year-row {
                  gap: 6rpx;

                  .year-text {
                    font-size: 16rpx;
                    padding: 3rpx 6rpx;
                    min-width: 60rpx;
                  }
                }
              }
            }
          }
        }
      }
    }

    .learning-tips {
      padding: 20rpx;

      .tip-title {
        font-size: 24rpx;
      }

      .tip-content {
        .tip-item {
          font-size: 20rpx;
        }
      }
    }

    .shichen-detail-list {
      gap: 12rpx;

      .shichen-detail-item {
        padding: 16rpx 20rpx;

        .shichen-header {
          gap: 12rpx;
          margin-bottom: 10rpx;

          .shichen-name {
            font-size: 24rpx;
            min-width: 70rpx;
          }

          .shichen-zodiac {
            font-size: 20rpx;
          }

          .shichen-time {
            font-size: 18rpx;
            padding: 3rpx 8rpx;
          }
        }

        .shichen-attrs {
          gap: 16rpx;
          margin-bottom: 10rpx;

          .attr-row {
            .attr-label {
              font-size: 18rpx;
              min-width: 50rpx;
            }

            .attr-value {
              font-size: 18rpx;
            }
          }
        }

        .shichen-description {
          font-size: 20rpx;
          padding-left: 54rpx;
        }
      }
    }

    .shichen-knowledge {
      padding: 20rpx;

      .knowledge-title {
        font-size: 24rpx;
      }

      .knowledge-content {
        .knowledge-item {
          font-size: 20rpx;
        }
      }
    }
  }
</style>
