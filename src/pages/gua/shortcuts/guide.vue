<template>
  <PageWrapper title="易经系统学习指南" :show-back="true">
    <view class="container">

      <!-- 体系组成 -->
      <view class="system-section">
        <view class="section-title">
          <view class="title-text">一、易经体系的组成</view>
          <view class="title-line"></view>
        </view>
        <view class="section-description">
          《易经》体系不是一个单一的占卜书，而是一个庞大的、层级分明的哲学与术数宇宙。它主要由以下四个核心层面构成：
        </view>

        <!-- 四个核心层面 -->
        <view class="core-layers">
          <!-- 核心哲学 -->
          <view class="layer-card philosophy">
            <view class="layer-header">
              <view class="layer-icon">🌟</view>
              <view class="layer-title">核心哲学（"道"）</view>
            </view>
            <view class="layer-content">
              <view class="concept-item">
                <view class="concept-title">阴阳学说</view>
                <view class="concept-desc">万物对立统一、互根互用的根本法则。是所有理论的基石。</view>
              </view>
              <view class="concept-item">
                <view class="concept-title">五行学说</view>
                <view class="concept-desc">描述宇宙间五种基本能量（木、火、土、金、水）的生克乘侮关系。是分析万物相互作用和动态平衡的工具。</view>
              </view>
              <view class="concept-item">
                <view class="concept-title">天人合一思想</view>
                <view class="concept-desc">认为天、地、人是一个有机整体，相互感应，规律相通。</view>
              </view>
            </view>
          </view>

          <!-- 符号系统 -->
          <view class="layer-card symbols">
            <view class="layer-header">
              <view class="layer-icon">🔮</view>
              <view class="layer-title">符号系统（"象"）</view>
            </view>
            <view class="layer-content">
              <view class="concept-item">
                <view class="concept-title">八卦</view>
                <view class="concept-desc">乾、坤、震、巽、坎、离、艮、兑。代表八种自然现象和基本属性，是构建所有模型的基本单元。</view>
              </view>
              <view class="concept-item">
                <view class="concept-title">六十四卦</view>
                <view class="concept-desc">由八卦两两相叠而成，代表宇宙间64种典型情境或模型。每个卦有卦名、卦辞、爻辞。</view>
              </view>
              <view class="concept-item">
                <view class="concept-title">天干地支</view>
                <view class="concept-desc">十天干（甲、乙、丙、丁…）和十二地支（子、丑、寅、卯…）。是表示时间、空间、能量状态的符号，用于命理、占卜、风水等具体术数中。</view>
              </view>
            </view>
          </view>

          <!-- 方法论 -->
          <view class="layer-card methodology">
            <view class="layer-header">
              <view class="layer-icon">⚖️</view>
              <view class="layer-title">方法论（"法"）</view>
            </view>
            <view class="layer-content">
              <view class="concept-item">
                <view class="concept-title">象数理占</view>
                <view class="concept-desc">研究《易经》的四种方法，缺一不可。</view>
                <view class="sub-concepts">
                  <view class="sub-concept">象：观察卦象、物象的象征意义</view>
                  <view class="sub-concept">数：计算卦爻的数字推演逻辑（如筮草算法）</view>
                  <view class="sub-concept">理：理解卦爻辞背后的哲学道理和人生智慧</view>
                  <view class="sub-concept">占：应用易经进行预测和实践的方法</view>
                </view>
              </view>
            </view>
          </view>

          <!-- 应用术数 -->
          <view class="layer-card applications">
            <view class="layer-header">
              <view class="layer-icon">🎯</view>
              <view class="layer-title">应用术数（"术"）</view>
            </view>
            <view class="layer-content">
              <view class="concept-desc">这是易经理论在具体领域的应用，分支繁多，主要包括：</view>
              <view class="application-grid">
                <view class="app-item">
                  <view class="app-title">命理类</view>
                  <view class="app-desc">研究人一生运势轨迹。如四柱八字、紫微斗数。</view>
                </view>
                <view class="app-item">
                  <view class="app-title">占卜类</view>
                  <view class="app-desc">针对具体事问进行预测。如六爻预测（纳甲法）、梅花易数、三式（奇门遁甲、大六壬、太乙神数）。</view>
                </view>
                <view class="app-item">
                  <view class="app-title">相术类</view>
                  <view class="app-desc">通过观察外在分析内在。如面相、手相。</view>
                </view>
                <view class="app-item">
                  <view class="app-title">风水类（堪舆）</view>
                  <view class="app-desc">研究环境与人的和谐关系。如阳宅风水、阴宅风水。</view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 学习阶段 -->
      <view class="learning-section">
        <view class="section-title">二、各阶段详细内容与学习成果</view>

        <!-- 学习阶段列表 -->
        <view class="stages-list">
          <!-- 阶段一 -->
          <view class="stage-item">
            <view class="stage-header">
              <view class="stage-number">1</view>
              <view class="stage-title">筑基阶段（明道 · 1-3个月）</view>
            </view>
            <view class="stage-goal">
              <strong>学习目标：</strong>建立正确的易经世界观，掌握最核心的基础概念和符号系统。
            </view>
            <view class="stage-content">
              <view class="content-section">
                <view class="content-title">核心内容:</view>
                <view class="content-list">
                  <view class="content-item"><strong>阴阳学说：</strong>理解阴阳的概念、属性和相互关系。</view>
                  <view class="content-item"><strong>五行学说：</strong>熟练记忆五行的生、克、制、化、乘、侮关系。</view>
                  <view class="content-item"><strong>八卦基础：</strong>背诵先天、后天八卦的名称、卦象、自然与人事象征。</view>
                  <view class="content-item"><strong>天干地支：</strong>背诵十天干和十二地支，熟悉其阴阳、五行、方位属性，学会排四柱。</view>
                </view>
              </view>
              <view class="content-section">
                <view class="content-title">阶段成果:</view>
                <view class="content-list">
                  <view class="result-item">能分析任何事物的阴阳五行属性。</view>
                  <view class="result-item">能默写八卦，并说出其基本象征。</view>
                  <view class="result-item">能正确排出自己的生辰八字。</view>
                  <view class="result-item">建立"变化与平衡"的易经思维。</view>
                </view>
              </view>
            </view>
          </view>

          <!-- 阶段二 -->
          <view class="stage-item">
            <view class="stage-header">
              <view class="stage-number">2</view>
              <view class="stage-title">核心深化阶段（通法 · 3-6个月）</view>
            </view>
            <view class="stage-goal">
              <strong>学习目标：</strong>深入理解六十四卦体系，掌握《易经》的核心方法论。
            </view>
            <view class="stage-content">
              <view class="content-section">
                <view class="content-title">核心内容:</view>
                <view class="content-list">
                  <view class="content-item"><strong>六十四卦：</strong>学习六十四卦的卦序、每卦的卦辞和爻辞。</view>
                  <view class="content-item"><strong>《易传》精读：</strong>精读《彖传》、《象传》、《文言传》、《系辞传》，这是理解卦爻辞的钥匙。</view>
                  <view class="content-item"><strong>卦变理论：</strong>深入学习错、综、互、变等卦变关系，多角度分析卦象。</view>
                </view>
              </view>
              <view class="content-section">
                <view class="content-title">阶段成果:</view>
                <view class="content-list">
                  <view class="result-item">能背诵六十四卦卦名和卦序。</view>
                  <view class="result-item">能结合《易传》对任意一卦进行哲学解读。</view>
                  <view class="result-item">形成"象数理占"一体化的思维模式。</view>
                </view>
              </view>
            </view>
          </view>

          <!-- 阶段三 -->
          <view class="stage-item">
            <view class="stage-header">
              <view class="stage-number">3</view>
              <view class="stage-title">术数专精阶段（精术 · 6-12个月）</view>
            </view>
            <view class="stage-goal">
              <strong>学习目标：</strong>选择1-2门术数进行深入专研，将理论应用于实践。
            </view>
            <view class="stage-content">
              <view class="content-section">
                <view class="content-title">核心内容（选择一门主修）:</view>
                <view class="path-options">
                  <view class="path-option">
                    <view class="path-title">路线A：命理（四柱八字或紫微斗数）</view>
                    <view class="content-list">
                      <view class="content-item"><strong>八字：</strong>排大运流年、十神系统、格局分析（身强身弱、取用神）、实战断事。</view>
                      <view class="content-item"><strong>紫微斗数：</strong>排盘安星、十二宫职体系、星曜性质、四化飞星、命盘解析。</view>
                    </view>
                  </view>
                  <view class="path-option">
                    <view class="path-title">路线B：卜占（六爻预测或梅花易数）</view>
                    <view class="content-list">
                      <view class="content-item"><strong>六爻：</strong>装卦（纳甲、安世应、配六亲六神）、用神选取、旺衰判断、断应期。</view>
                      <view class="content-item"><strong>梅花：</strong>起卦方法、体用生克、三要十应（外应）、占断思路。</view>
                    </view>
                  </view>
                </view>
              </view>
              <view class="content-section">
                <view class="content-title">阶段成果:</view>
                <view class="content-list">
                  <view class="result-item">（命理）能独立完成命盘分析，给出趋势性判断。</view>
                  <view class="result-item">（卜占）能独立完成从起卦到断卦的全过程，对事态发展有清晰逻辑。</view>
                </view>
              </view>
            </view>
          </view>

          <!-- 阶段四 -->
          <view class="stage-item">
            <view class="stage-header">
              <view class="stage-number">4</view>
              <view class="stage-title">高阶融通阶段（化用 · 1-3年或更长）</view>
            </view>
            <view class="stage-goal">
              <strong>学习目标：</strong>攻克"三式"等顶级术数，融会贯通，形成自己的体系。
            </view>
            <view class="stage-content">
              <view class="content-section">
                <view class="content-title">核心内容:</view>
                <view class="content-list">
                  <view class="content-item"><strong>奇门遁甲：</strong>掌握排盘原理（置闰、拆补）、符号系统（三奇六仪、八门、九星、八神）、断局方法论（取用神、分析旺衰）。</view>
                  <view class="content-item"><strong>大六壬：</strong>学习起课（四课三传）、天将神煞系统、课体课传分析。</view>
                  <view class="content-item"><strong>风水：</strong>学习形峦基础、理气派别（如玄空飞星）、实地勘察技巧。</view>
                  <view class="content-item"><strong>跨术数验证：</strong>用八字验证紫微，用奇门验证六爻，体会术数之间的统一性。</view>
                </view>
              </view>
              <view class="content-section">
                <view class="content-title">阶段成果:</view>
                <view class="content-list">
                  <view class="result-item">能运用奇门、大六壬进行复杂决策和深度预测。</view>
                  <view class="result-item">能将风水与其他术数结合，提供综合调整建议。</view>
                  <view class="result-item">达到"善易者不占"的境界，将易经智慧内化为世界观和方法论。</view>
                </view>
              </view>
            </view>
          </view>

        </view>
      </view>

      <!-- 重要提醒 -->
      <view class="reminder-section">
        <view class="section-title">
          <view class="title-text">重要提醒</view>
          <view class="title-line"></view>
        </view>
        <view class="reminder-cards">
          <view class="reminder-card">
            <view class="reminder-icon">⚠️</view>
            <view class="reminder-content">
              <view class="reminder-title">循序渐进</view>
              <view class="reminder-text">严禁跳跃阶段，否则根基不稳，后期必然困惑。</view>
            </view>
          </view>
          <view class="reminder-card">
            <view class="reminder-icon">📚</view>
            <view class="reminder-content">
              <view class="reminder-title">经典为重</view>
              <view class="reminder-text">多读原典和公认的权威注疏，谨慎对待网络上的碎片化信息。</view>
            </view>
          </view>
          <view class="reminder-card">
            <view class="reminder-icon">🔄</view>
            <view class="reminder-content">
              <view class="reminder-title">实践与反思</view>
              <view class="reminder-text">多练习、多复盘、多总结，记录案例，对比反馈。</view>
            </view>
          </view>
          <view class="reminder-card">
            <view class="reminder-icon">🌟</view>
            <view class="reminder-content">
              <view class="reminder-title">德行先行</view>
              <view class="reminder-text">学习易经是为了明理修身，切忌用于炫技或恐吓他人。</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </PageWrapper>
</template>

<script lang="ts">
  import { defineComponent } from 'vue';
  import PageWrapper from '@/components/PageWrapper.vue';

  export default defineComponent({
    name: 'YijingSystemPage',
    components: {
      PageWrapper
    }
  });
</script>

<style lang="scss" scoped>
  .container {
    max-width: 800rpx;
    margin: 0 auto;
    padding: 32rpx 24rpx;
  }

  // 页面标题
  .page-header {
    text-align: center;
    margin-bottom: 48rpx;
    padding-bottom: 32rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .title-wrapper {
      margin-bottom: 16rpx;

      .title {
        font-size: 40rpx;
        font-weight: 600;
        color: #333;
        letter-spacing: 1rpx;
      }

      .title-decoration {
        font-size: 20rpx;
        color: #999;
        margin-left: 8rpx;
      }
    }

    .subtitle {
      font-size: 24rpx;
      color: #666;
      line-height: 1.5;
    }
  }

  // 章节标题
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin: 18rpx 0 32rpx 0;
    padding-bottom: 16rpx;
    border-bottom: 2rpx solid #e8e8e8;
  }

  .section-description {
    font-size: 26rpx;
    color: #555;
    line-height: 1.6;
    margin-bottom: 32rpx;
    padding: 20rpx;
    background: #f8f9fa;
    border-radius: 8rpx;
    border-left: 4rpx solid #8b4513;
  }

  // 核心层面卡片
  .core-layers {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
  }

  .layer-card {
    background: white;
    border-radius: 12rpx;
    padding: 24rpx;
    border: 1rpx solid #e8e8e8;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);

    .layer-header {
      display: flex;
      align-items: center;
      gap: 12rpx;
      margin-bottom: 20rpx;

      .layer-icon {
        font-size: 24rpx;
      }

      .layer-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
      }
    }

    .layer-content {
      .concept-item {
        margin-bottom: 20rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .concept-title {
          font-size: 24rpx;
          font-weight: 600;
          color: #555;
          margin-bottom: 8rpx;
        }

        .concept-desc {
          font-size: 22rpx;
          color: #666;
          line-height: 1.5;
        }

        .sub-concepts {
          margin-left: 16rpx;
          margin-top: 12rpx;

          .sub-concept {
            font-size: 20rpx;
            color: #777;
            line-height: 1.4;
            margin-bottom: 6rpx;
            position: relative;
            padding-left: 12rpx;

            &::before {
              content: '•';
              position: absolute;
              left: 0;
              color: #d4af37;
            }
          }
        }
      }
    }
  }

  // 应用术数网格
  .application-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16rpx;
    margin-top: 16rpx;

    .app-item {
      background: #f8f9fa;
      border-radius: 8rpx;
      padding: 16rpx;
      border: 1rpx solid #e8e8e8;

      .app-title {
        font-size: 22rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 8rpx;
      }

      .app-desc {
        font-size: 20rpx;
        color: #666;
        line-height: 1.4;
      }
    }
  }

  // 学习阶段样式
  .learning-section {
    margin-top: 48rpx;
  }

  .stages-list {
    display: flex;
    flex-direction: column;
    gap: 32rpx;
  }

  .stage-item {
    background: white;
    border-radius: 12rpx;
    padding: 24rpx;
    border: 1rpx solid #e8e8e8;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);

    .stage-header {
      display: flex;
      align-items: center;
      gap: 16rpx;
      margin-bottom: 20rpx;

      .stage-number {
        width: 48rpx;
        height: 48rpx;
        border-radius: 50%;
        background: #8b4513;
        color: white;
        font-size: 24rpx;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
      }

      .stage-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
        line-height: 1.3;
      }
    }

    .stage-goal {
      font-size: 24rpx;
      color: #555;
      line-height: 1.5;
      margin-bottom: 24rpx;
      padding: 16rpx;
      background: #f8f9fa;
      border-radius: 8rpx;
      border-left: 4rpx solid #8b4513;
    }

    .stage-content {
      .content-section {
        margin-bottom: 24rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .content-title {
          font-size: 24rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 16rpx;
        }

        .content-list {
          .content-item {
            font-size: 22rpx;
            color: #666;
            line-height: 1.5;
            margin-bottom: 12rpx;
            padding-left: 16rpx;
            position: relative;

            &::before {
              content: '•';
              position: absolute;
              left: 0;
              color: #d4af37;
            }

            strong {
              color: #333;
            }
          }

          .result-item {
            font-size: 22rpx;
            color: #666;
            line-height: 1.5;
            margin-bottom: 12rpx;
            padding: 12rpx 16rpx;
            background: #fef8f0;
            border-radius: 6rpx;
            border-left: 3rpx solid #d4af37;
            position: relative;
            padding-left: 32rpx;

            &::before {
              content: '✓';
              position: absolute;
              left: 12rpx;
              color: #d4af37;
              font-weight: 600;
            }
          }
        }

        .path-options {
          .path-option {
            margin-bottom: 20rpx;
            padding: 16rpx;
            background: #f8f9fa;
            border-radius: 8rpx;
            border: 1rpx solid #e8e8e8;

            .path-title {
              font-size: 22rpx;
              font-weight: 600;
              color: #333;
              margin-bottom: 12rpx;
            }
          }
        }
      }
    }
  }

  // 提醒部分样式
  .reminder-section {
    margin-top: 48rpx;
  }

  .reminder-cards {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16rpx;

    .reminder-card {
      background: white;
      border-radius: 12rpx;
      padding: 20rpx;
      border: 1rpx solid #e8e8e8;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
      display: flex;
      align-items: flex-start;
      gap: 12rpx;

      .reminder-icon {
        font-size: 24rpx;
        flex-shrink: 0;
      }

      .reminder-content {
        flex: 1;

        .reminder-title {
          font-size: 22rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 8rpx;
        }

        .reminder-text {
          font-size: 20rpx;
          color: #666;
          line-height: 1.4;
        }
      }
    }
  }

  // 响应式适配
  @media (max-width: 750rpx) {
    .container {
      padding: 24rpx 16rpx;
    }

    .page-header {
      margin-bottom: 32rpx;
      padding-bottom: 24rpx;

      .title-wrapper .title {
        font-size: 32rpx;
      }

      .subtitle {
        font-size: 20rpx;
      }
    }

    .section-title {
      font-size: 28rpx;
      margin: 12rpx 0 24rpx 0;
    }

    .section-description {
      font-size: 22rpx;
      padding: 16rpx;
    }

    .layer-card {
      padding: 20rpx;

      .layer-header {
        .layer-title {
          font-size: 24rpx;
        }
      }

      .layer-content {
        .concept-item {
          .concept-title {
            font-size: 20rpx;
          }

          .concept-desc {
            font-size: 18rpx;
          }
        }
      }
    }

    .application-grid {
      grid-template-columns: 1fr;
    }

    .stage-item {
      padding: 20rpx;

      .stage-header {
        .stage-number {
          width: 40rpx;
          height: 40rpx;
          font-size: 20rpx;
        }

        .stage-title {
          font-size: 24rpx;
        }
      }

      .stage-goal {
        font-size: 20rpx;
        padding: 12rpx;
      }

      .stage-content {
        .content-section {
          .content-title {
            font-size: 20rpx;
          }

          .content-list {
            .content-item, .result-item {
              font-size: 18rpx;
            }

            .result-item {
              padding: 8rpx 12rpx;
              padding-left: 28rpx;
            }
          }

          .path-options {
            .path-option {
              padding: 12rpx;

              .path-title {
                font-size: 18rpx;
              }
            }
          }
        }
      }
    }

    .reminder-cards {
      grid-template-columns: 1fr;
      gap: 12rpx;

      .reminder-card {
        padding: 16rpx;

        .reminder-icon {
          font-size: 20rpx;
        }

        .reminder-content {
          .reminder-title {
            font-size: 18rpx;
          }

          .reminder-text {
            font-size: 16rpx;
          }
        }
      }
    }
  }
</style>
