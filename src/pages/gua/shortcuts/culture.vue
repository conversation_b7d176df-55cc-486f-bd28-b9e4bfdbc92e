<template>
  <PageWrapper title="八卦文化" :show-back="true">
    <view class="container">

      <!-- 文化内容区域 -->
      <view class="culture-section">
        <!-- 内容导航 -->
        <view class="layout-tabs">
          <view
            v-for="(item, index) in cultureItems"
            :key="index"
            :class="['tab-item', { active: currentCultureIndex === index }]"
            @tap="switchCultureContent(index)"
          >
            {{ item.title }}
          </view>
        </view>

        <!-- 内容展示区域 -->
        <view class="culture-content">
          <!-- 动态内容区域 -->
          <view class="content-body">
            <!-- 八卦起源故事 -->
            <view v-if="currentCultureIndex === 0" class="origin-content">
              <view v-for="(story, index) in originStories" :key="index" class="story-item">
                <view class="story-title">{{ story.title }}</view>
                <view class="story-content">{{ story.content }}</view>
                <view v-if="story.quote" class="story-quote">{{ story.quote }}</view>
              </view>
            </view>

            <!-- 历代易学大师 -->
            <view v-if="currentCultureIndex === 1" class="masters-content">
              <view v-for="(master, index) in easyMasters" :key="index" class="master-item">
                <view class="master-header">
                  <view class="master-name">{{ master.name }}</view>
                  <view class="master-period">{{ master.period }}</view>
                </view>
                <view class="master-title">{{ master.title }}</view>
                <view class="master-contribution">{{ master.contribution }}</view>
                <view v-if="master.works" class="master-works">
                  <text class="works-label">主要著作：</text>
                  <text>{{ master.works }}</text>
                </view>
              </view>
            </view>

            <!-- 不同流派解释 -->
            <view v-if="currentCultureIndex === 2" class="schools-content">
              <view v-for="(school, index) in easySchools" :key="index" class="school-item">
                <view class="school-header">
                  <view class="school-name">{{ school.name }}</view>
                  <view class="school-period">{{ school.period }}</view>
                </view>
                <view class="school-core">
                  <text class="core-label">核心观点：</text>
                  <text>{{ school.core }}</text>
                </view>
                <view class="school-features">
                  <text class="features-label">主要特点：</text>
                  <text>{{ school.features }}</text>
                </view>
                <view v-if="school.representatives" class="school-representatives">
                  <text class="representatives-label">代表人物：</text>
                  <text>{{ school.representatives }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </PageWrapper>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import PageWrapper from '@/components/PageWrapper.vue';

  // 文化内容相关状态
  const currentCultureIndex = ref(0);

  // 文化内容导航项
  const cultureItems = ref([
    {
      icon: '📜',
      title: '八卦起源',
      subtitle: '探寻八卦的神话传说与历史起源'
    },
    {
      icon: '👨‍🏫',
      title: '历代大师',
      subtitle: '了解易学发展史上的重要人物'
    },
    {
      icon: '🏛️',
      title: '流派差异',
      subtitle: '比较不同易学流派的观点差异'
    }
  ]);

  // 八卦起源故事数据
  const originStories = ref([
    {
      title: '伏羲画卦',
      content: '相传远古时期，伏羲氏仰观天象，俯察地理，观鸟兽之文与地之宜，近取诸身，远取诸物，始作八卦，以通神明之德，以类万物之情。伏羲在黄河边见到龙马负图而出，图上有奇异的符号，伏羲据此创造了八卦。',
      quote: '《易传·系辞下》："古者包牺氏之王天下也，仰则观象于天，俯则观法于地，观鸟兽之文与地之宜，近取诸身，远取诸物，于是始作八卦，以通神明之德，以类万物之情。"'
    },
    {
      title: '河图洛书',
      content: '河图洛书是八卦起源的重要传说。河图出于黄河，洛书出于洛水。河图以十数合五方，五行，阴阳，天地之象。洛书以九数合四时，八节，五行，阴阳之数。这两个神秘图案被认为是八卦和《易经》的源头。',
      quote: '《尚书·顾命》："河图在东序。"'
    },
    {
      title: '神农重卦',
      content: '传说神农氏时期，将伏羲的八卦重叠组合，形成了六十四卦。神农氏根据农业生产的需要，进一步发展了八卦理论，使其更加完善和实用。',
      quote: null
    },
    {
      title: '文王演易',
      content: '周文王被囚羑里时，潜心研究八卦，将其发展为六十四卦，并为每卦写下卦辞。文王将八卦的哲学思想进一步深化，形成了完整的易学体系。',
      quote: '《史记·周本纪》："西伯盖即位五十年。其囚羑里，盖益易之八卦为六十四卦。"'
    }
  ]);

  // 历代易学大师数据
  const easyMasters = ref([
    {
      name: '伏羲',
      period: '远古时期',
      title: '八卦创始人',
      contribution: '创立八卦，开创了中华文明的符号系统，被尊为"易学之祖"。通过观察自然现象，创造了表达宇宙万物变化规律的八个基本符号。',
      works: '八卦图'
    },
    {
      name: '周文王',
      period: '西周（约公元前1152-1056年）',
      title: '易学集大成者',
      contribution: '将八卦演化为六十四卦，撰写卦辞，奠定了《周易》的基础。在羑里被囚期间，深入研究易理，形成了完整的易学理论体系。',
      works: '《周易》卦辞'
    },
    {
      name: '孔子',
      period: '春秋时期（公元前551-479年）',
      title: '易学阐释者',
      contribution: '撰写《易传》（十翼），对《周易》进行了深入的哲学阐释，将易学从占卜工具提升为哲学思想体系。',
      works: '《易传》（十翼）'
    },
    {
      name: '王弼',
      period: '三国魏（226-249年）',
      title: '玄学易学代表',
      contribution: '以老庄思想解释《周易》，开创了义理派易学，强调"得意忘象"，注重易学的哲学内涵而非象数。',
      works: '《周易注》、《周易略例》'
    },
    {
      name: '邵雍',
      period: '北宋（1011-1077年）',
      title: '象数易学大师',
      contribution: '创立先天易学，发展了象数理论，提出"先天图"和"后天图"，对易学象数派产生了深远影响。',
      works: '《皇极经世》、《观物内篇》'
    },
    {
      name: '朱熹',
      period: '南宋（1130-1200年）',
      title: '理学易学集大成者',
      contribution: '将易学纳入理学体系，强调易学的道德修养功能，编撰《周易本义》，成为后世易学研究的重要参考。',
      works: '《周易本义》、《易学启蒙》'
    }
  ]);

  // 不同流派解释数据
  const easySchools = ref([
    {
      name: '象数派',
      period: '汉代至宋代',
      core: '注重卦象、爻象的数理关系，通过象数来解释易理，强调易学的预测功能。',
      features: '重视卦象的具体含义，运用数理推演，注重实用性，多用于占卜预测。以图表、数字为主要工具，讲究严密的逻辑推理。',
      representatives: '京房、邵雍、刘牧、朱震'
    },
    {
      name: '义理派',
      period: '魏晋至明清',
      core: '强调易学的哲学内涵和道德教化功能，注重义理阐释而轻象数。',
      features: '重视易学的哲学思辨，强调道德修养，淡化占卜功能。以文字训诂为主，注重经典的义理解释。',
      representatives: '王弼、韩康伯、程颐、朱熹'
    },
    {
      name: '汉学派',
      period: '汉代',
      core: '以汉代经学为基础，注重训诂考据，强调易学与天文历法的关系。',
      features: '重视古文献的考证，注重字义训诂，强调易学的科学性。多与天文、历法、五行学说结合。',
      representatives: '孟喜、京房、郑玄、虞翻'
    },
    {
      name: '宋学派',
      period: '宋代',
      core: '将易学与理学相结合，强调易学的本体论意义，注重心性修养。',
      features: '融合儒释道三家思想，强调理气关系，注重内在修养。以哲学思辨为主，重视易学的形而上学意义。',
      representatives: '周敦颐、程颢、程颐、朱熹、张载'
    },
    {
      name: '清代朴学派',
      period: '清代',
      core: '回归汉学传统，注重考据训诂，力求恢复易学的原始面貌。',
      features: '重视文献考证，注重音韵训诂，反对宋明理学的玄虚。以实证方法研究易学，强调客观性。',
      representatives: '惠栋、张惠言、焦循、李道平'
    },
    {
      name: '现代科学派',
      period: '近现代',
      core: '运用现代科学方法研究易学，探索易学与现代科学的关系。',
      features: '结合数学、物理学、生物学等现代科学，探索易学的科学内核。注重实证研究，力求客观理性。',
      representatives: '李约瑟、南怀瑾、刘大钧、廖名春'
    }
  ]);

  // 切换文化内容
  const switchCultureContent = (index: number) => {
    currentCultureIndex.value = index;
  };
</script>

<style lang="scss" scoped>
  .container {
    min-height: 100vh;
    padding: 30rpx;
    background: #f8f4e9;
  }

  .page-header {
    text-align: center;
    margin-bottom: 40rpx;
    padding: 40rpx 32rpx;
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-radius: 24rpx;
    box-shadow: 0 8rpx 32rpx rgba(139, 69, 19, 0.1);

    .title {
      font-size: 40rpx;
      font-weight: bold;
      color: #2c3e50;
      margin-bottom: 16rpx;
    }

    .subtitle {
      font-size: 28rpx;
      color: #7f8c8d;
      line-height: 1.5;
    }
  }

  // 文化内容区域样式

  .layout-tabs {
    display: flex;
    justify-content: center;
    gap: 0;
    background: transparent;
    border-radius: 0;
    padding: 12rpx 2rpx 6rpx 2rpx;
    box-shadow: none;
    border: none;

    .tab-item {
      flex: 1;
      padding: 12rpx 24rpx;
      text-align: center;
      font-size: 26rpx;
      font-weight: 500;
      background: #f8f9fa;
      color: #6c757d;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 1px solid #865404;

      &:first-child {
        border-radius: 12rpx 0 0 12rpx;
      }

      &:last-child {
        border-radius: 0 12rpx 12rpx 0;
      }

      &:not(:first-child) {
        border-left: none;
      }

      &.active {
        background: #865404;
        color: white;
      }

      &:active {
        transform: scale(0.98);
      }
    }
  }

  .culture-content {
    padding: 40rpx 0;
  }

  .content-header {
    text-align: center;
    margin-bottom: 40rpx;
    padding-bottom: 24rpx;
    border-bottom: 2rpx solid #f0f0f0;

    .content-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #2c3e50;
      margin-bottom: 12rpx;
    }

    .content-subtitle {
      font-size: 26rpx;
      color: #7f8c8d;
      line-height: 1.5;
    }
  }
  // 起源故事样式
  .origin-content {
    .story-item {
      margin-bottom: 32rpx;
      padding: 24rpx;
      border-radius: 16rpx;
      border-left: 4rpx solid #8b4513;

      &:last-child {
        margin-bottom: 0;
      }

      .story-title {
        font-size: 28rpx;
        font-weight: bold;
        color: #8b4513;
        margin-bottom: 16rpx;
      }

      .story-content {
        font-size: 26rpx;
        color: #333;
        line-height: 1.6;
        margin-bottom: 16rpx;
      }

      .story-quote {
        font-size: 24rpx;
        color: #666;
        font-style: italic;
        padding: 12rpx 16rpx;
        background: rgba(139, 69, 19, 0.05);
        border-radius: 8rpx;
      }
    }
  }

  // 大师介绍样式
  .masters-content {
    .master-item {
      margin-bottom: 32rpx;
      padding: 24rpx;
      border-radius: 16rpx;
      border-left: 4rpx solid #8b4513;

      &:last-child {
        margin-bottom: 0;
      }

      .master-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12rpx;

        .master-name {
          font-size: 28rpx;
          font-weight: bold;
          color: #8b4513;
        }

        .master-period {
          font-size: 22rpx;
          color: #999;
          background: rgba(139, 69, 19, 0.1);
          padding: 4rpx 12rpx;
          border-radius: 12rpx;
        }
      }

      .master-title {
        font-size: 24rpx;
        color: #666;
        margin-bottom: 16rpx;
        font-weight: 500;
      }

      .master-contribution {
        font-size: 26rpx;
        color: #333;
        line-height: 1.6;
        margin-bottom: 16rpx;
      }

      .master-works {
        font-size: 24rpx;
        color: #666;
        padding: 12rpx 16rpx;
        background: rgba(139, 69, 19, 0.05);
        border-radius: 8rpx;

        .works-label {
          font-weight: bold;
          color: #8b4513;
        }
      }
    }
  }

  // 流派差异样式
  .schools-content {
    .school-item {
      margin-bottom: 32rpx;
      padding: 24rpx;
      border-radius: 16rpx;
      border-left: 4rpx solid #8b4513;

      &:last-child {
        margin-bottom: 0;
      }

      .school-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16rpx;

        .school-name {
          font-size: 28rpx;
          font-weight: bold;
          color: #8b4513;
        }

        .school-period {
          font-size: 22rpx;
          color: #999;
          background: rgba(139, 69, 19, 0.1);
          padding: 4rpx 12rpx;
          border-radius: 12rpx;
        }
      }

      .school-core,
      .school-features,
      .school-representatives {
        margin-bottom: 12rpx;
        font-size: 26rpx;
        line-height: 1.6;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .core-label,
      .features-label,
      .representatives-label {
        font-weight: bold;
        color: #8b4513;
        margin-right: 8rpx;
      }
    }
  }
</style>
