<template>
  <PageWrapper title="快速取卦" :show-back="true">
    <view class="container">
      <!-- 卦象显示区域 -->
      <view class="hexagram-section">
        <view class="hexagram-title">{{ currentHexagram?.name || '乾为天' }}</view>
        <view class="hexagram-subtitle">点击爻线切换阴阳</view>
        
        <!-- 大尺寸卦象显示 -->
        <view class="hexagram-display">
          <HexagramDisplay
            :hexagram="currentHexagram || defaultHexagram"
            size="large"
            :interactive="true"
            @yao-click="handleYaoClick"
          />
        </view>
      </view>

      <!-- 相关卦象 -->
      <view class="related-hexagrams" v-if="currentHexagram">
        <view class="related-grid">
          <!-- 互卦 -->
          <view class="related-item" v-if="huGua">
            <view class="related-label">互卦</view>
            <HexagramDisplay
              :hexagram="huGua"
              size="small"
              :show-name="true"
            />
          </view>

          <!-- 错卦 -->
          <view class="related-item" v-if="cuoGua">
            <view class="related-label">错卦</view>
            <HexagramDisplay
              :hexagram="cuoGua"
              size="small"
              :show-name="true"
            />
          </view>

          <!-- 综卦 -->
          <view class="related-item" v-if="zongGua">
            <view class="related-label">综卦</view>
            <HexagramDisplay
              :hexagram="zongGua"
              size="small"
              :show-name="true"
            />
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <view class="action-button reset" @tap="resetHexagram">
          <view class="button-icon">🔄</view>
          <view class="button-text">重置</view>
        </view>
        <view class="action-button random" @tap="randomHexagram">
          <view class="button-icon">🎲</view>
          <view class="button-text">随机</view>
        </view>
        <view class="action-button detail" @tap="viewDetail">
          <view class="button-icon">📖</view>
          <view class="button-text">详情</view>
        </view>
      </view>

    </view>
  </PageWrapper>
</template>

<script lang="ts">
  import { defineComponent, ref, computed } from 'vue';
  import PageWrapper from '@/components/PageWrapper.vue';
  import HexagramDisplay from '@/components/HexagramDisplay.vue';
  import { hexagramList, getHexagramByYaos, getCuoGua, getZongGua, getHuGua } from '@/utils/hexagram';
  import type { Hexagram } from '@/utils/hexagram';

  export default defineComponent({
    name: 'QuickDivinationPage',
    components: {
      PageWrapper,
      HexagramDisplay
    },
    setup() {
      // 简单的八卦数据
      const trigramNames = ['坤', '艮', '坎', '巽', '震', '离', '兑', '乾'];
      const trigramSymbols = ['☷', '☶', '☵', '☴', '☳', '☲', '☱', '☰'];

      // 当前爻的状态，默认全为阳爻（1）
      const yaos = ref<number[]>([1, 1, 1, 1, 1, 1]);

      // 默认卦象（乾卦）
      const defaultHexagram = hexagramList[0]; // 乾卦

      // 根据当前爻状态计算卦象
      const currentHexagram = computed<Hexagram | null>(() => {
        return getHexagramByYaos(yaos.value);
      });

      // 计算相关卦象
      const huGua = computed<Hexagram | null>(() => {
        return currentHexagram.value ? getHuGua(currentHexagram.value) : null;
      });

      const cuoGua = computed<Hexagram | null>(() => {
        return currentHexagram.value ? getCuoGua(currentHexagram.value) : null;
      });

      const zongGua = computed<Hexagram | null>(() => {
        return currentHexagram.value ? getZongGua(currentHexagram.value) : null;
      });

      // 处理爻线点击
      const handleYaoClick = (yaoIndex: number) => {
        // HexagramDisplay组件中爻线是从上到下显示的（reverse后），
        // 所以需要转换索引：index 0 = 第6爻，index 5 = 第1爻
        const actualYaoIndex = 5 - yaoIndex;
        // 切换爻的阴阳状态
        yaos.value[actualYaoIndex] = yaos.value[actualYaoIndex] === 1 ? 0 : 1;
      };

      // 获取上卦（上三爻）
      const getUpperTrigram = () => {
        const upperYaos = yaos.value.slice(3, 6);
        const trigramIndex = upperYaos[2] * 4 + upperYaos[1] * 2 + upperYaos[0];
        return `${trigramNames[trigramIndex]}（${trigramSymbols[trigramIndex]}）`;
      };

      // 获取下卦（下三爻）
      const getLowerTrigram = () => {
        const lowerYaos = yaos.value.slice(0, 3);
        const trigramIndex = lowerYaos[2] * 4 + lowerYaos[1] * 2 + lowerYaos[0];
        return `${trigramNames[trigramIndex]}（${trigramSymbols[trigramIndex]}）`;
      };

      // 重置为乾卦
      const resetHexagram = () => {
        yaos.value = [1, 1, 1, 1, 1, 1];
      };

      // 随机生成卦象
      const randomHexagram = () => {
        yaos.value = Array.from({ length: 6 }, () => Math.random() > 0.5 ? 1 : 0);
      };

      // 查看详情
      const viewDetail = () => {
        if (currentHexagram.value) {
          uni.navigateTo({
            url: `/pages/gua/hexagram/hexagram-detail?id=${currentHexagram.value.id}`
          });
        }
      };

      return {
        yaos,
        defaultHexagram,
        currentHexagram,
        huGua,
        cuoGua,
        zongGua,
        handleYaoClick,
        getUpperTrigram,
        getLowerTrigram,
        resetHexagram,
        randomHexagram,
        viewDetail
      };
    }
  });
</script>

<style lang="scss" scoped>
  .container {
    padding: 32rpx 24rpx;
  }

  // 卦象显示区域
  .hexagram-section {
    text-align: center;
    margin-bottom: 48rpx;

    .hexagram-title {
      font-size: 40rpx;
      font-weight: 600;
      color: #8b4513;
      margin-bottom: 8rpx;
    }

    .hexagram-subtitle {
      font-size: 24rpx;
      color: #a97c50;
      margin-bottom: 32rpx;
    }

    .hexagram-display {
      display: flex;
      justify-content: center;
      margin: 32rpx 0;
    }
  }

  // 相关卦象
  .related-hexagrams {
    margin-bottom: 64rpx;

    .related-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #8b4513;
      text-align: center;
      margin-bottom: 24rpx;
    }

    .related-grid {
      display: flex;
      justify-content: space-around;

      .related-item {
        flex: 1;
        text-align: center;
      
        .related-label {
          font-size: 22rpx;
          font-weight: 500;
          color: #865404;
          text-align: center;
          padding: 8rpx 12rpx;
          border-radius: 8rpx;
          background: rgba(212, 175, 55, 0.1);
          border: 1rpx solid #865404;
          margin-bottom: 12rpx;
          display: inline-block;
        }
      }
    }
  }

  // 卦象信息
  .hexagram-info {
    background: white;
    border-radius: 12rpx;
    padding: 24rpx;
    margin-bottom: 32rpx;
    border: 1rpx solid #e8e8e8;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);

    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12rpx 0;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .info-label {
        font-size: 24rpx;
        color: #666;
      }

      .info-value {
        font-size: 24rpx;
        font-weight: 500;
        color: #8b4513;
      }
    }
  }

  // 操作按钮
  .action-buttons {
    display: flex;
    gap: 16rpx;
    margin: 32rpx ;

    .action-button {
      flex: 1;
      background: white;
      border-radius: 12rpx;
      padding: 20rpx;
      text-align: center;
      border: 1rpx solid #e8e8e8;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
      transition: all 0.2s ease;

      &:active {
        transform: scale(0.98);
        background: #f8f9fa;
      }

      .button-icon {
        font-size: 28rpx;
        margin-bottom: 8rpx;
      }

      .button-text {
        font-size: 22rpx;
        color: #333;
        font-weight: 500;
      }

      &.reset {
        .button-icon {
          color: #6c757d;
        }
      }

      &.random {
        .button-icon {
          color: #28a745;
        }
      }

      &.detail {
        .button-icon {
          color: #8b4513;
        }
      }
    }
  }

  // 使用说明
  .usage-tips {
    background: white;
    border-radius: 12rpx;
    padding: 24rpx;
    border: 1rpx solid #e8e8e8;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);

    .tips-title {
      font-size: 26rpx;
      font-weight: 600;
      color: #8b4513;
      margin-bottom: 16rpx;
    }

    .tips-content {
      .tip-item {
        font-size: 22rpx;
        color: #666;
        line-height: 1.5;
        margin-bottom: 8rpx;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  // 响应式适配
  @media (max-width: 750rpx) {
    .container {
      padding: 24rpx 16rpx;
    }

    .hexagram-section {
      .hexagram-title {
        font-size: 32rpx;
      }

      .hexagram-subtitle {
        font-size: 20rpx;
      }
    }

    .related-hexagrams {
      .related-title {
        font-size: 24rpx;
      }

      .related-grid {
        flex-direction: column;
        gap: 12rpx;

        .related-item {
          padding: 16rpx;

          .related-label {
            font-size: 18rpx;
            padding: 6rpx 8rpx;
          }
        }
      }
    }

    .action-buttons {
      gap: 12rpx;

      .action-button {
        padding: 16rpx;

        .button-icon {
          font-size: 24rpx;
        }

        .button-text {
          font-size: 20rpx;
        }
      }
    }
  }
</style>
