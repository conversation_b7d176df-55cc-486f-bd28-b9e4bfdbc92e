<template>
  <PageWrapper :title="`${hexagram.code} ${hexagram.name}`" :show-back="true" @back="onBack">
    <view class="container">
        <view class="hexagram-card" @touchstart="onTouchStart" @touchend="onTouchEnd">
          <view class="hexagram-content">
            <view class="arrow-btn left-arrow" @tap="onPrevHexagram">
              <YiIcon name="arrow-right" :size="38" style="transform: rotate(180deg)" />
            </view>
            <view class="binary-container">
              <view v-for="(yao, index) in getYaoLines(hexagram.binary)" :key="index" class="yao">
                <template v-if="yao === '阳'">
                  <view class="line yang-line"></view>
                </template>
                <template v-else>
                  <view class="yin-line">
                    <view class="yin-segment"></view>
                    <view class="yin-segment"></view>
                  </view>
                </template>
              </view>
            </view>
            <view class="info">
              <text class="label">{{ hexagram.label }}</text>
              <text class="phase">{{ hexagram.phase }}</text>
              <text class="description">{{ hexagram.description }}</text>
            </view>
            <view class="arrow-btn right-arrow" @tap="onNextHexagram">
              <YiIcon name="arrow-right" :size="38" />
            </view>
          </view>
        </view>

        <view class="button-controller">
          <view v-for="row in 2" :key="row" class="button-row">
            <button
              v-for="(btn, idx) in buttonNames.slice((row - 1) * 3, row * 3)"
              :key="idx + (row - 1) * 3"
              :class="['yao-btn', { active: buttonStates[idx + (row - 1) * 3] }]"
              @tap="toggleButton(idx + (row - 1) * 3)"
            >
              {{ btn }}
            </button>
          </view>
        </view>

        <view class="html-content" :class="getActiveClasses()">
          <!-- 调试信息 -->
          <view v-if="!htmlContent" class="debug-info">
            <text>调试信息:</text>
            <text>卦象ID: {{ hexagramId }}</text>
            <text>卦象名称: {{ hexagram.name }}</text>
            <text>页面状态: 等待HTML内容加载...</text>
          </view>

          <mp-html v-if="htmlContent" :key="activeClassesKey" :content="htmlContent" :tag-style="mpTagStyle" />

          <!-- 如果HTML内容为空但不是加载中，显示错误信息 -->
          <view v-if="htmlContent && htmlContent.length < 50" class="error-info">
            <text>HTML内容异常:</text>
            <text>{{ htmlContent }}</text>
          </view>
        </view>
      <BackToTop ref="backToTop" />
    </view>
  </PageWrapper>
</template>

<script lang="ts">
  import { defineComponent } from 'vue';
  import { getHexagramById } from '@/utils/hexagram';

  import { getStringSize } from '@/utils/tools';
  import { loadHexagramContent } from '@/utils/html-loader';
  import YiIcon from '@/components/YiIcon.vue';
  import BackToTop from '@/components/BackToTop.vue';
  import PageWrapper from '@/components/PageWrapper.vue';

  export default defineComponent({
    components: { YiIcon, BackToTop, PageWrapper },
    data() {
      return {
        hexagramId: 1,
        hexagram: getHexagramById(1),
        buttonNames: ['周易正义', '梅花易数', '周易本义', '伊川易传', '断易天机', '译解易经'],
        classList: ['zy', 'mh', 'by', 'yc', 'tj', 'yj'],
        buttonStates: [false, false, false, false, false, false],
        htmlContent: '',
        touch: { startX: 0, endX: 0 },
        mpTagStyle: {
          h3: 'display:block;border-left:8rpx solid #865404;padding-left:12rpx;margin-top:40rpx;font-size:32rpx;font-weight:bold;color:#333;',
          h4: 'display:block;border-left:8rpx solid #B5A642;padding-left:12rpx;margin-top:40rpx;font-size:30rpx;font-weight:bold;color:#333;',
        },
      };
    },
    computed: {
      activeClassesKey() {
        return this.buttonStates.join('-') + '-' + (this.htmlContent?.length || 0);
      },
    },
    mounted() {

      // 获取页面参数中的卦象ID
      const pages = getCurrentPages();
      const current = pages[pages.length - 1];

      const options =
        (current &&
          (current as { $page?: { options?: Record<string, string> } }).$page &&
          (current as { $page: { options: Record<string, string> } }).$page.options) ||
        (current as { options?: Record<string, string> }).options;

      console.log(`[detail.vue] 🔗 页面参数: ${JSON.stringify(options)}`);

      if (options && options.id) {
        this.hexagramId = parseInt(options.id);
        this.hexagram = getHexagramById(this.hexagramId);
        console.log(`[detail.vue] ✅ 获取卦象ID: ${this.hexagramId} (${this.hexagram.name})`);
      } else {
        console.log(`[detail.vue] ⚠️ 未获取到参数，使用默认ID: ${this.hexagramId}`);
      }
      console.log(`[detail.vue] 🚀 开始加载卦象内容，ID: ${this.hexagramId}`);

      loadHexagramContent(this.hexagramId)
        .then((content) => {
          this.htmlContent = content;
          const contentSize = getStringSize(content);
          console.log(`[detail.vue] ✅ HTML内容加载成功，长度: ${contentSize}`);
        })
        .catch((error) => {
          console.log(`[detail.vue] ❌ HTML内容加载失败: ${error}`);
          this.htmlContent = `<div class="v-container zy"><div class="v-author"><span>卦象${this.hexagramId}</span></div><div>内容加载失败，请重试</div></div>`;
        });
    },
    methods: {
      onTouchStart(e: TouchEvent) {
        this.touch.startX = e.touches[0].clientX;
      },
      onTouchEnd(e: TouchEvent) {
        this.touch.endX = e.changedTouches[0].clientX;
        const deltaX = this.touch.endX - this.touch.startX;
        if (Math.abs(deltaX) > 50) {
          if (deltaX > 0) {
            this.onPrevHexagram();
          } else {
            this.onNextHexagram();
          }
        }
      },
      getYaoLines(binary: string) {
        return binary
          .split('')
          .reverse()
          .map((bit) => (bit === '1' ? '阳' : '阴'));
      },
      onPrevHexagram() {
        if (this.hexagramId > 1) {
          // 直接更新当前页面数据，而不是跳转新页面
          this.hexagramId = this.hexagramId - 1;
          this.hexagram = getHexagramById(this.hexagramId);
          loadHexagramContent(this.hexagramId)
            .then((content) => {
              this.htmlContent = content;
            })
            .catch((error) => {
              console.error('加载HTML内容失败:', error);
              this.htmlContent = `<div class="v-container zy"><div class="v-author"><span>卦象${this.hexagramId}</span></div><div>内容加载失败，请重试</div></div>`;
            });
        }
      },
      onNextHexagram() {
        if (this.hexagramId < 64) {
          // 直接更新当前页面数据，而不是跳转新页面
          this.hexagramId = this.hexagramId + 1;
          this.hexagram = getHexagramById(this.hexagramId);
          loadHexagramContent(this.hexagramId)
            .then((content) => {
              this.htmlContent = content;
            })
            .catch((error) => {
              console.error('加载HTML内容失败:', error);
              this.htmlContent = `<div class="v-container zy"><div class="v-author"><span>卦象${this.hexagramId}</span></div><div>内容加载失败，请重试</div></div>`;
            });
        }
      },
      toggleButton(idx: number) {
        this.buttonStates = this.buttonStates.map((state, i) => (i === idx ? !state : state));
      },
      getActiveClasses() {
        const activeClasses: string[] = [];
        this.buttonStates.forEach((state, idx) => {
          if (state) {
            activeClasses.push(`show-${this.classList[idx]}`);
          }
        });
        return activeClasses.join(' ');
      },
      onBack() {
        const pages = getCurrentPages();
        console.log('详情页返回，当前页面栈长度:', pages.length);

        if (pages.length > 1) {
          const prevPage = pages[pages.length - 2];
          const prevRoute = prevPage?.route || '';
          console.log('上一个页面路径:', prevRoute);

          // 检查上一个页面是否是TabBar页面
          const tabBarPages = ['pages/index/index', 'pages/gua/index', 'pages/game/index', 'pages/user/index'];

          if (tabBarPages.includes(prevRoute)) {
            // 如果上一个页面是TabBar页面，使用switchTab
            uni.switchTab({
              url: `/${prevRoute}`,
              fail: (err) => {
                console.warn('切换到TabBar页面失败:', err);
                uni.switchTab({ url: '/pages/gua/index' });
              },
            });
          } else {
            // 普通页面返回
            uni.navigateBack({
              fail: (err) => {
                console.warn('返回失败:', err);
                // 如果是从六十四卦列表页进入的，返回到列表页
                if (prevRoute === 'pages/gua/hexagram/hexagram-list') {
                  uni.navigateTo({ url: '/pages/gua/hexagram/hexagram-list' });
                } else {
                  // 其他情况返回到卦典首页
                  uni.switchTab({ url: '/pages/gua/index' });
                }
              },
            });
          }
        } else {
          // 没有历史堆栈，默认跳转到六十四卦列表页
          uni.navigateTo({
            url: '/pages/gua/hexagram/hexagram-list',
            fail: (err) => {
              console.warn('跳转到六十四卦列表失败:', err);
              // 如果跳转失败，再尝试卦典首页
              uni.switchTab({ url: '/pages/gua/index' });
            }
          });
        }
      },
      onShare() {
        uni.showShareMenu({ withShareTicket: true, menus: ['shareAppMessage', 'shareTimeline'] });
      },

    },
    onPageScroll(e: { scrollTop: number }) {
      const backToTop = this.$refs.backToTop as { setMPShow?: (scrollTop: number) => void };
      if (backToTop && typeof backToTop.setMPShow === 'function') {
        backToTop.setMPShow(e.scrollTop);
      }
    },
  });
</script>

<style lang="scss" scoped>
  .container {
    min-height: 100vh;
    padding: 30rpx;
  }

  .hexagram-detail-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24rpx;
    height: 112rpx;
    border-bottom: 2rpx solid #a4a29f;
    .back-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
      margin: 0;
      border-radius: 50%;
      cursor: pointer;
      background: transparent;
      border: none;
      line-height: 1;
    }
    .btn-hover {
      background: #f3ede2;
    }
    .share {
      color: #333;
      padding: 16rpx;
      cursor: pointer;
      display: flex;
      align-items: center;
    }
    .title {
      font-size: 36rpx;
      color: #333;
      font-weight: bold;
      flex: 1;
      text-align: center;
      margin: 0 16rpx;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .hexagram-card {
    padding: 20rpx 0rpx;
    border-radius: 24rpx;
    background: #f8f4e9;

    .hexagram-content {
      position: relative;
      display: flex;
      align-items: center;
      gap: 30rpx;
      padding: 0 70rpx;
    }

    .arrow-btn {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 60rpx;
      text-align: center;
      background: none;
      border: none;
      box-shadow: none;
      outline: none;
      -webkit-appearance: none;
      appearance: none;
      font-size: 56rpx;
      color: #865404;
      cursor: pointer;
      transition: color 0.2s;
      user-select: none;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 1;
      font-weight: bold;
    }

    .arrow-btn:active {
      color: #b5a642;
    }

    .left-arrow {
      left: 0;
    }

    .right-arrow {
      right: 0;
    }

    .binary-container {
      display: flex;
      flex-direction: column-reverse;
      gap: 12rpx;
      margin-bottom: 16rpx;

      .yao {
        display: flex;
        justify-content: center;

        .line,
        .yin-segment {
          height: 28rpx;
          border-radius: 6rpx;
          box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
        }

        .yang-line {
          width: 200rpx;
          background: #865404;
        }

        .yin-line {
          display: flex;
          gap: 16rpx;
          width: 200rpx;
          justify-content: space-between;

          .yin-segment {
            width: calc((200rpx - 16rpx) / 2);
            background: #7c7a7a;
            box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
          }
        }
      }
    }

    .info {
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      .label {
        font-size: 52rpx;
        font-weight: bold;
        color: #333;
      }

      .phase {
        font-size: 32rpx;
        color: #865404;
        border: 2rpx solid #865404;
        margin: 10rpx 0;
        padding: 2rpx;
      }

      .description {
        font-size: 28rpx;
        font-style: italic;
        color: #666;
        line-height: 40rpx;
      }
    }
  }

  .button-controller {
    margin: 40rpx 0;

    .button-row {
      display: flex;
      justify-content: center;
      gap: 24rpx;
      margin-bottom: 28rpx;

      .yao-btn {
        padding: 0 36rpx;
        border: 2rpx solid #865404;
        border-radius: 12rpx;
        background: #fff;
        color: #865404;
        font-size: 30rpx;
        transition:
          background 0.2s,
          color 0.2s;
        outline: none;
      }

      .yao-btn.active {
        background: #865404;
        color: #fff;
      }
    }
  }

  .html-content {
    // 默认全部隐藏
    :deep(.v-container) {
      display: none;
    }

    // 调试信息样式
    .debug-info, .error-info {
      padding: 32rpx;
      background: #f8f9fa;
      border-radius: 16rpx;
      border-left: 8rpx solid #007bff;
      margin-bottom: 32rpx;

      text {
        display: block;
        font-size: 28rpx;
        line-height: 1.5;
        color: #495057;
        margin-bottom: 8rpx;

        &:first-child {
          font-weight: 600;
          color: #007bff;
        }
      }
    }

    .error-info {
      border-left-color: #dc3545;
      background: #f8d7da;

      text:first-child {
        color: #dc3545;
      }
    }

    // h3和h4标题始终显示
    :deep(h3),
    :deep(h4) {
      display: block !important;
      border-left: 8rpx solid #865404;
      padding-left: 12rpx;
      margin-top: 40rpx;
      font-size: 32rpx;
    }

    :deep(h4) {
      border-left-color: #b5a642;
    }

    // 只显示当前激活的
    &.show-zy :deep(.v-container.zy),
    &.show-mh :deep(.v-container.mh),
    &.show-by :deep(.v-container.by),
    &.show-yc :deep(.v-container.yc),
    &.show-tj :deep(.v-container.tj),
    &.show-yj :deep(.v-container.yj) {
      display: block;
    }

    :deep(.v-author) {
      color: #865404;

      span {
        font-size: 32rpx;
      }
    }

    :deep(.v-container) {
      border: 2rpx solid #e0d6b9;
      border-radius: 16rpx;
      padding: 16rpx;
      margin: 20rpx 0;

      div {
        margin-bottom: 16rpx;
      }
    }

    // 微信小程序 mp-html 组件会把标题转换为带有 _h3/_h4 类名的节点
    :deep(._h3),
    :deep(._h4) {
      display: block !important;
      border-left: 8rpx solid #865404;
      padding-left: 12rpx;
      margin-top: 40rpx;
      font-size: 32rpx;
    }

    :deep(._h4) {
      border-left-color: #b5a642;
    }
  }
</style>
