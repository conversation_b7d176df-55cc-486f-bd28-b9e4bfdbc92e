<template>
  <PageWrapper title="六十四卦" :show-back="true">
    <view class="container">
      <!-- 搜索栏 
      <view class="search-section">
        <view class="search-box">
          <YiIcon name="search" :size="20" class="search-icon" />
          <input 
            v-model="searchText" 
            type="text" 
            placeholder="搜索卦象名称或描述" 
            @input="onSearch" 
            class="search-input"
          />
          <view v-if="searchText" @tap="clearSearch" class="clear-btn">
            <YiIcon name="close" :size="16" />
          </view>
        </view>
      </view>
-->
      <!-- 卦序选择 -->
      <view class="filter-section">
        <view class="order-buttons">
          <view
            :class="['order-button', { active: currentOrder === 'wenwang' }]"
            @tap="setOrder('wenwang')"
          >
            文王卦序
          </view>
          <view
            :class="['order-button', { active: currentOrder === 'bagong' }]"
            @tap="setOrder('bagong')"
          >
            八宫卦序
          </view>
          <view
            :class="['order-button', { active: currentOrder === 'xiantian' }]"
            @tap="setOrder('xiantian')"
          >
            先天卦序
          </view>
        </view>

        <!-- 信息提示栏 -->
        <view class="info-bar" v-if="currentOrder">
          <view class="info-text">
            {{ getOrderDescription() }}
          </view>
          <view class="info-buttons">
            <view class="info-button" @tap="handleInfoButtonClick()">
              {{ getInfoButtonText() }}
            </view>
            <view v-if="currentOrder === 'wenwang'" class="info-button" @tap="showGuaDeGe()">
              卦德歌
            </view>
          </view>
        </view>
      </view>

      <!-- 卦序歌弹框 -->
      <view v-if="showGuaXuGeModal" class="modal-overlay" @tap="closeGuaXuGeModal">
        <view class="modal-content" @tap.stop>
          <view class="modal-header">
            <view class="modal-title">文王卦序歌</view>
            <view class="modal-close" @tap="closeGuaXuGeModal">×</view>
          </view>
          <view class="modal-body">
            <view class="gua-xu-ge-content">
              <view class="verse-line">
                <text class="gua-name">乾</text><text class="gua-name">坤</text><text class="gua-name">屯</text><text class="gua-name">蒙</text><text class="gua-name">需</text><text class="gua-name">讼</text><text class="gua-name">师</text>，
                <text class="gua-name">比</text><text class="gua-name">小畜</text>兮<text class="gua-name">履</text><text class="gua-name">泰</text><text class="gua-name">否</text>；
              </view>
              <view class="verse-line">
                <text class="gua-name">同人</text><text class="gua-name">大有</text><text class="gua-name">谦</text><text class="gua-name">豫</text><text class="gua-name">随</text>，
                <text class="gua-name">蛊</text><text class="gua-name">临</text><text class="gua-name">观</text>兮<text class="gua-name">噬嗑</text><text class="gua-name">贲</text>；
              </view>
              <view class="verse-line">
                <text class="gua-name">剥</text><text class="gua-name">复</text><text class="gua-name">无妄</text><text class="gua-name">大畜</text><text class="gua-name">颐</text>，
                <text class="gua-name">大过</text><text class="gua-name">坎</text><text class="gua-name">离</text>三十备。
              </view>
              <view class="verse-line">
                <text class="gua-name">咸</text><text class="gua-name">恒</text><text class="gua-name">遁</text>兮及<text class="gua-name">大壮</text>，
                <text class="gua-name">晋</text>与<text class="gua-name">明夷</text><text class="gua-name">家人</text><text class="gua-name">睽</text>；
              </view>
              <view class="verse-line">
                <text class="gua-name">蹇</text><text class="gua-name">解</text><text class="gua-name">损</text><text class="gua-name">益</text><text class="gua-name">夬</text><text class="gua-name">姤</text><text class="gua-name">萃</text>，
                <text class="gua-name">升</text><text class="gua-name">困</text><text class="gua-name">井</text><text class="gua-name">革</text><text class="gua-name">鼎</text><text class="gua-name">震</text>继；
              </view>
              <view class="verse-line">
                <text class="gua-name">艮</text><text class="gua-name">渐</text><text class="gua-name">归妹</text><text class="gua-name">丰</text><text class="gua-name">旅</text><text class="gua-name">巽</text>，
                <text class="gua-name">兑</text><text class="gua-name">涣</text><text class="gua-name">节</text>兮<text class="gua-name">中孚</text>至；
              </view>
              <view class="verse-line">
                <text class="gua-name">小过</text><text class="gua-name">既济</text>兼<text class="gua-name">未济</text>，
                是为下经三十四。
              </view>
            </view>
            <view class="modal-description">
              文王卦序是《周易》六十四卦的传统排列顺序，体现了卦象之间的内在联系和哲学思想。
            </view>
          </view>
        </view>
      </view>

      <!-- 爻变规律弹框 -->
      <view v-if="showYaoBianModal" class="modal-overlay" @tap="closeYaoBianModal">
        <view class="modal-content" @tap.stop>
          <view class="modal-header">
            <view class="modal-title">八宫爻变规律</view>
            <view class="modal-close" @tap="closeYaoBianModal">×</view>
          </view>
          <view class="modal-body">
            <view class="yao-bian-content">
              <view class="rule-item">
                <view class="rule-title">1. 本宫卦（纯卦）</view>
                <view class="rule-desc">每宫第一卦，六爻相同</view>
              </view>
              <view class="rule-item">
                <view class="rule-title">2. 一世卦</view>
                <view class="rule-desc">初爻变，其余五爻不变</view>
              </view>
              <view class="rule-item">
                <view class="rule-title">3. 二世卦</view>
                <view class="rule-desc">二爻变，其余五爻不变</view>
              </view>
              <view class="rule-item">
                <view class="rule-title">4. 三世卦</view>
                <view class="rule-desc">三爻变，其余五爻不变</view>
              </view>
              <view class="rule-item">
                <view class="rule-title">5. 四世卦</view>
                <view class="rule-desc">四爻变，其余五爻不变</view>
              </view>
              <view class="rule-item">
                <view class="rule-title">6. 五世卦</view>
                <view class="rule-desc">五爻变，其余五爻不变</view>
              </view>
              <view class="rule-item">
                <view class="rule-title">7. 游魂卦</view>
                <view class="rule-desc">四爻变，其余五爻不变</view>
              </view>
              <view class="rule-item">
                <view class="rule-title">8. 归魂卦</view>
                <view class="rule-desc">内卦变为外卦，外卦变为内卦</view>
              </view>
            </view>
            <view class="modal-description">
              八宫卦序按照爻变规律将六十四卦分为八宫，每宫八卦，体现了卦象的演变过程。
            </view>
          </view>
        </view>
      </view>

      <!-- 卦德歌弹框 -->
      <view v-if="showGuaDeGeModal" class="modal-overlay" @tap="closeGuaDeGeModal">
        <view class="modal-content" @tap.stop>
          <view class="modal-header">
            <view class="modal-title">卦德歌</view>
            <view class="modal-close" @tap="closeGuaDeGeModal">×</view>
          </view>
          <view class="modal-body">
            <view class="gua-de-ge-content">
              <view class="verse-line">
                <text class="gua-name">乾</text>健<text class="gua-name">坤</text>厚<text class="gua-name">屯</text>作始，
                <text class="gua-name">蒙</text>发<text class="gua-name">需</text>补<text class="gua-name">讼</text>分歧；
              </view>
              <view class="verse-line">
                <text class="gua-name">师</text>众<text class="gua-name">比</text>辅<text class="gua-name">小畜</text>懿，
                <text class="gua-name">履</text>行<text class="gua-name">泰</text>和<text class="gua-name">否</text>塞闭；
              </view>
              <view class="verse-line">
                <text class="gua-name">同人</text>合助<text class="gua-name">大有</text>利，
                <text class="gua-name">谦</text>逊<text class="gua-name">豫</text>乐<text class="gua-name">随</text>缘喜；
              </view>
              <view class="verse-line">
                <text class="gua-name">蛊</text>惑<text class="gua-name">临</text>莅<text class="gua-name">观</text>察之，
                <text class="gua-name">噬嗑</text>卡呛<text class="gua-name">贲</text>修饰；
              </view>
              <view class="verse-line">
                <text class="gua-name">剥</text>落<text class="gua-name">复</text>原<text class="gua-name">无妄</text>谧，
                <text class="gua-name">大畜</text>积德<text class="gua-name">颐</text>养滋；
              </view>
              <view class="verse-line">
                <text class="gua-name">大过</text>极度陷<text class="gua-name">坎</text>习，
                <text class="gua-name">离</text>明绚丽上经毕。
              </view>
              <view class="verse-line">
                <text class="gua-name">咸</text>感<text class="gua-name">恒</text>持<text class="gua-name">遁</text>隐避，
                <text class="gua-name">大壮</text>仪威德<text class="gua-name">晋</text>级；
              </view>
              <view class="verse-line">
                <text class="gua-name">明夷</text>用晦<text class="gua-name">家人</text>齐，
                <text class="gua-name">睽</text>同<text class="gua-name">蹇</text>难<text class="gua-name">解</text>赦释；
              </view>
              <view class="verse-line">
                <text class="gua-name">损</text>舍<text class="gua-name">益</text>彰<text class="gua-name">夬</text>断施，
                <text class="gua-name">姤</text>遇<text class="gua-name">萃</text>集<text class="gua-name">升</text>长起；
              </view>
              <view class="verse-line">
                <text class="gua-name">困</text>虑<text class="gua-name">井</text>然<text class="gua-name">革</text>明治，
                <text class="gua-name">鼎</text>立<text class="gua-name">震</text>动<text class="gua-name">艮</text>依止；
              </view>
              <view class="verse-line">
                <text class="gua-name">渐</text>进<text class="gua-name">归妹</text>妇道知，
                <text class="gua-name">丰</text>盛<text class="gua-name">旅</text>羁<text class="gua-name">巽</text>风事；
              </view>
              <view class="verse-line">
                <text class="gua-name">兑</text>悦<text class="gua-name">涣</text>散拟<text class="gua-name">节</text>制，
                <text class="gua-name">中孚</text>诚信<text class="gua-name">小过</text>谨；
              </view>
              <view class="verse-line">
                <text class="gua-name">既济</text>达成仍<text class="gua-name">未济</text>，
                下经至此演周易。
              </view>
            </view>
            <view class="modal-description">
              卦德歌以诗歌形式概括了六十四卦的核心德性和含义，便于记忆和理解各卦的精神内涵。
            </view>
          </view>
        </view>
      </view>

      <!-- 先天八卦图弹框 -->
      <view v-if="showXiantianModal" class="modal-overlay" @tap="closeXiantianModal">
        <view class="modal-content xiantian-modal" @tap.stop>
          <view class="modal-header">
            <view class="modal-title">六十四卦方图</view>
            <view class="modal-close" @tap="closeXiantianModal">×</view>
          </view>
          <view class="modal-body">
            <view class="modal-description">
              <p>
               伏羲先天六十四卦方图，《宋元学案》称“方图四分四层图”：把六十四卦圆图，依演化顺序分成八等分，由坤卦起依次排列，形成一个八乘八的方阵。
              </p>
              <p>
              以方图的中心为基点，恰好是一个对称图形，也就是相对位置上，两卦的阴阳刚好相反（错卦）;</p>
              <p>
              方图内外四层: 
              </p><p>最内层为“巽、恒、益、震”4个卦;
              </p>
              <p>
              围绕在外有12个卦（坎、涣、解、未济、井、鼎、屯、噬嗑、既济、家人、丰、离）;
              </p>
              <p>
              又外一层为20个卦（艮、蹇、渐、小过、旅、咸、蒙、困、蛊、大过、颐、随、贲、革、损、节、中孚、归妹、睽、兑）;
              </p>
              <p>
              最外层有28个卦（坤、剥、比、观、豫、晋、萃、否、谦、遁、师、讼、升、姤、复、无妄、明夷、同人、临、履、泰、大畜、需、小畜、大壮、大有、夬、乾卦）
              </p>

            </view>
          </view>
        </view>
      </view>

      <!-- 卦象网格 -->
      <view class="hexagram-grid">
        <view
          v-for="hexagram in filteredHexagrams"
          :key="hexagram.id"
          class="hexagram-card"
          @tap="onHexagramTap(hexagram.id)"
        >
          <!-- 使用 HexagramDisplay 组件 -->
          <view class="hexagram-display-container">
            <HexagramDisplay
              :hexagram="hexagram"
              size="tiny"
            />
          </view>
          
          <!-- 卦象信息 -->
          <view class="hexagram-info">
            <!-- <view class="hexagram-number">{{ hexagram.id }}</view> -->
            <view class="hexagram-name">{{ hexagram.name }}</view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="filteredHexagrams.length === 0" class="empty-state">
        <YiIcon name="search" :size="60" class="empty-icon" />
        <text class="empty-text">未找到匹配的卦象</text>
        <text class="empty-hint">请尝试其他关键词</text>
      </view>
    </view>
  </PageWrapper>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { hexagrams } from '@/utils/hexagram';
  import YiIcon from '@/components/YiIcon.vue';
  import PageWrapper from '@/components/PageWrapper.vue';
  import HexagramDisplay from '@/components/HexagramDisplay.vue';

  // 响应式数据
  const searchText = ref('');
  const currentOrder = ref('wenwang'); // 当前卦序：'wenwang' 或 'bagong'
  const showGuaXuGeModal = ref(false); // 卦序歌弹框显示状态
  const showYaoBianModal = ref(false); // 爻变规律弹框显示状态
  const showGuaDeGeModal = ref(false); // 卦德歌弹框显示状态
  const showXiantianModal = ref(false); // 先天八卦图弹框显示状态

  // 计算属性：过滤和排序后的卦象
  const filteredHexagrams = computed(() => {
    let result = [...hexagrams];

    // 按搜索文本筛选
    if (searchText.value) {
      const search = searchText.value.toLowerCase().trim();
      result = result.filter(hexagram =>
        hexagram.name.toLowerCase().includes(search) ||
        hexagram.description.toLowerCase().includes(search) ||
        hexagram.id.toString().includes(search)
      );
    }

    // 按卦序排序
    if (currentOrder.value === 'bagong') {
      // 八宫卦序：按 idBagong 字段排序
      result = result.sort((a, b) => a.idBagong - b.idBagong);
    } else if (currentOrder.value === 'xiantian') {
      // 先天卦序：按 idXiantian 字段排序
      result = result.sort((a, b) => a.idXiantian - b.idXiantian);
    } else {
      // 文王卦序：按原始ID排序（默认就是文王卦序）
      result = result.sort((a, b) => a.id - b.id);
    }

    return result;
  });

  // 方法
  const onSearch = () => {
    // 搜索防抖可以在这里实现
  };

  const clearSearch = () => {
    searchText.value = '';
  };

  const setOrder = (order: string) => {
    currentOrder.value = order;
  };

  // 获取卦序描述
  const getOrderDescription = () => {
    switch (currentOrder.value) {
      case 'wenwang':
        return '文王卦序体现卦象间的哲学联系';
      case 'bagong':
        return '八宫卦序按爻变规律分为八宫，每宫八卦';
      case 'xiantian':
        return '先天卦序体现阴阳消长';
      default:
        return '';
    }
  };

  // 获取信息按钮文本
  const getInfoButtonText = () => {
    switch (currentOrder.value) {
      case 'wenwang':
        return '卦序歌';
      case 'bagong':
        return '查看爻变规律';
      case 'xiantian':
        return '六十四卦方图';
      default:
        return '';
    }
  };

  // 处理信息按钮点击
  const handleInfoButtonClick = () => {
    switch (currentOrder.value) {
      case 'wenwang':
        showGuaXuGe();
        break;
      case 'bagong':
        showYaoBianGuiLv();
        break;
      case 'xiantian':
        showXiantian();
        break;
    }
  };

  // 弹框相关方法
  const showGuaXuGe = () => {
    showGuaXuGeModal.value = true;
  };

  const closeGuaXuGeModal = () => {
    showGuaXuGeModal.value = false;
  };

  const showYaoBianGuiLv = () => {
    showYaoBianModal.value = true;
  };

  const closeYaoBianModal = () => {
    showYaoBianModal.value = false;
  };

  const showGuaDeGe = () => {
    showGuaDeGeModal.value = true;
  };

  const closeGuaDeGeModal = () => {
    showGuaDeGeModal.value = false;
  };

  const showXiantian = () => {
    showXiantianModal.value = true;
  };

  const closeXiantianModal = () => {
    showXiantianModal.value = false;
  };

  const onHexagramTap = (id: number) => {
    console.log('[hexagram-catalog] 点击卦象，ID:', id);
    const url = `/pages/gua/hexagram/hexagram-detail?id=${id}`;

    uni.navigateTo({
      url,
      success: () => {
        console.log('[hexagram-catalog] 页面跳转成功');
      },
      fail: (error) => {
        console.error('[hexagram-catalog] 页面跳转失败:', error);
      }
    });
  };
</script>

<style lang="scss" scoped>
  .container {
    min-height: 100vh;
    padding: 30rpx;
  }

  .search-section {
    padding: 32rpx;
    margin-bottom: 16rpx;
  }

  .search-box {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border-radius: 24rpx;
    padding: 16rpx 24rpx;
    border: 2rpx solid #e9ecef;
    transition: all 0.3s ease;

    &:focus-within {
      border-color: #865404;
      box-shadow: 0 0 0 4rpx rgba(134, 84, 4, 0.1);
    }

    .search-icon {
      color: #6c757d;
      margin-right: 16rpx;
    }

    .search-input {
      flex: 1;
      font-size: 28rpx;
      color: #333;
      background: transparent;
      border: none;
      outline: none;

      &::placeholder {
        color: #adb5bd;
      }
    }

    .clear-btn {
      padding: 8rpx;
      color: #6c757d;
      cursor: pointer;
    }
  }

  .filter-section {
    padding: 34rpx 0 12rpx;
    margin-bottom: 16rpx;
  }

  .order-buttons {
    display: flex;
    gap: 0;
  }

  .order-button {
    padding: 8rpx 0;
    background: #f8f9fa;
    font-size: 26rpx;
    font-weight: 500;
    text-align: center;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #865404;
    border-right: none; // 移除右边框，避免叠加
    width: 600rpx;

    &:first-child {
      border-radius: 12rpx 0 0 12rpx;
    }

    &:last-child {
      border-radius: 0 12rpx 12rpx 0;
      border-right: 1px solid #865404; // 最后一个恢复右边框
    }

    &.active {
      background: #865404;
      color: white;
    }

    &:not(.active):hover {
      background: #e9ecef;
    }
  }

  .info-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 40rpx;
    padding-left: 16rpx;
    border-left: 4rpx solid #8b4513;

    .info-text {
      flex: 1;
      font-size: 26rpx;
      color: #666;
      line-height: 1;
      margin-right: 16rpx;
    }

    .info-buttons {
      display: flex;
      gap: 12rpx;
    }

    .info-button {
      padding: 8rpx 16rpx;
      background: #8b4513;
      color: white;
      border-radius: 8rpx;
      font-size: 22rpx;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      white-space: nowrap;

      &:hover {
        background: #a0522d;
        transform: translateY(-1rpx);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }

  .stats-section {
    padding: 16rpx 32rpx;
    text-align: center;
  }

  .stats-text {
    font-size: 24rpx;
    color: #6c757d;
  }

  .hexagram-grid {
    padding: 24rpx 0;
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 12rpx;
  }

  .hexagram-card {
    background: white;
    border-radius: 8rpx;
    padding: 14rpx 4rpx 10rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      transform: translateY(-2rpx);
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
    }

    &:active {
      transform: translateY(-1rpx);
    }
  }

  .hexagram-display-container {
    display: flex;
    justify-content: center;
    margin-bottom: 8rpx;
  }

  .hexagram-info {
    text-align: center;
    padding-top:4rpx;
  }

  .hexagram-number {
    font-size: 16rpx;
    color: #865404;
    font-weight: bold;
    margin-bottom: 2rpx;
  }

  .hexagram-name {
    font-size: 20rpx;
    color: #333;
    font-weight: 600;
    line-height: 1.5;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 32rpx;
    text-align: center;
  }

  .empty-icon {
    color: #dee2e6;
    margin-bottom: 24rpx;
  }

  .empty-text {
    font-size: 32rpx;
    color: #6c757d;
    margin-bottom: 12rpx;
  }

  .empty-hint {
    font-size: 24rpx;
    color: #adb5bd;
  }

  // 弹框样式
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 40rpx;
  }

  .modal-content {
    background: #ffffff;
    border-radius: 24rpx;
    width: 100%;
    max-width: 600rpx;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
    animation: modalSlideIn 0.3s ease-out;
  }

  @keyframes modalSlideIn {
    from {
      opacity: 0;
      transform: translateY(-50rpx) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 32rpx 12rpx;
    border-bottom: 1rpx solid #e0e0e0;
  }

  .modal-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #8b4513;
  }

  .modal-close {
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    color: #666;
    cursor: pointer;
    border-radius: 50%;
    transition: all 0.3s ease;

    &:hover {
      background: #f5f5f5;
      color: #333;
    }
  }

  .modal-body {
    padding: 32rpx;
    max-height: 80vh;
  }

  .modal-description {
    margin-top: 24rpx;
    padding: 16rpx;
    background: #f8f9fa;
    border-radius: 12rpx;
    font-size: 26rpx;
    color: #666;
    line-height: 1.6;

    p {
      margin-bottom: 20rpx;
    }
  }

  // 卦序歌样式
  .gua-xu-ge-content {
    .verse-line {
      font-size: 28rpx;
      line-height: 2;
      color: #333;
      margin-bottom: 12rpx;
      padding-left: 24rpx;
      position: relative;

      &::before {
        content: '•';
        position: absolute;
        left: 0;
        color: #8b4513;
        font-weight: bold;
      }

      .gua-name {
        color: #865404;
      }
    }
  }

  // 爻变规律样式
  .yao-bian-content {
    .rule-item {
      margin-bottom: 12rpx;
      padding: 12rpx;
      background: #f8f9fa;
      border-radius: 12rpx;
      border-left: 4rpx solid #8b4513;

      .rule-title {
        font-size: 26rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 8rpx;
      }

      .rule-desc {
        font-size: 26rpx;
        color: #666;
        line-height: 1.5;
      }
    }
  }

  // 卦德歌样式
  .gua-de-ge-content {
    .verse-line {
      font-size: 28rpx;
      line-height: 2;
      color: #333;
      margin-bottom: 12rpx;
      padding-left: 24rpx;
      position: relative;

      &::before {
        content: '•';
        position: absolute;
        left: 0;
        color: #8b4513;
        font-weight: bold;
      }

      .gua-name {
        color: #865404;
      }
    }
  }

  // 先天八卦图样式
  .xiantian-modal {
    .modal-content {
      max-width: 700rpx;
    }
  }

  .xiantian-diagram {
    padding: 40rpx 20rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .bagua-circle {
    position: relative;
    width: 400rpx;
    height: 400rpx;
    border: 2rpx solid #8b4513;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(45deg, #f8f4e9 0%, #fff8e1 100%);
  }

  .bagua-item {
    position: absolute;
    width: 80rpx;
    height: 80rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: white;
    border: 1rpx solid #8b4513;
    border-radius: 50%;
    box-shadow: 0 2rpx 8rpx rgba(139, 69, 19, 0.15);

    .bagua-symbol {
      font-size: 24rpx;
      color: #8b4513;
      margin-bottom: 2rpx;
    }

    .bagua-name {
      font-size: 20rpx;
      font-weight: 600;
      color: #8b4513;
      margin-bottom: 2rpx;
    }

    .bagua-direction {
      font-size: 16rpx;
      color: #666;
    }

    // 八个方位的具体位置
    &.bagua-qian { top: -40rpx; left: 50%; transform: translateX(-50%); } // 南
    &.bagua-dui { top: 20rpx; right: 20rpx; } // 东南
    &.bagua-li { top: 50%; right: -40rpx; transform: translateY(-50%); } // 东
    &.bagua-zhen { bottom: 20rpx; right: 20rpx; } // 东北
    &.bagua-xun { top: 20rpx; left: 20rpx; } // 西南
    &.bagua-kan { top: 50%; left: -40rpx; transform: translateY(-50%); } // 西
    &.bagua-gen { bottom: 20rpx; left: 20rpx; } // 西北
    &.bagua-kun { bottom: -40rpx; left: 50%; transform: translateX(-50%); } // 北
  }
</style>
