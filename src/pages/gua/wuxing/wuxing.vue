<template>
  <PageWrapper title="五行生克" :show-back="true">
    <view class="container">

      <!-- 五行Tab切换 -->
      <view class="wuxing-tabs">
        <view class="layout-tabs">
          <view
            :class="['tab-item', { active: currentTab === 'properties' }]"
            @tap="switchTab('properties')"
          >
            五行特性
          </view>
          <view
            :class="['tab-item', { active: currentTab === 'cycle' }]"
            @tap="switchTab('cycle')"
          >
            五行生克
          </view>
          <view
            :class="['tab-item', { active: currentTab === 'sixiang' }]"
            @tap="switchTab('sixiang')"
          >
            五行四象
          </view>
          <view
            :class="['tab-item', { active: currentTab === 'relations' }]"
            @tap="switchTab('relations')"
          >
            五行归类
          </view>
        </view>
      </view>

          <!-- 五行特性介绍 -->
      <view class="properties-intro" v-if="currentTab === 'properties'">
        <view class="elements-list">
          <view class="element-item element-木">
            <view class="element-header">
              <view class="element-icon">{{ wuxingProperties['木'].icon }}</view>
              <view class="element-name">木</view>
              <view class="element-formula">"木曰曲直"</view>
            </view>
            <view class="element-explanation">
              <view class="formula-desc">曲，屈也；直，伸也。曲直，即能曲能伸之义。</view>
              <view class="element-content">
                木具有生长、能曲能伸、升发的特性。木代表生发力量的性能，标示宇宙万物具有生生不已的功能。凡具有这类特性的事物或现象，都可归属于"木"。
              </view>
            </view>
          </view>

          <view class="element-item element-火">
            <view class="element-header">
              <view class="element-icon">{{ wuxingProperties['火'].icon }}</view>
              <view class="element-name">火</view>
              <view class="element-formula">"火曰炎上"</view>
            </view>
            <view class="element-explanation">
              <view class="formula-desc">炎，热也；上，向上。</view>
              <view class="element-content">
                火具有发热、温暖、向上的特性。火代表生发力量的升华，光辉而热力的性能。凡具有温热、升腾、茂盛性能的事物或现象，均可归属于"火"。
              </view>
            </view>
          </view>

          <view class="element-item element-土">
            <view class="element-header">
              <view class="element-icon">{{ wuxingProperties['土'].icon }}</view>
              <view class="element-name">土</view>
              <view class="element-formula">"土爰稼穑"</view>
            </view>
            <view class="element-explanation">
              <view class="formula-desc">春种曰稼，秋收曰穑，指农作物的播种和收获。</view>
              <view class="element-content">
                土具有载物、生化的特性，故称土载四行，为万物之母。土具生生之义，为世界万物和人类生存之本，"四象五行皆藉土"。五行以土为贵。凡具有生化、承载、受纳性能的事物或现象，皆归属于"土"。
              </view>
            </view>
          </view>

          <view class="element-item element-金">
            <view class="element-header">
              <view class="element-icon">{{ wuxingProperties['金'].icon }}</view>
              <view class="element-name">金</view>
              <view class="element-formula">"金曰从革"</view>
            </view>
            <view class="element-explanation">
              <view class="formula-desc">从，顺从、服从；革，革除、改革、变革。</view>
              <view class="element-content">
                金具有能柔能刚、变革、肃杀的特性。金代表固体的性能，凡物生长之后，必会达到凝固状态，用金以示其坚固性。引申为肃杀、潜能、收敛、清洁之意。凡具有这类性能的事物或现象，均可归属于"金"。
              </view>
            </view>
          </view>

          <view class="element-item element-水">
            <view class="element-header">
              <view class="element-icon">{{ wuxingProperties['水'].icon }}</view>
              <view class="element-name">水</view>
              <view class="element-formula">"水曰润下"</view>
            </view>
            <view class="element-explanation">
              <view class="formula-desc">润，湿润；下，向下。</view>
              <view class="element-content">
                水代表冻结含藏之意，水具有滋润、就下、闭藏的特性。凡具有寒凉、滋润、就下、闭藏性能的事物或现象都可归属于"水"。
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 五行生克图 -->
      <view class="cycle-section" v-if="currentTab === 'cycle'">
        <!-- 五行圆环图 -->
        <WuxingCircle
          :selected-element="selectedElement"
          @select-element="handleSelectElement"
        />

        <!-- 相生相克关系说明 -->
        <view class="relations-explanation">
          <view class="relation-group sheng-group">
            <view class="group-title">
              <view class="title-icon">🌱</view>
              <view class="title-text">相生关系</view>
            </view>
            <view class="group-desc">一种五行对另一种五行有促进、助长和资生的作用</view>
            <view class="relations-list">
              <view
                v-for="relation in shengRelations"
                :key="relation.id"
                class="relation-item sheng-item"
              >
                <view class="relation-content">
                  <view class="relation-symbols">
                    <text class="element-symbol">{{ relation.from.icon }}</text>
                    <text class="arrow">→</text>
                    <text class="element-symbol">{{ relation.to.icon }}</text>
                  </view>
                  <view class="relation-desc">{{ relation.description }}</view>
                </view>
              </view>
            </view>
          </view>

          <view class="relation-group ke-group">
            <view class="group-title">
              <view class="title-icon">⚡</view>
              <view class="title-text">相克关系</view>
            </view>
            <view class="group-desc">一种五行对另一种五行有抑制、制约和克制的作用</view>
            <view class="relations-list">
              <view
                v-for="relation in keRelations"
                :key="relation.id"
                class="relation-item ke-item"
              >
                <view class="relation-content">
                  <view class="relation-symbols">
                    <text class="element-symbol">{{ relation.from.icon }}</text>
                    <text class="arrow">→</text>
                    <text class="element-symbol">{{ relation.to.icon }}</text>
                  </view>
                  <view class="relation-desc">{{ relation.description }}</view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 五行四象 -->
      <view class="sixiang-section" v-if="currentTab === 'sixiang'">

        <view class="concept-cards">
          <view class="concept-card sheng-card">
            <view class="card-header">
              <view class="card-icon">🌱</view>
              <view class="card-title">相生</view>
              <view class="card-subtitle">助长促进</view>
            </view>
            <view class="card-desc">五行强盛时，通过相生关系化解过旺之势</view>
          </view>

          <view class="concept-card cheng-card">
            <view class="card-header">
              <view class="card-icon">🔨</view>
              <view class="card-title">相成</view>
              <view class="card-subtitle">相克成就</view>
            </view>
            <view class="card-desc">通过适度的相克关系，达到相互成就的效果</view>
          </view>

          <view class="concept-card cheng-card-alt">
            <view class="card-header">
              <view class="card-icon">⚡</view>
              <view class="card-title">相乘</view>
              <view class="card-subtitle">过度克制</view>
            </view>
            <view class="card-desc">一方过于强盛，对另一方产生过度的克制</view>
          </view>

          <view class="concept-card wu-card">
            <view class="card-header">
              <view class="card-icon">🔄</view>
              <view class="card-title">相侮</view>
              <view class="card-subtitle">反向克制</view>
            </view>
            <view class="card-desc">被克方过于强盛，反过来克制原本的克制方</view>
          </view>
        </view>

        <view class="wuxing-table">
          <view class="table-header">
            <view class="header-cell element-col"></view>
            <view class="header-cell sheng-col">
              <view class="header-title">相生</view>
            </view>
            <view class="header-cell cheng-col">
              <view class="header-title">相成</view>
            </view>
            <view class="header-cell cheng-col-alt">
              <view class="header-title">相乘</view>
            </view>
            <view class="header-cell wu-col">
              <view class="header-title">相侮</view>
            </view>
          </view>

          <view class="table-row wood-row">
            <view class="table-cell element-cell">
              <view class="element-icon">{{ wuxingProperties['木'].icon }}</view>
              <view class="element-name">木</view>
            </view>
            <view class="table-cell sheng-cell">
              <view class="ancient-text">強木得火，方化其頑</view>
              <view class="modern-explain">木过旺时，得火相助可化解顽固之性</view>
            </view>
            <view class="table-cell cheng-cell">
              <view class="ancient-text">木旺得金，方成棟樑</view>
              <view class="modern-explain">木旺盛时，经金的修剪才能成为栋梁</view>
            </view>
            <view class="table-cell cheng-cell-alt">
              <view class="ancient-text">木弱逢金，必為砍折</view>
              <view class="modern-explain">木衰弱时，遇金克制必然被砍折</view>
            </view>
            <view class="table-cell wu-cell">
              <view class="ancient-text">木能剋土，土重木折</view>
              <view class="modern-explain">木本克土，但土过重时反而折断木</view>
            </view>
          </view>

          <view class="table-row fire-row">
            <view class="table-cell element-cell">
              <view class="element-icon">{{ wuxingProperties['火'].icon }}</view>
              <view class="element-name">火</view>
            </view>
            <view class="table-cell sheng-cell">
              <view class="ancient-text">強火得土，方止其燄</view>
              <view class="modern-explain">火过旺时，得土相助可止其烈焰</view>
            </view>
            <view class="table-cell cheng-cell">
              <view class="ancient-text">火旺得水，方成相濟</view>
              <view class="modern-explain">火旺盛时，得水调节才能相济</view>
            </view>
            <view class="table-cell cheng-cell-alt">
              <view class="ancient-text">火弱逢水，必為熄滅</view>
              <view class="modern-explain">火衰弱时，遇水克制必然熄灭</view>
            </view>
            <view class="table-cell wu-cell">
              <view class="ancient-text">火能剋金，金多火熄</view>
              <view class="modern-explain">火本克金，但金过多时反而熄灭火</view>
            </view>
          </view>

          <view class="table-row earth-row">
            <view class="table-cell element-cell">
              <view class="element-icon">{{ wuxingProperties['土'].icon }}</view>
              <view class="element-name">土</view>
            </view>
            <view class="table-cell sheng-cell">
              <view class="ancient-text">強土得金，方制其害</view>
              <view class="modern-explain">土过旺时，得金相助可制其害</view>
            </view>
            <view class="table-cell cheng-cell">
              <view class="ancient-text">土旺得木，方能疏通</view>
              <view class="modern-explain">土旺盛时，得木疏通才能发挥作用</view>
            </view>
            <view class="table-cell cheng-cell-alt">
              <view class="ancient-text">土衰遇木，必遭傾陷</view>
              <view class="modern-explain">土衰弱时，遇木克制必然倾陷</view>
            </view>
            <view class="table-cell wu-cell">
              <view class="ancient-text">土能剋水，水多土流</view>
              <view class="modern-explain">土本克水，但水过多时反而冲走土</view>
            </view>
          </view>

          <view class="table-row metal-row">
            <view class="table-cell element-cell">
              <view class="element-icon">{{ wuxingProperties['金'].icon }}</view>
              <view class="element-name">金</view>
            </view>
            <view class="table-cell sheng-cell">
              <view class="ancient-text">強金得水，方挫其鋒</view>
              <view class="modern-explain">金过旺时，得水相助可挫其锋芒</view>
            </view>
            <view class="table-cell cheng-cell">
              <view class="ancient-text">金旺得火，方成器皿</view>
              <view class="modern-explain">金旺盛时，经火锻造才能成器皿</view>
            </view>
            <view class="table-cell cheng-cell-alt">
              <view class="ancient-text">金衰遇火，必見銷鎔</view>
              <view class="modern-explain">金衰弱时，遇火克制必然销熔</view>
            </view>
            <view class="table-cell wu-cell">
              <view class="ancient-text">金能剋木，木堅金缺</view>
              <view class="modern-explain">金本克木，但木过坚时反而缺损金</view>
            </view>
          </view>

          <view class="table-row water-row">
            <view class="table-cell element-cell">
              <view class="element-icon">{{ wuxingProperties['水'].icon }}</view>
              <view class="element-name">水</view>
            </view>
            <view class="table-cell sheng-cell">
              <view class="ancient-text">強水得木，方洩其勢</view>
              <view class="modern-explain">水过旺时，得木相助可泄其势</view>
            </view>
            <view class="table-cell cheng-cell">
              <view class="ancient-text">水旺得土，方成池沼</view>
              <view class="modern-explain">水旺盛时，得土约束才能成池沼</view>
            </view>
            <view class="table-cell cheng-cell-alt">
              <view class="ancient-text">水弱逢土，必為淤塞</view>
              <view class="modern-explain">水衰弱时，遇土克制必然淤塞</view>
            </view>
            <view class="table-cell wu-cell">
              <view class="ancient-text">水能剋火，火炎水熱</view>
              <view class="modern-explain">水本克火，但火过炎时反而蒸热水</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 五行归类 -->
      <view class="category-section" v-if="currentTab === 'relations'">
        <!-- 天地人子tab切换 -->
        <view class="category-tabs">
          <view class="sub-tabs">
            <view
              :class="['sub-tab-item', { active: currentCategoryTab === 'tian' }]"
              @tap="switchCategoryTab('tian')"
            >
              <view class="tab-icon">☀️</view>
              <view class="tab-text">天</view>
            </view>
            <view
              :class="['sub-tab-item', { active: currentCategoryTab === 'di' }]"
              @tap="switchCategoryTab('di')"
            >
              <view class="tab-icon">🌍</view>
              <view class="tab-text">地</view>
            </view>
            <view
              :class="['sub-tab-item', { active: currentCategoryTab === 'ren' }]"
              @tap="switchCategoryTab('ren')"
            >
              <view class="tab-icon">👤</view>
              <view class="tab-text">人</view>
            </view>
          </view>

          <!-- 视图模式切换 -->
          <view class="view-mode-tabs">
            <view
              :class="['mode-tab-item', { active: currentViewMode === 'list' }]"
              @tap="switchViewMode('list')"
            >
              <view class="mode-icon">📋</view>
            </view>
            <view
              :class="['mode-tab-item', { active: currentViewMode === 'chart' }]"
              @tap="switchViewMode('chart')"
            >
              <view class="mode-icon">📊</view>
            </view>
          </view>
        </view>

        <!-- 列表视图 -->
        <view v-if="currentViewMode === 'list'" class="list-view">
          <view class="category-table">
            <!-- 表头 -->
            <view class="table-header">
              <view class="header-cell category-col">五行</view>
              <view class="header-cell wood-col">{{ wuxingProperties['木'].icon }} 木</view>
              <view class="header-cell fire-col">{{ wuxingProperties['火'].icon }} 火</view>
              <view class="header-cell earth-col">{{ wuxingProperties['土'].icon }} 土</view>
              <view class="header-cell metal-col">{{ wuxingProperties['金'].icon }} 金</view>
              <view class="header-cell water-col">{{ wuxingProperties['水'].icon }} 水</view>
            </view>

            <!-- 表格内容 -->
            <view class="table-body">
              <view
                v-for="(item, index) in currentCategories"
                :key="index"
                class="table-row"
              >
                <view class="table-cell category-cell">
                  <view class="category-name">{{ item.category }}</view>
                </view>
                <view class="table-cell wood-cell">{{ item.wood }}</view>
                <view class="table-cell fire-cell">{{ item.fire }}</view>
                <view class="table-cell earth-cell">{{ item.earth }}</view>
                <view class="table-cell metal-cell">{{ item.metal }}</view>
                <view class="table-cell water-cell">{{ item.water }}</view>
              </view>
            </view>
          </view>
        </view>

        <!-- 图表视图 -->
        <view v-if="currentViewMode === 'chart'" class="chart-view">
          <view class="chart-container">

            <!-- 分类项目选择器 -->
            <view class="category-selector">
              <view class="selector-options">
                <view
                  v-for="(option, index) in availableCategoryOptions"
                  :key="index"
                  :class="['selector-option', { active: currentCategoryIndex === option.index }]"
                  @tap="switchCategoryIndex(option.index)"
                >
                  {{ option.name }}
                </view>
              </view>
            </view>

            <!-- 五行圆环图 -->
            <WuxingCircle
              :selected-element="selectedElement"
              :custom-elements="customWuxingElements"
              @select-element="handleSelectElement"
            />
          </view>
        </view>
      </view>
    </view>
  </PageWrapper>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';
  import PageWrapper from '@/components/PageWrapper.vue';
  import WuxingCircle from '@/components/WuxingCircle.vue';
  import {
    shengRelations,
    keRelations,
    wuxingCategories,
    wuxingProperties
  } from '@/utils/wuxing';

  // 响应式数据
  const selectedElement = ref('');
  const currentTab = ref('properties');
  const currentCategoryTab = ref('tian'); // 五行归类子tab：tian, di, ren
  const currentViewMode = ref('list'); // 视图模式：list, chart
  const currentCategoryIndex = ref(4); // 当前选择的分类项目索引，默认为四时

  // 计算属性：分离天、地、人三部分数据
  const tianCategories = computed(() => {
    return wuxingCategories.filter((_, index) => index <= 10); 
  });

  const diCategories = computed(() => {
    return wuxingCategories.filter((_, index) => index >= 11 && index <= 18);
  });

  const renCategories = computed(() => {
    return wuxingCategories.filter((_, index) => index >= 19); 
  });

  // 根据当前分类标签获取可用的分类选项
  const availableCategoryOptions = computed(() => {
    let startIndex = 0;
    let endIndex = 10;

    switch (currentCategoryTab.value) {
      case 'tian':
        startIndex = 0;
        endIndex = 10; // 更新为新的天部范围
        break;
      case 'di':
        startIndex = 11;
        endIndex = 18; // 更新为新的地部范围
        break;
      case 'ren':
        startIndex = 19;
        endIndex = wuxingCategories.length - 1;
        break;
    }

    return wuxingCategories
      .slice(startIndex, endIndex + 1)
      .map((category, localIndex) => ({
        name: category.category || '未命名',
        index: startIndex + localIndex
      }))
      .filter(option => option.name !== '未命名' && option.name !== ''); // 过滤掉空名称
  });

  // 当前显示的分类数据
  const currentCategories = computed(() => {
    switch (currentCategoryTab.value) {
      case 'tian':
        return tianCategories.value;
      case 'di':
        return diCategories.value;
      case 'ren':
        return renCategories.value;
      default:
        return tianCategories.value;
    }
  });

  // 五行元素数组，从 wuxingProperties 动态生成
  const wuxingElementsArray = computed(() => {
    return Object.keys(wuxingProperties).map(name => ({
      name,
      icon: wuxingProperties[name].icon
    }));
  });

  // 根据当前分类标签生成自定义五行元素数据
  const customWuxingElements = computed(() => {
    // 使用当前选择的分类项目索引
    const category = wuxingCategories[currentCategoryIndex.value];
    const elementKeys = ['wood', 'fire', 'earth', 'metal', 'water'] as const;

    return wuxingElementsArray.value.map((element, index) => ({
      name: element.name,
      icon: category[elementKeys[index] as keyof typeof category],
    }));
  });

  // 方法
  const handleSelectElement = (elementName: string) => {
    selectedElement.value = selectedElement.value === elementName ? '' : elementName;
  };

  const switchTab = (tab: string) => {
    currentTab.value = tab;
    // 切换到五行归类tab时，重置子tab和视图模式
    if (tab === 'relations') {
      currentCategoryTab.value = 'tian';
      currentViewMode.value = 'list';
    }
  };

  const switchCategoryTab = (tab: string) => {
    currentCategoryTab.value = tab;
    // 保持当前视图模式，不重置

    // 重置分类索引到对应分类的第一个有效项目
    switch (tab) {
      case 'tian':
        currentCategoryIndex.value = 3; // 四时（天部第4项）
        break;
      case 'di':
        currentCategoryIndex.value = 13; // 五色（地部第2项，五音索引11可能不够直观）
        break;
      case 'ren':
        currentCategoryIndex.value = 19; // 五脏（人部第1项）
        break;
    }
  };

  const switchViewMode = (mode: string) => {
    currentViewMode.value = mode;
  };

  const switchCategoryIndex = (index: number) => {
    currentCategoryIndex.value = index;
  };
</script>

<style lang="scss" scoped>
  .container {
    padding: 30rpx;
    background: #f8f4e9;
  }

  // 五行Tab样式
  .wuxing-tabs {
    margin-bottom: 40rpx;

    .layout-tabs {
      display: flex;
      justify-content: center;
      gap: 0;
      background: transparent;
      border-radius: 0;
      padding: 12rpx 2rpx 6rpx 2rpx;
      box-shadow: none;
      border: none;

      .tab-item {
        flex: 1;
        padding: 12rpx 24rpx;
        text-align: center;
        font-size: 26rpx;
        font-weight: 500;
        background: #f8f9fa;
        color: #6c757d;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid #865404;
        border-right: none; // 移除右边框，避免叠加

        &:first-child {
          border-radius: 12rpx 0 0 12rpx;
        }

        &:last-child {
          border-radius: 0 12rpx 12rpx 0;
          border-right: 1px solid #865404; // 最后一个恢复右边框
        }

        &.active {
          background: #865404;
          color: white;
        }

        &:not(.active):hover {
          background: #e9ecef;
        }

        &:active {
          transform: scale(0.98);
        }
      }
    }
  }

  // 五行生克循环样式
  .cycle-section {
    margin-top: -40rpx;
    margin-bottom: 60rpx;

    .relations-explanation {
      padding: 0 20rpx;

      .relation-group {
        margin-bottom: 20rpx;
        background: white;
        border-radius: 16rpx;
        padding: 30rpx;
        box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

        .group-title {
          display: flex;
          align-items: center;
          margin-bottom: 16rpx;

          .title-icon {
            font-size: 32rpx;
            margin-right: 12rpx;
          }

          .title-text {
            font-size: 32rpx;
            font-weight: 600;
          }
        }

        .group-desc {
          font-size: 24rpx;
          color: #666;
          margin-bottom: 24rpx;
          padding-left: 44rpx;
        }

        .relations-list {
          .relation-item {
            margin-bottom: 12rpx;

            &:last-child {
              margin-bottom: 0;
            }

            .relation-content {
              display: flex;
              align-items: center;
              padding: 16rpx 20rpx;
              border-radius: 12rpx;

              .relation-symbols {
                display: flex;
                align-items: center;
                margin-right: 20rpx;
                opacity: 0.7;

                .element-symbol {
                  font-size: 28rpx;
                }

                .arrow {
                  font-size: 24rpx;
                  margin: 0 12rpx;
                  font-weight: 600;
                }
              }

              .relation-desc {
                flex: 1;
                font-size: 26rpx;
                color: #555;
              }
            }
          }
        }

        &.sheng-group {
          border-left: 3rpx solid #4CAF50;

          .group-title .title-text {
            color: #2E7D32;
          }

          .relation-item .relation-content {
            background: rgba(76, 175, 80, 0.05);

            .arrow {
              color: #4CAF50;
            }
          }
        }

        &.ke-group {
          border-left: 3rpx solid #FF9800;

          .group-title .title-text {
            color: #F57C00;
          }

          .relation-item .relation-content {
            background: rgba(255, 152, 0, 0.05);

            .arrow {
              color: #FF9800;
            }
          }
        }
      }
    }
  }

  // 五行四象样式
  .sixiang-section {
    margin-bottom: 60rpx;

    .section-intro {
      background: rgba(248, 248, 250, 0.6);
      border: 1rpx solid rgba(139, 69, 19, 0.08);
      border-radius: 16rpx;
      padding: 32rpx 28rpx;
      box-shadow: 0 2rpx 12rpx rgba(139, 69, 19, 0.04);
      backdrop-filter: blur(10rpx);

      .intro-text {
        font-size: 24rpx;
        color: rgba(100, 100, 100, 0.75);
        line-height: 1.8;
        text-align: left;
        letter-spacing: 0.5rpx;
        margin-top: 16rpx;
      }
    }

    .concept-cards {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16rpx;
      margin-bottom: 30rpx;

      .concept-card {
        background: white;
        border-radius: 12rpx;
        padding: 20rpx;
        box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
        border-left: 4rpx solid #ddd;

        .card-header {
          display: flex;
          align-items: center;
          margin-bottom: 8rpx;

          .card-icon {
            font-size: 24rpx;
            margin-right: 8rpx;
          }

          .card-title {
            font-size: 24rpx;
            font-weight: 600;
            margin-right: 8rpx;
          }

          .card-subtitle {
            font-size: 20rpx;
            color: #888;
          }
        }

        .card-desc {
          font-size: 22rpx;
          color: #666;
          line-height: 1.4;
        }

        &.sheng-card {
          border-left-color: #4CAF50;
          .card-title { color: #2E7D32; }
        }

        &.cheng-card {
          border-left-color: #2196F3;
          .card-title { color: #1565C0; }
        }

        &.cheng-card-alt {
          border-left-color: #FF9800;
          .card-title { color: #F57C00; }
        }

        &.wu-card {
          border-left-color: #9C27B0;
          .card-title { color: #7B1FA2; }
        }
      }
    }

    .wuxing-table {
      background: white;
      border-radius: 16rpx;
      overflow: hidden;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
      margin-bottom: 30rpx;

      .table-header {
          display: flex;

          .header-cell {
            flex: 1;
            padding: 20rpx 12rpx 8rpx;
            text-align: center;
            color: #6d4c1a;
              border-bottom: 1rpx solid rgba(255, 152, 0, 0.3);

            &:first-child {
              border-bottom: none;
            }

            &:last-child {
              border-right: none;
            }

            &.element-col {
              flex: 0.5;
              font-size: 24rpx;
              font-weight: 600;
              background: #f8f9fa;
              border-right: 1rpx solid #e9ecef;
            }

            .header-title {
              font-size: 22rpx;
              font-weight: 600;
              margin-bottom: 4rpx;
            }

            &.sheng-col {
              background: rgba(76, 175, 80, 0.1);
              border-left: 1rpx solid rgba(76, 175, 80, 0.3);
            }

            &.cheng-col {
              background: rgba(33, 150, 243, 0.1);
              border-left: 1rpx solid rgba(33, 150, 243, 0.3);
            }

            &.cheng-col-alt {
              background: rgba(255, 152, 0, 0.1);
              border-left: 1rpx solid rgba(255, 152, 0, 0.3);
            }

            &.wu-col {
              background: rgba(156, 39, 176, 0.1);
              border-left: 1rpx solid rgba(156, 39, 176, 0.3);
            }
          }
        }

        .table-row {
          display: flex;
          border-bottom: 1rpx solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .table-cell {
            flex: 1;
            padding: 20rpx 12rpx;
            text-align: left;
            border-right: 1rpx solid #f0f0f0;

            &:last-child {
              border-right: none;
            }

            &.element-cell {
              flex: 0.5;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              font-weight: 600;
              text-align: center;
              background: #f8f9fa;
              border-right: 1rpx solid #e9ecef;

              .element-icon {
                font-size: 28rpx;
                margin-bottom: 8rpx;
              }

              .element-name {
                font-size: 26rpx;
                color: #333;
              }
            }

            .ancient-text {
              font-size: 22rpx;
              color: #8b4513;
              font-weight: 600;
              margin-bottom: 8rpx;
              line-height: 1.4;
            }

            .modern-explain {
              font-size: 20rpx;
              color: #666;
              line-height: 1.5;
            }

            &.sheng-cell {
              background: rgba(76, 175, 80, 0.02);
              border-left: 1rpx solid rgba(76, 175, 80, 0.3);
            }

            &.cheng-cell {
              background: rgba(33, 150, 243, 0.02);
              border-left: 1rpx solid rgba(33, 150, 243, 0.3);
            }

            &.cheng-cell-alt {
              background: rgba(255, 152, 0, 0.02);
              border-left: 1rpx solid rgba(255, 152, 0, 0.3);
            }

            &.wu-cell {
              background: rgba(156, 39, 176, 0.02);
              border-left: 1rpx solid rgba(156, 39, 176, 0.3);
            }
          }

          // 五行元素行的背景色
          &.wood-row {
            background: rgba(76, 175, 80, 0.03);
          }

          &.fire-row {
            background: rgba(244, 67, 54, 0.03);
          }

          &.earth-row {
            background: rgba(139, 69, 19, 0.03);
          }

          &.metal-row {
            background: rgba(255, 193, 7, 0.03);
          }

          &.water-row {
            background: rgba(33, 150, 243, 0.03);
          }
        }
      }
    }

    .table-explanations {
      .explanation-item {
        background: white;
        border-radius: 12rpx;
        padding: 24rpx;
        margin-bottom: 16rpx;
        box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
        border-left: 4rpx solid #8b4513;

        &:last-child {
          margin-bottom: 0;
        }

        .explanation-title {
          font-size: 26rpx;
          font-weight: 600;
          color: #8b4513;
          margin-bottom: 8rpx;
        }

        .explanation-text {
          font-size: 24rpx;
          color: #666;
          line-height: 1.6;
        }
      }
    }
  

  // 五行特性介绍样式
  .properties-intro {
    margin-bottom: 60rpx;

    .intro-description {
      text-align: center;
      margin-bottom: 40rpx;

      .desc-text {
        font-size: 28rpx;
        color: #666;
        line-height: 1.6;
        padding: 0 20rpx;
        text-align: left;
      }
    }

    .elements-list {
      display: flex;
      flex-direction: column;
      gap: 24rpx;
      padding: 0 10rpx;

      .element-item {
        background: white;
        border-radius: 16rpx;
        padding: 30rpx;
        box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
        border-left: 4rpx solid #ddd;

        .element-header {
          display: flex;
          align-items: center;
          margin-bottom: 20rpx;

          .element-icon {
            font-size: 40rpx;
            margin-right: 16rpx;
          }

          .element-name {
            font-size: 36rpx;
            font-weight: 700;
            color: #333;
            margin-right: 16rpx;
          }

          .element-formula {
            font-size: 26rpx;
            font-weight: 600;
            color: #8b4513;
            font-style: italic;
            flex: 1;
          }
        }

        .element-explanation {
          .formula-desc {
            font-size: 24rpx;
            color: #888;
            margin-bottom: 12rpx;
            font-style: italic;
            padding-left: 20rpx;
            border-left: 3rpx solid #eee;
          }

          .element-content {
            font-size: 26rpx;
            color: #555;
            line-height: 1.6;
            text-align: justify;
          }
        }

        // 五行元素特定样式
        &.element-木 {
          border-left-color: #4CAF50;

          .element-formula {
            color: #2E7D32;
          }

          .formula-desc {
            border-left-color: #4CAF50;
          }
        }

        &.element-火 {
          border-left-color: #F44336;

          .element-formula {
            color: #C62828;
          }

          .formula-desc {
            border-left-color: #F44336;
          }
        }

        &.element-土 {
          border-left-color: #8b4513;

          .element-formula {
            color: #5d2f0a;
          }

          .formula-desc {
            border-left-color: #8b4513;
          }
        }

        &.element-金 {
          border-left-color: #FFC107;

          .element-formula {
            color: #F57F17;
          }

          .formula-desc {
            border-left-color: #FFC107;
          }
        }

        &.element-水 {
          border-left-color: #2196F3;

          .element-formula {
            color: #1565C0;
          }

          .formula-desc {
            border-left-color: #2196F3;
          }
        }
      }
    }
  }

  // 页面标题
  .page-header {
    text-align: center;
    margin-bottom: 60rpx;
    padding: 40rpx 0;

    .title-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 20rpx;

      .title {
        font-size: 52rpx;
        font-weight: 800;
        background: linear-gradient(135deg, #8b4513 0%, #a0522d 50%, #865404 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-right: 16rpx;
      }

      .title-decoration {
        font-size: 32rpx;
        color: #d4af37;
        animation: sparkle 2s ease-in-out infinite;
      }
    }

    .subtitle {
      font-size: 28rpx;
      color: #6d4c1a;
      line-height: 1.6;
      opacity: 0.8;
    }
  }

  @keyframes sparkle {
    0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.7; }
    50% { transform: scale(1.2) rotate(180deg); opacity: 1; }
  }



  // 关系说明区域
  .relationship-section {
    margin-bottom: 60rpx;

    .empty-content {
      text-align: center;
      padding: 80rpx 40rpx;

      .empty-text {
        font-size: 28rpx;
        color: #999;
        background: rgba(255, 255, 255, 0.8);
        padding: 20rpx 40rpx;
        border-radius: 50rpx;
        border: 2rpx dashed #ddd;
      }
    }
  }

  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 32rpx;

    .title-text {
      font-size: 32rpx;
      font-weight: 600;
      color: #6d4c1a;
      margin-right: 16rpx;
    }

    .title-line {
      flex: 1;
      height: 2rpx;
      background: linear-gradient(90deg, #d4af37 0%, transparent 100%);
    }
  }

  .relationship-tabs {
    display: flex;
    background: white;
    border-radius: 16rpx;
    padding: 8rpx;
    margin-bottom: 32rpx;
    box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.05);

    .tab-item {
      flex: 1;
      text-align: center;
      padding: 20rpx;
      border-radius: 12rpx;
      font-size: 28rpx;
      font-weight: 500;
      color: #a97c50;
      transition: all 0.3s ease;

      &.active {
        background: #8b4513;
        color: white;
        box-shadow: 0 4rpx 12rpx rgba(139, 69, 19, 0.3);
      }
    }
  }

  .relationship-content {
    .relationship-desc {
      background: white;
      padding: 24rpx 32rpx;
      border-radius: 16rpx;
      margin-bottom: 24rpx;
      font-size: 26rpx;
      color: #6d4c1a;
      line-height: 1.6;
      box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.05);
    }

    .relationship-list {
      .relation-item {
        display: flex;
        align-items: center;
        background: white;
        padding: 32rpx;
        border-radius: 20rpx;
        margin-bottom: 16rpx;
        box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.05);
        transition: all 0.3s ease;

        &:active {
          transform: translateY(2rpx);
          box-shadow: 0 2rpx 8rpx rgba(139, 69, 19, 0.1);
        }

        .relation-from, .relation-to {
          display: flex;
          align-items: center;
          flex: 1;

          .element-badge {
            width: 60rpx;
            height: 60rpx;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16rpx;
            font-size: 32rpx;
            background: linear-gradient(135deg, #f8f4e9 0%, #f0ead6 100%);

            &.wood { border: 3rpx solid #4CAF50; }
            &.fire { border: 3rpx solid #F44336; }
            &.earth { border: 3rpx solid #FF9800; }
            &.metal { border: 3rpx solid #FFC107; }
            &.water { border: 3rpx solid #2196F3; }
          }

          .element-text {
            font-size: 28rpx;
            font-weight: 600;
            color: #6d4c1a;
          }
        }

        .relation-arrow {
          font-size: 32rpx;
          color: #8b4513;
          margin: 0 24rpx;
          font-weight: bold;
        }

        .relation-desc {
          flex: 2;
          font-size: 24rpx;
          color: #a97c50;
          line-height: 1.5;
          text-align: right;
        }
      }
    }
  }

  // 五行属性详情
  .properties-section {
    margin-bottom: 60rpx;

    .property-card {
      background: white;
      border-radius: 24rpx;
      padding: 40rpx;
      box-shadow: 0 8rpx 24rpx rgba(139, 69, 19, 0.08);
      border-left: 6rpx solid #8b4513;

      .property-header {
        display: flex;
        align-items: center;
        margin-bottom: 32rpx;
        padding-bottom: 24rpx;
        border-bottom: 2rpx solid #f0ead6;

        .property-icon {
          width: 100rpx;
          height: 100rpx;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 48rpx;
          background: linear-gradient(135deg, #f8f4e9 0%, #f0ead6 100%);
          margin-right: 24rpx;
          box-shadow: 0 4rpx 12rpx rgba(139, 69, 19, 0.1);
        }

        .property-info {
          flex: 1;

          .property-name {
            font-size: 36rpx;
            font-weight: 700;
            color: #6d4c1a;
            margin-bottom: 8rpx;
          }

          .property-nature {
            font-size: 26rpx;
            color: #a97c50;
            line-height: 1.5;
          }
        }
      }

      .property-details {
        .detail-row {
          display: flex;
          align-items: center;
          padding: 20rpx 0;
          border-bottom: 1rpx solid #f5f3f0;

          &:last-child {
            border-bottom: none;
          }

          .detail-label {
            width: 120rpx;
            font-size: 26rpx;
            font-weight: 600;
            color: #8b4513;
          }

          .detail-value {
            flex: 1;
            font-size: 26rpx;
            color: #6d4c1a;
          }
        }
      }
    }
  }

  // 应用说明
  .application-section {
    .application-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 20rpx;
    }

    .app-item {
      background: white;
      border-radius: 20rpx;
      padding: 32rpx 24rpx;
      text-align: center;
      box-shadow: 0 6rpx 20rpx rgba(139, 69, 19, 0.06);
      border: 2rpx solid rgba(139, 69, 19, 0.03);
      transition: all 0.3s ease;

      &:active {
        transform: translateY(2rpx) scale(0.95);
        box-shadow: 0 3rpx 10rpx rgba(139, 69, 19, 0.1);
      }

      .app-icon {
        font-size: 48rpx;
        margin-bottom: 16rpx;
        filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
      }

      .app-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #6d4c1a;
        margin-bottom: 12rpx;
      }

      .app-desc {
        font-size: 24rpx;
        color: #a97c50;
        line-height: 1.4;
      }
    }
  }

  // 响应式适配
  @media (max-width: 750rpx) {
    .wuxing-circle .circle-container {
      width: 320rpx;
      height: 320rpx;
    }

    .wuxing-circle .element-item {
      width: 100rpx;
      height: 100rpx;

      .element-icon {
        font-size: 40rpx;
      }

      .element-name {
        font-size: 22rpx;
      }
    }

    .relationship-content .relation-item {
      flex-direction: column;
      text-align: center;

      .relation-from, .relation-to {
        justify-content: center;
        margin-bottom: 16rpx;
      }

      .relation-arrow {
        margin: 16rpx 0;
        transform: rotate(90deg);
      }

      .relation-desc {
        text-align: center;
        margin-top: 16rpx;
      }
    }

    .application-grid {
      grid-template-columns: 1fr;
      gap: 16rpx;
    }
  }

  // 五行归类样式
  .category-section {
    // 图表视图中的分类选择器样式
    .chart-view {
      .category-selector {
        margin: 18rpx 0 32rpx 18rpx;

        .selector-options {
          display: flex;
          flex-wrap: wrap;
          justify-content: flex-start;
          gap: 18rpx;

          .selector-option {
            min-width: 80rpx;
            padding: 14rpx 20rpx;
            background: #faf8f5;
            border: 2rpx solid #e8ddd4;
            border-radius: 16rpx;
            font-size: 22rpx;
            color: #8b4513;
            cursor: pointer;
            transition: all 0.25s ease;
            text-align: center;
            font-weight: 500;
            box-shadow: 0 2rpx 6rpx rgba(139, 69, 19, 0.08);

            // 增加点击区域
            position: relative;

            &::before {
              content: '';
              position: absolute;
              top: -4rpx;
              left: -4rpx;
              right: -4rpx;
              bottom: -4rpx;
              border-radius: 20rpx;
            }

            &.active {
              background: linear-gradient(135deg, #8b4513, #a0522d);
              color: #ffffff;
              border-color: #8b4513;
              font-weight: 600;
              transform: translateY(-1rpx);
              box-shadow: 0 4rpx 12rpx rgba(139, 69, 19, 0.25);
            }

            &:not(.active):active {
              transform: scale(0.97);
              background: #f5f1ec;
              border-color: #d4c4b0;
            }
          }
        }
      }
    }

    .category-description {
      background: white;
      border-radius: 16rpx;
      padding: 24rpx;
      margin-bottom: 30rpx;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

      text {
        font-size: 26rpx;
        line-height: 1.6;
        color: #6d4c1a;
      }
    }

    .category-tabs {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30rpx;
      gap: 20rpx;

      .sub-tabs {
        display: flex;
        gap: 8rpx;
        margin-bottom: 8rpx;

        .sub-tab-item {
          display: flex;
          align-items: center;
          padding: 12rpx 20rpx;
          border-radius: 24rpx;
          cursor: pointer;
          transition: all 0.2s ease;
          background: rgba(139, 69, 19, 0.08);
          border: 1rpx solid rgba(139, 69, 19, 0.15);
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
          }

          &:active::before {
            left: 100%;
          }

          &.active {
            background: rgba(139, 69, 19, 0.12);
            border: 1rpx solid #8b4513;
            box-shadow: inset 0 2rpx 4rpx rgba(139, 69, 19, 0.1);
          }

          .tab-icon {
            font-size: 20rpx;
            margin-right: 8rpx;
            opacity: 0.8;
            transition: opacity 0.2s ease;
          }

          .tab-text {
            font-size: 22rpx;
            font-weight: 500;
            color: #8b4513;
            opacity: 0.8;
            transition: opacity 0.2s ease;
          }

          &.active {
            .tab-icon,
            .tab-text {
              opacity: 1;
            }

            .tab-text {
              font-weight: 600;
            }
          }
        }
      }

      .view-mode-tabs {
        display: flex;
        background: white;
        border-radius: 12rpx;
        padding: 3rpx;
        box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);

        .mode-tab-item {
          display: flex;
          align-items: center;
          padding: 6rpx 16rpx 6rpx 20rpx;
          border-radius: 8rpx;
          cursor: pointer;
          transition: all 0.3s ease;
          background: transparent;
          opacity: 0.6;

          &.active {
            border: 1px solid #8b4513;
            opacity: 1;
          }

          .mode-icon {
            font-size: 20rpx;
            margin-right: 6rpx;
          }
        }
      }
    }

    .category-table {
      background: white;
      border-radius: 16rpx;
      overflow: hidden;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
      margin-bottom: 30rpx;

      .table-header {
        display: flex;
        background: #fafbfc;
        // border-bottom: 2rpx solid #e1e5e9;

        .header-cell {
          flex: 1;
          padding: 24rpx 16rpx;
          text-align: center;
          color: #495057;
          border-right: 1rpx solid #dee2e6;
          font-size: 24rpx;
          font-weight: 600;
          position: relative;

          &:last-child {
            border-right: none;
          }

          &.category-col {
            flex: 0.8;
            background: #f8f9fa;
          }

          &.wood-col {
            flex: 0.9;
            &::after {
              content: '';
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;
              height: 4rpx;
              background: #4caf50;
            }
          }

          &.fire-col {
            flex: 0.9;
            &::after {
              content: '';
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;
              height: 4rpx;
              background: #f44336;
            }
          }

          &.earth-col {
            flex: 1.3;
            &::after {
              content: '';
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;
              height: 4rpx;
              background: #ff9800;
            }
          }

          &.metal-col {
            flex: 0.9;
            &::after {
              content: '';
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;
              height: 4rpx;
              background: #9e9e9e;
            }
          }

          &.water-col {
            flex: 0.9;
            &::after {
              content: '';
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;
              height: 4rpx;
              background: #2196f3;
            }
          }
        }
      }

      .table-body {
        .table-row {
          display: flex;
          border-bottom: 1rpx solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          &:nth-child(even) {
            background: #fafafa;
          }

          .table-cell {
            flex: 1;
            padding: 20rpx 16rpx;
            text-align: center;
            font-size: 24rpx;
            border-right: 1rpx solid #dee2e6;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 80rpx;

            &:last-child {
              border-right: none;
            }

            &.category-cell {
              flex: 0.8;
              flex-direction: column;
              background: #f8f9fa;
              text-align: center;

              .category-name {
                font-weight: 600;
                color: #495057;
                font-size: 22rpx;
              }
            }

            &.wood-cell {
              flex: 0.9;
              color: #2e7d32;
              font-weight: 500;
            }

            &.fire-cell {
              flex: 0.9;
              color: #c62828;
              font-weight: 500;
            }

            &.earth-cell {
              flex: 1.3;
              color: #ef6c00;
              font-weight: 500;
              white-space: nowrap;
            }

            &.metal-cell {
              flex: 0.9;
              color: #616161;
              font-weight: 500;
            }

            &.water-cell {
              flex: 0.9;
              color: #1565c0;
              font-weight: 500;
            }


          }
        }
      }
    }

    .category-explanation {
      background: white;
      border-radius: 16rpx;
      padding: 24rpx;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

      .explanation-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #6d4c1a;
        margin-bottom: 20rpx;
        text-align: center;
      }

      .explanation-content {
        .explanation-item {
          margin-bottom: 16rpx;
          display: flex;
          align-items: flex-start;

          &:last-child {
            margin-bottom: 0;
          }

          .item-title {
            font-size: 24rpx;
            font-weight: 600;
            color: #8b4513;
            min-width: 60rpx;
            margin-right: 12rpx;
          }

          .item-desc {
            font-size: 24rpx;
            line-height: 1.6;
            color: #666;
            flex: 1;
          }
        }
      }
    }

    // 图表视图样式
    .chart-view {
      .chart-container {
        background: white;
        border-radius: 16rpx;
        padding: 24rpx;
        box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

        .chart-title {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 30rpx;
          padding: 16rpx;
          background: linear-gradient(135deg, #f8f9fa, #e9ecef);
          border-radius: 12rpx;

          .chart-icon {
            font-size: 28rpx;
            margin-right: 12rpx;
          }

          .chart-text {
            font-size: 26rpx;
            font-weight: 600;
            color: #6d4c1a;
          }
        }
      }
    }
  }
</style>
