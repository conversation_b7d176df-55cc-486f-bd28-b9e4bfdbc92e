<template>
  <PageWrapper title="开发选项" :show-logo="false">
    <view class="container">
      <!-- 警告提示 -->
      <view class="warning-card">
        <view class="warning-icon">⚠️</view>
        <view class="warning-content">
          <text class="warning-title">开发选项</text>
          <text class="warning-text">这些功能仅用于开发和调试，请谨慎使用</text>
        </view>
      </view>

      <!-- 开发工具菜单 -->
      <view class="function-menu-section">
        <view class="menu-grid">
          <view
            v-for="menu in menuItems"
            :key="menu.id"
            class="menu-card"
            @tap="handleMenuClick(menu)"
          >
            <view class="menu-icon">
              <text class="icon-text">{{ menu.icon }}</text>
            </view>
            <view class="menu-content">
              <text class="menu-title">{{ menu.title }}</text>
              <text class="menu-desc">{{ menu.description }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 系统信息 -->
      <view class="section">
        <text class="section-title">系统信息</text>

        <view class="info-item">
          <text class="info-label">用户ID:</text>
          <text class="info-value">{{ userInfo.userId || '未知' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">总积分:</text>
          <text class="info-value">{{ userInfo.totalScore || 0 }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">经验值:</text>
          <text class="info-value">{{ userInfo.experience || 0 }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">等级:</text>
          <text class="info-value">{{ userInfo.level || 1 }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">游戏次数:</text>
          <text class="info-value">{{ totalGamesPlayed }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">卦象缓存:</text>
          <text class="info-value">{{ cacheInfo.cachedCount }}/64 ({{ cacheInfo.totalSize }}KB)</text>
        </view>

        <view class="info-item">
          <text class="info-label">CDN域名:</text>
          <text class="info-value">{{ cdnDomain }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">CDN域名:</text>
          <text class="info-value">{{ cdnDomain }}</text>
        </view>
      </view>



      <!-- 调试日志 -->
      <view v-if="debugLogs.length > 0" class="section">
        <text class="section-title">调试日志</text>
        <view class="log-container">
          <text v-for="(log, index) in debugLogs" :key="index" class="log-item" :class="log.type">
            [{{ log.time }}] {{ log.message }}
          </text>
        </view>
        <button class="option-btn secondary" @tap="clearLogs">清除日志</button>
      </view>
    </view>
  </PageWrapper>
</template>

<script lang="ts">
  import { defineComponent, ref, onMounted, onUnmounted, computed } from 'vue';
  import PageWrapper from '@/components/PageWrapper.vue';
  import { userProfileManager } from '@/games/shared/services/user/user-profile';
  import { hexagramCache, storage } from '@/utils/storage';
  import { loadHexagramContent } from '@/utils/html-loader';
  import { CDN_DOMAIN } from '@/config';
  // import { gameDataAccessor } from '@/games/shared/services/user/user-storage';

  interface DebugLog {
    time: string;
    type: 'info' | 'success' | 'warning' | 'error';
    message: string;
  }

  interface MenuItem {
    id: string;
    icon: string;
    title: string;
    description: string;
    action: string;
  }

  export default defineComponent({
    name: 'DeveloperOptions',
    components: {
      PageWrapper,
    },
    setup() {
      const userInfo = ref<Record<string, unknown>>({});
      const debugLogs = ref<DebugLog[]>([]);

      // 开发工具菜单数据
      const menuItems = ref<MenuItem[]>([
        {
          id: 'data-management',
          icon: '💾',
          title: '缓存数据管理',
          description: '管理用户数据、卦象数据、游戏数据等缓存',
          action: 'navigateToDataManagement',
        },
        {
          id: 'button-styles',
          icon: '🎨',
          title: '按钮样式',
          description: '查看和测试各种按钮样式效果',
          action: 'navigateToButtonStyles',
        },
        {
          id: 'nav-debugger',
          icon: '🧭',
          title: '导航栏调试',
          description: '调试和测试导航栏相关功能',
          action: 'navigateToNavDebugger',
        },
        {
          id: 'hexagram-sizes',
          icon: '📐',
          title: '卦象尺寸',
          description: '查看 HexagramDisplay 组件的不同尺寸效果',
          action: 'navigateToHexagramSizes',
        },
      ]);

      // 计算总游戏次数
      const totalGamesPlayed = computed(() => {
        if (!userInfo.value.gamesPlayed) return 0;
        return Object.values((userInfo.value.gamesPlayed as Record<string, number>) || {}).reduce(
          (sum: number, count: number) => sum + count,
          0
        );
      });

      // 计算缓存信息
      const cacheInfo = computed(() => {
        try {
          return hexagramCache.getStats();
        } catch (error) {
          return { cachedCount: 0, totalSize: 0, expiredCount: 0, hitRate: 0 };
        }
      });

      // CDN域名
      const cdnDomain = computed(() => CDN_DOMAIN);

      // 添加日志
      const addLog = (type: DebugLog['type'], message: string) => {
        const now = new Date();
        const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
        debugLogs.value.unshift({ time, type, message });

        // 限制日志数量
        if (debugLogs.value.length > 50) {
          debugLogs.value = debugLogs.value.slice(0, 50);
        }
      };

      // 加载用户数据
      const loadUserData = async () => {
        try {
          const profile = userProfileManager.getCurrentProfile();
          if (profile) {
            userInfo.value = { ...profile };
            addLog('info', '用户数据加载成功');
          } else {
            addLog('warning', '未找到用户数据');
          }
        } catch (error) {
          addLog('error', `加载用户数据失败: ${error}`);
        }
      };

      // 查看用户数据
      const viewUserData = () => {
        const profile = userProfileManager.getCurrentProfile();
        if (profile) {
          // const dataStr = JSON.stringify(profile, null, 2);
          console.log('用户数据:', profile);
          addLog('info', '用户数据已输出到控制台');

          // 显示简化的数据
          uni.showModal({
            title: '用户数据',
            content: `用户ID: ${profile.userId}\n总积分: ${profile.totalScore}\n经验值: ${profile.experience}\n等级: ${profile.level}`,
            showCancel: false,
          });
        } else {
          addLog('error', '未找到用户数据');
        }
      };

      // 重置用户数据
      const resetUserData = () => {
        uni.showModal({
          title: '确认重置',
          content: '这将清除所有用户数据，包括积分、等级、游戏记录等，此操作不可恢复！',
          confirmText: '确认重置',
          confirmColor: '#ff4757',
          success: async (res) => {
            if (res.confirm) {
              try {
                // 获取当前用户ID
                const currentProfile = userProfileManager.getCurrentProfile();
                if (!currentProfile) {
                  addLog('error', '未找到当前用户档案');
                  return;
                }

                // 清除存储中的用户数据
                const userId = currentProfile.userId;
                uni.removeStorageSync(`user_profile_${userId}`);

                // 重新初始化用户档案
                await userProfileManager.initializeProfile(userId);

                // 重新加载数据
                await loadUserData();

                addLog('success', '用户数据重置成功');
                uni.showToast({
                  title: '重置成功',
                  icon: 'success',
                });
              } catch (error) {
                addLog('error', `重置失败: ${error}`);
                uni.showToast({
                  title: '重置失败',
                  icon: 'error',
                });
              }
            }
          },
        });
      };

      // 导出用户数据
      const exportUserData = () => {
        const profile = userProfileManager.getCurrentProfile();
        if (profile) {
          const exportData = {
            exportTime: new Date().toISOString(),
            userData: profile,
          };

          console.log('导出的用户数据:', exportData);
          addLog('success', '用户数据已导出到控制台');

          uni.showToast({
            title: '已导出到控制台',
            icon: 'success',
          });
        } else {
          addLog('error', '未找到用户数据');
        }
      };

      // 清理游戏历史
      const clearGameHistory = () => {
        uni.showModal({
          title: '确认清理',
          content: '这将清除所有游戏历史记录，但保留用户等级和总积分',
          success: async (res) => {
            if (res.confirm) {
              try {
                // 这里可以添加清理游戏历史的逻辑
                addLog('success', '游戏历史清理成功');
                uni.showToast({
                  title: '清理成功',
                  icon: 'success',
                });
              } catch (error) {
                addLog('error', `清理失败: ${error}`);
              }
            }
          },
        });
      };

      // 重新计算积分
      const recalculateScore = async () => {
        try {
          // 这里可以添加重新计算积分的逻辑
          addLog('info', '积分重新计算完成');
          await loadUserData();
          uni.showToast({
            title: '计算完成',
            icon: 'success',
          });
        } catch (error) {
          addLog('error', `计算失败: ${error}`);
        }
      };

      // 查看缓存统计
      const viewCacheStats = () => {
        try {
          const stats = hexagramCache.getStats();
          addLog('info', `缓存统计: ${stats.cachedCount}/64个卦象已缓存，总大小${stats.totalSize}KB`);

          uni.showModal({
            title: '卦象缓存统计',
            content: `已缓存: ${stats.cachedCount}/64 个卦象\n总大小: ${stats.totalSize} KB\n过期数量: ${stats.expiredCount} 个\n缓存命中率: ${stats.hitRate}%`,
            showCancel: false,
          });
        } catch (error) {
          addLog('error', `获取缓存统计失败: ${error}`);
        }
      };

      // 清除卦象缓存
      const clearHexagramCache = () => {
        uni.showModal({
          title: '确认清除',
          content: '这将清除所有卦象detail页面的浏览缓存，下次访问时需要重新加载',
          success: (res) => {
            if (res.confirm) {
              try {
                hexagramCache.clearAll();
                addLog('success', '卦象缓存清除成功');
                uni.showToast({
                  title: '清除成功',
                  icon: 'success',
                });
              } catch (error) {
                addLog('error', `清除缓存失败: ${error}`);
                uni.showToast({
                  title: '清除失败',
                  icon: 'error',
                });
              }
            }
          },
        });
      };

      // 清理过期缓存
      const cleanExpiredCache = () => {
        try {
          const statsBefore = hexagramCache.getStats();
          storage.cleanExpiredCache();
          const statsAfter = hexagramCache.getStats();

          const cleanedCount = statsBefore.expiredCount;
          addLog('success', `过期缓存清理完成，清理了${cleanedCount}个过期缓存`);

          uni.showToast({
            title: `清理了${cleanedCount}个过期缓存`,
            icon: 'success',
          });
        } catch (error) {
          addLog('error', `清理过期缓存失败: ${error}`);
          uni.showToast({
            title: '清理失败',
            icon: 'error',
          });
        }
      };

      // 测试HTML加载
      const testHtmlLoading = async () => {
        addLog('info', '开始测试HTML加载功能...');
        addLog('info', `CDN域名: ${CDN_DOMAIN}`);

        try {
          // 先清除卦象1的缓存，确保从远程加载
          hexagramCache.remove(1);
          addLog('info', '已清除卦象1的缓存');

          const startTime = Date.now();
          const content = await loadHexagramContent(1);
          const endTime = Date.now();
          const loadTime = endTime - startTime;

          if (content && content.length > 100) {
            addLog('success', `HTML加载成功！耗时: ${loadTime}ms，内容长度: ${content.length}`);
            addLog('info', `内容预览: ${content.substring(0, 100)}...`);

            uni.showModal({
              title: 'HTML加载测试',
              content: `✅ 加载成功！\n耗时: ${loadTime}ms\n内容长度: ${content.length}\n\n详细日志请查看控制台`,
              showCancel: false,
            });
          } else {
            addLog('warning', `HTML加载返回默认内容，可能加载失败`);
            addLog('info', `返回内容: ${content}`);

            uni.showModal({
              title: 'HTML加载测试',
              content: `⚠️ 返回默认内容\n可能是网络问题或CDN配置问题\n\n详细日志请查看控制台`,
              showCancel: false,
            });
          }
        } catch (error) {
          addLog('error', `HTML加载测试失败: ${error}`);
          uni.showModal({
            title: 'HTML加载测试',
            content: `❌ 加载失败\n错误: ${error}\n\n详细日志请查看控制台`,
            showCancel: false,
          });
        }
      };

      // 测试网络连接
      const testNetworkConnection = async () => {
        addLog('info', '开始测试网络连接...');

        // 测试多个URL
        const testUrls = [
          `${CDN_DOMAIN}/hexagram/1.dat`,
          `${CDN_DOMAIN}/hexagram/1.html`,
          `${CDN_DOMAIN}`,
          'https://www.baidu.com', // 作为对照
        ];

        const results = [];

        for (const url of testUrls) {
          addLog('info', `测试连接: ${url}`);

          try {
            const startTime = Date.now();

            const result = await new Promise((resolve) => {
              uni.request({
                url,
                method: 'GET',
                timeout: 5000,
                success: (res) => {
                  const endTime = Date.now();
                  const responseTime = endTime - startTime;
                  resolve({
                    url,
                    success: true,
                    statusCode: res.statusCode,
                    responseTime,
                    dataLength: res.data ? String(res.data).length : 0,
                  });
                },
                fail: (error) => {
                  const endTime = Date.now();
                  const responseTime = endTime - startTime;
                  resolve({
                    url,
                    success: false,
                    error: error.errMsg || error,
                    responseTime,
                  });
                },
              });
            });

            results.push(result);

            if (result.success) {
              addLog('success', `✅ ${url} - ${result.statusCode} (${result.responseTime}ms, ${result.dataLength}字节)`);
            } else {
              addLog('error', `❌ ${url} - ${result.error} (${result.responseTime}ms)`);
            }
          } catch (error) {
            addLog('error', `❌ ${url} - 测试异常: ${error}`);
            results.push({ url, success: false, error: String(error) });
          }
        }

        // 显示测试结果摘要
        const successCount = results.filter(r => r.success).length;
        const totalCount = results.length;

        addLog('info', `网络连接测试完成: ${successCount}/${totalCount} 成功`);

        uni.showModal({
          title: '网络连接测试',
          content: `测试完成: ${successCount}/${totalCount} 成功\n\n详细结果请查看调试日志`,
          showCancel: false,
        });
      };



      // 菜单点击处理
      const handleMenuClick = (menu: MenuItem) => {
        switch (menu.action) {
          case 'navigateToDataManagement':
            navigateToDataManagement();
            break;
          case 'navigateToButtonStyles':
            navigateToButtonStyles();
            break;
          case 'navigateToNavDebugger':
            navigateToNavDebugger();
            break;
          case 'navigateToHexagramSizes':
            navigateToHexagramSizes();
            break;
          default:
            addLog('warning', `未知的菜单操作: ${menu.action}`);
            break;
        }
      };

      // 导航到数据管理页面
      const navigateToDataManagement = () => {
        uni.navigateTo({
          url: '/pages/developer/data-management',
          success: () => {
            addLog('info', '导航到数据管理页面');
          },
          fail: (error) => {
            addLog('error', `导航失败: ${error.errMsg}`);
          }
        });
      };

      // 导航到按钮样式页面
      const navigateToButtonStyles = () => {
        uni.navigateTo({
          url: '/pages/developer/button-styles',
          success: () => {
            addLog('info', '导航到按钮样式页面');
          },
          fail: (error) => {
            addLog('error', `导航失败: ${error.errMsg}`);
          }
        });
      };

      // 导航到导航栏调试页面
      const navigateToNavDebugger = () => {
        uni.navigateTo({
          url: '/pages/developer/nav-debugger',
          success: () => {
            addLog('info', '导航到导航栏调试页面');
          },
          fail: (error) => {
            addLog('error', `导航失败: ${error.errMsg}`);
          }
        });
      };

      // 导航到卦象尺寸页面
      const navigateToHexagramSizes = () => {
        uni.navigateTo({
          url: '/pages/developer/hexagram-sizes',
          success: () => {
            addLog('info', '导航到卦象尺寸页面');
          },
          fail: (error) => {
            addLog('error', `导航失败: ${error.errMsg}`);
          }
        });
      };

      // 清除日志
      const clearLogs = () => {
        debugLogs.value = [];
      };

      onMounted(() => {
        loadUserData();
        addLog('info', '开发选项页面已加载');
      });

      onUnmounted(() => {
        // 清理事件监听
        uni.$off('debug-log-update');
      });

      return {
        userInfo,
        debugLogs,
        totalGamesPlayed,
        cacheInfo,
        cdnDomain,
        menuItems,
        handleMenuClick,
        clearLogs,
      };
    },
  });
</script>

<style lang="scss" scoped>
  .container {
    padding: 40rpx;
    background: #f5f5f5;
    min-height: 100vh;
  }

  /* 功能菜单区域 */
  .function-menu-section {
    margin-bottom: 40rpx;

    .menu-grid {
      background: white;
      border-radius: 24rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
      overflow: hidden;

      .menu-card {
        background: transparent;
        border-radius: 0;
        padding: 32rpx;
        display: flex;
        align-items: center;
        gap: 24rpx;
        transition: background-color 0.2s ease;
        border-bottom: 1rpx solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        &:active {
          background-color: rgba(0, 0, 0, 0.05);
        }

        .menu-icon {
          width: 48rpx;
          height: 48rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;

          .icon-text {
            font-size: 40rpx;
          }
        }

        .menu-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 4rpx;
        }

        .menu-title {
          font-size: 32rpx;
          color: #333;
          font-weight: 500;
          line-height: 1.2;
        }

        .menu-desc {
          font-size: 26rpx;
          color: #666;
          line-height: 1.3;
        }
      }
    }
  }

  .warning-card {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    border-radius: 32rpx;
    padding: 40rpx;
    margin-bottom: 40rpx;
    display: flex;
    align-items: center;
    box-shadow: 0 8rpx 24rpx rgba(255, 154, 158, 0.3);
  }

  .warning-icon {
    font-size: 64rpx;
    margin-right: 32rpx;
  }

  .warning-content {
    flex: 1;
  }

  .warning-title {
    display: block;
    font-size: 36rpx;
    font-weight: 700;
    color: #fff;
    margin-bottom: 8rpx;
  }

  .warning-text {
    display: block;
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.4;
  }

  .section {
    background: white;
    border-radius: 32rpx;
    margin-bottom: 40rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  }

  .section-title {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    padding: 40rpx 40rpx 0;
    margin-bottom: 32rpx;
  }

  .option-item {
    display: flex;
    align-items: center;
    padding: 32rpx 40rpx;
    border-bottom: 2rpx solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }
  }

  .option-info {
    flex: 1;
    margin-right: 32rpx;
  }

  .option-name {
    display: block;
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 8rpx;
  }

  .option-desc {
    display: block;
    font-size: 28rpx;
    color: #666;
    line-height: 1.4;
  }

  .option-btn {
    padding: 16rpx 32rpx;
    border-radius: 16rpx;
    font-size: 28rpx;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;

    &.primary {
      background: linear-gradient(135deg, #8b4513, #a0522d);
      color: white;
      box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.3);

      &:active {
        background: linear-gradient(135deg, #7a3e11, #8b4513);
        transform: translateY(2rpx);
        box-shadow: 0 2rpx 8rpx rgba(139, 69, 19, 0.4);
      }
    }

    &.secondary {
      background: #f8f9fa;
      color: #495057;
      border: 2rpx solid #dee2e6;

      &:active {
        background: #e9ecef;
        transform: translateY(2rpx);
      }
    }

    &.danger {
      background: linear-gradient(135deg, #ff4757, #ff3742);
      color: white;
      box-shadow: 0 4rpx 16rpx rgba(255, 71, 87, 0.3);

      &:active {
        background: linear-gradient(135deg, #ff3742, #ff2837);
        transform: translateY(2rpx);
        box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.4);
      }
    }
  }

  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 40rpx;
    border-bottom: 2rpx solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }
  }

  .info-label {
    font-size: 28rpx;
    color: #666;
    font-weight: 500;
  }

  .info-value {
    font-size: 28rpx;
    color: #333;
    font-weight: 600;
  }



  .log-container {
    max-height: 600rpx;
    overflow-y: auto;
    background: #f8f9fa;
    margin: 0 40rpx 32rpx;
    border-radius: 16rpx;
    padding: 24rpx;
  }

  .log-item {
    display: block;
    font-size: 24rpx;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    line-height: 1.5;
    margin-bottom: 8rpx;
    padding: 8rpx 16rpx;
    border-radius: 8rpx;

    &.info {
      color: #495057;
      background: rgba(108, 117, 125, 0.1);
    }

    &.success {
      color: #155724;
      background: rgba(40, 167, 69, 0.1);
    }

    &.warning {
      color: #856404;
      background: rgba(255, 193, 7, 0.1);
    }

    &.error {
      color: #721c24;
      background: rgba(220, 53, 69, 0.1);
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
</style>
