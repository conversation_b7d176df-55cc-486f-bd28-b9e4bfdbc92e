<template>
  <PageWrapper title="导航栏调试" :show-back="true">
    <view class="container">
      <view class="header">
        <text class="title">🔧 导航栏调试控制</text>
        <text class="subtitle">控制全局导航栏调试信息的显示</text>
      </view>

      <view class="control-section">
        <view class="control-item">
          <view class="control-info">
            <text class="control-title">调试开关</text>
            <text class="control-desc">开启后仅在当前页面显示导航栏调试信息</text>
          </view>
          <switch
            :checked="debugEnabled"
            @change="onDebugToggle"
            color="#8B4513"
          />
        </view>

        <view class="control-item">
          <view class="control-info">
            <text class="control-title">显示调试按钮</text>
            <text class="control-desc">在当前页面上显示调试切换按钮</text>
          </view>
          <switch
            :checked="showToggleBtn"
            @change="onToggleBtnChange"
            color="#8B4513"
            :disabled="!debugEnabled"
          />
        </view>
      </view>

      <view class="status-section">
        <view class="status-item">
          <text class="status-label">当前状态:</text>
          <text class="status-value" :class="{ active: debugEnabled }">
            {{ debugEnabled ? '✅ 调试已开启' : '❌ 调试已关闭' }}
          </text>
        </view>
        
        <view class="status-item">
          <text class="status-label">调试按钮:</text>
          <text class="status-value" :class="{ active: showToggleBtn && debugEnabled }">
            {{ showToggleBtn && debugEnabled ? '✅ 显示中' : '❌ 已隐藏' }}
          </text>
        </view>
      </view>

      <view class="info-section">
        <view class="info-title">📋 功能说明</view>
        <view class="info-list">
          <view class="info-item">
            <text class="info-bullet">•</text>
            <text class="info-text">调试开关：控制是否仅在当前页面显示导航栏调试信息</text>
          </view>
          <view class="info-item">
            <text class="info-bullet">•</text>
            <text class="info-text">调试按钮：在当前页面右上角显示调试切换按钮</text>
          </view>
          <view class="info-item">
            <text class="info-bullet">•</text>
            <text class="info-text">调试信息包括：胶囊按钮位置、状态栏高度、对齐计算等</text>
          </view>
          <view class="info-item">
            <text class="info-bullet">•</text>
            <text class="info-text">仅在微信小程序环境下显示完整调试信息</text>
          </view>
          <view class="info-item">
            <text class="info-bullet">•</text>
            <text class="info-text">⚠️ 调试设置仅在当前页面有效，离开页面后会自动重置</text>
          </view>
        </view>
      </view>

      <view class="action-section">
        <button 
          class="test-btn" 
          @tap="goToTestPage"
          :disabled="!debugEnabled"
        >
          前往测试页面
        </button>
        <button 
          class="reset-btn" 
          @tap="resetSettings"
        >
          重置设置
        </button>
      </view>
    </view>
  </PageWrapper>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import PageWrapper from '@/components/PageWrapper.vue'

const debugEnabled = ref(false)
const showToggleBtn = ref(false)

// 注意：这些设置只在当前页面有效，离开页面后会重置

// 调试开关切换
const onDebugToggle = (event: any) => {
  debugEnabled.value = event.detail.value
  if (!debugEnabled.value) {
    showToggleBtn.value = false
  }

  uni.showToast({
    title: debugEnabled.value ? '调试已开启（仅当前页面）' : '调试已关闭',
    icon: 'success'
  })
}

// 调试按钮显示切换
const onToggleBtnChange = (event: any) => {
  showToggleBtn.value = event.detail.value

  uni.showToast({
    title: showToggleBtn.value ? '调试按钮已显示（仅当前页面）' : '调试按钮已隐藏',
    icon: 'success'
  })
}

// 前往测试页面
const goToTestPage = () => {
  uni.navigateTo({
    url: '/pages/index/index'
  })
}

// 重置设置
const resetSettings = () => {
  uni.showModal({
    title: '确认重置',
    content: '确定要重置当前页面的调试设置吗？',
    success: (res) => {
      if (res.confirm) {
        debugEnabled.value = false
        showToggleBtn.value = false

        uni.showToast({
          title: '设置已重置',
          icon: 'success'
        })
      }
    }
  })
}
</script>

<style scoped lang="scss">
.container {
  padding: 40rpx;
  min-height: 100vh;
  background-color: #f8f4e9;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #8B4513;
  display: block;
  margin-bottom: 16rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.control-section {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.control-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.control-info {
  flex: 1;
  margin-right: 40rpx;
}

.control-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.control-desc {
  font-size: 26rpx;
  color: #666;
  display: block;
}

.status-section {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.status-label {
  font-size: 30rpx;
  color: #333;
}

.status-value {
  font-size: 28rpx;
  color: #999;
  
  &.active {
    color: #52c41a;
  }
}

.info-section {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.info-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 30rpx;
}

.info-list {
  // 列表容器
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.info-bullet {
  font-size: 28rpx;
  color: #8B4513;
  margin-right: 16rpx;
  margin-top: 4rpx;
}

.info-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  flex: 1;
}

.action-section {
  display: flex;
  gap: 20rpx;
}

.test-btn, .reset-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  border: none;
  
  &:disabled {
    opacity: 0.5;
  }
}

.test-btn {
  background: #8B4513;
  color: white;
  
  &:not(:disabled):active {
    background: #6d3410;
  }
}

.reset-btn {
  background: #f5f5f5;
  color: #666;
  
  &:active {
    background: #e8e8e8;
  }
}
</style>
