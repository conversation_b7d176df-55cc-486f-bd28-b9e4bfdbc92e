<template>
  <PageWrapper title="按钮样式" :show-logo="false">
    <view class="container">
      <!-- 页面说明 -->
      <view class="intro-card">
        <view class="intro-icon">🎨</view>
        <view class="intro-content">
          <text class="intro-title">按钮样式展示</text>
          <text class="intro-text">基于易境万象app整体风格设计的12款按钮样式</text>
        </view>
      </view>

      <!-- 按钮样式展示 -->
      <view class="styles-grid">
        <!-- 1. 经典易经风格 -->
        <view class="style-card">
          <text class="style-title">经典易经风格</text>
          <button class="btn btn-classic" @tap="onButtonTap('经典易经风格')">
            <text class="btn-text">🔮 开始占卜</text>
          </button>
        </view>

        <!-- 2. 现代简约风格 -->
        <view class="style-card">
          <text class="style-title">现代简约风格</text>
          <button class="btn btn-modern" @tap="onButtonTap('现代简约风格')">
            <text class="btn-text">开始游戏</text>
          </button>
        </view>

        <!-- 3. 渐变光影风格 -->
        <view class="style-card">
          <text class="style-title">渐变光影风格</text>
          <button class="btn btn-gradient" @tap="onButtonTap('渐变光影风格')">
            <text class="btn-text">✨ 探索奥秘</text>
          </button>
        </view>

        <!-- 4. 古典文雅风格 -->
        <view class="style-card">
          <text class="style-title">古典文雅风格</text>
          <button class="btn btn-elegant" @tap="onButtonTap('古典文雅风格')">
            <text class="btn-text">📜 研习经典</text>
          </button>
        </view>

        <!-- 5. 玻璃拟态风格 -->
        <view class="style-card">
          <text class="style-title">玻璃拟态风格</text>
          <button class="btn btn-glass" @tap="onButtonTap('玻璃拟态风格')">
            <text class="btn-text">🌟 开启智慧</text>
          </button>
        </view>

        <!-- 6. 新拟态风格 -->
        <view class="style-card">
          <text class="style-title">新拟态风格</text>
          <button class="btn btn-neumorphism" @tap="onButtonTap('新拟态风格')">
            <text class="btn-text">🎯 精准预测</text>
          </button>
        </view>

        <!-- 7. 霓虹发光风格 -->
        <view class="style-card">
          <text class="style-title">霓虹发光风格</text>
          <button class="btn btn-neon" @tap="onButtonTap('霓虹发光风格')">
            <text class="btn-text">⚡ 能量爆发</text>
          </button>
        </view>

        <!-- 8. 水墨风格 -->
        <view class="style-card">
          <text class="style-title">水墨风格</text>
          <button class="btn btn-ink" @tap="onButtonTap('水墨风格')">
            <text class="btn-text">🖌️ 墨韵天成</text>
          </button>
        </view>

        <!-- 9. 金属质感风格 -->
        <view class="style-card">
          <text class="style-title">金属质感风格</text>
          <button class="btn btn-metal" @tap="onButtonTap('金属质感风格')">
            <text class="btn-text">⚔️ 铸就传奇</text>
          </button>
        </view>

        <!-- 10. 彩虹渐变风格 -->
        <view class="style-card">
          <text class="style-title">彩虹渐变风格</text>
          <button class="btn btn-rainbow" @tap="onButtonTap('彩虹渐变风格')">
            <text class="btn-text">🌈 七彩人生</text>
          </button>
        </view>

        <!-- 11. 阴阳太极风格 -->
        <view class="style-card">
          <text class="style-title">阴阳太极风格</text>
          <button class="btn btn-taiji" @tap="onButtonTap('阴阳太极风格')">
            <text class="btn-text">☯️ 阴阳调和</text>
          </button>
        </view>

        <!-- 12. 星空宇宙风格 -->
        <view class="style-card">
          <text class="style-title">星空宇宙风格</text>
          <button class="btn btn-cosmic" @tap="onButtonTap('星空宇宙风格')">
            <text class="btn-text">🌌 宇宙奥秘</text>
          </button>
        </view>
      </view>

      <!-- 使用说明 -->
      <view class="usage-card">
        <text class="usage-title">使用说明</text>
        <text class="usage-text">点击任意按钮查看交互效果，这些样式可以应用到应用的各个界面中。</text>
      </view>
    </view>
  </PageWrapper>
</template>

<script setup lang="ts">
  const onButtonTap = (styleName: string) => {
    uni.showToast({
      title: `点击了${styleName}按钮`,
      icon: 'none',
      duration: 1500,
    });
  };
</script>

<style lang="scss" scoped>
  .container {
    padding: 20rpx;
    background: #f8f4e9;
    min-height: 100vh;
  }

  .intro-card {
    background: linear-gradient(135deg, rgba(139, 69, 19, 0.1), rgba(160, 82, 45, 0.1));
    border-radius: 24rpx;
    padding: 32rpx;
    margin-bottom: 32rpx;
    display: flex;
    align-items: center;
    border: 2rpx solid rgba(139, 69, 19, 0.15);
  }

  .intro-icon {
    font-size: 48rpx;
    margin-right: 24rpx;
  }

  .intro-content {
    flex: 1;
  }

  .intro-title {
    display: block;
    font-size: 32rpx;
    font-weight: bold;
    color: #8b4513;
    margin-bottom: 8rpx;
  }

  .intro-text {
    display: block;
    font-size: 28rpx;
    color: #a0522d;
    line-height: 1.4;
  }

  .styles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300rpx, 1fr));
    gap: 24rpx;
    margin-bottom: 32rpx;
  }

  .style-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20rpx;
    padding: 32rpx;
    text-align: center;
    box-shadow: 0 8rpx 32rpx rgba(139, 69, 19, 0.1);
    border: 1rpx solid rgba(139, 69, 19, 0.1);
  }

  .style-title {
    display: block;
    font-size: 28rpx;
    font-weight: 600;
    color: #8b4513;
    margin-bottom: 24rpx;
  }

  .btn {
    width: 100%;
    padding: 24rpx 32rpx;
    border-radius: 16rpx;
    border: none;
    font-size: 28rpx;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;
  }

  .btn-text {
    position: relative;
    z-index: 2;
  }

  /* 1. 经典易经风格 */
  .btn-classic {
    background: linear-gradient(135deg, #8b4513, #a0522d);
    color: #fff;
    box-shadow: 0 8rpx 24rpx rgba(139, 69, 19, 0.3);
    border: 2rpx solid #d4af37;

    &:active {
      transform: translateY(2rpx);
      box-shadow: 0 4rpx 12rpx rgba(139, 69, 19, 0.4);
    }
  }

  /* 2. 现代简约风格 */
  .btn-modern {
    background: #fff;
    color: #8b4513;
    border: 2rpx solid #8b4513;
    box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.1);

    &:active {
      background: #8b4513;
      color: #fff;
      transform: scale(0.98);
    }
  }

  /* 3. 渐变光影风格 */
  .btn-gradient {
    background: linear-gradient(135deg, #ff9a9e, #fecfef, #fecfef, #ff9a9e);
    background-size: 400% 400%;
    color: #8b4513;
    animation: gradientShift 3s ease infinite;
    box-shadow: 0 8rpx 24rpx rgba(255, 154, 158, 0.3);

    &:active {
      transform: scale(0.95);
    }
  }

  @keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  /* 4. 古典文雅风格 */
  .btn-elegant {
    background: linear-gradient(135deg, #f5f5dc, #faf0e6);
    color: #8b4513;
    border: 2rpx solid #d4af37;
    box-shadow: inset 0 2rpx 4rpx rgba(212, 175, 55, 0.2), 0 4rpx 16rpx rgba(139, 69, 19, 0.1);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, transparent 30%, rgba(212, 175, 55, 0.1) 50%, transparent 70%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:active::before {
      opacity: 1;
    }

    &:active {
      transform: translateY(1rpx);
    }
  }

  /* 5. 玻璃拟态风格 */
  .btn-glass {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20rpx);
    color: #8b4513;
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8rpx 32rpx rgba(139, 69, 19, 0.1);

    &:active {
      background: rgba(255, 255, 255, 0.3);
      transform: scale(0.98);
    }
  }

  /* 6. 新拟态风格 */
  .btn-neumorphism {
    background: #f8f4e9;
    color: #8b4513;
    box-shadow:
      8rpx 8rpx 16rpx rgba(139, 69, 19, 0.1),
      -8rpx -8rpx 16rpx rgba(255, 255, 255, 0.8);
    border: none;

    &:active {
      box-shadow:
        inset 4rpx 4rpx 8rpx rgba(139, 69, 19, 0.1),
        inset -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.8);
    }
  }

  /* 7. 霓虹发光风格 */
  .btn-neon {
    background: transparent;
    color: #ff6b35;
    border: 2rpx solid #ff6b35;
    box-shadow:
      0 0 10rpx #ff6b35,
      inset 0 0 10rpx rgba(255, 107, 53, 0.1);
    text-shadow: 0 0 10rpx #ff6b35;

    &:active {
      box-shadow:
        0 0 20rpx #ff6b35,
        0 0 40rpx #ff6b35,
        inset 0 0 20rpx rgba(255, 107, 53, 0.2);
      text-shadow: 0 0 20rpx #ff6b35;
    }
  }

  /* 8. 水墨风格 */
  .btn-ink {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: #ecf0f1;
    border: 2rpx solid #7f8c8d;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(236, 240, 241, 0.1) 0%, transparent 70%);
      transform: rotate(45deg);
      transition: all 0.5s ease;
      opacity: 0;
    }

    &:active::before {
      opacity: 1;
      transform: rotate(45deg) translate(20rpx, 20rpx);
    }

    &:active {
      transform: scale(0.98);
    }
  }

  /* 9. 金属质感风格 */
  .btn-metal {
    background: linear-gradient(135deg, #bdc3c7, #ecf0f1, #bdc3c7);
    color: #2c3e50;
    border: 2rpx solid #95a5a6;
    box-shadow:
      0 4rpx 8rpx rgba(0, 0, 0, 0.2),
      inset 0 2rpx 4rpx rgba(255, 255, 255, 0.8),
      inset 0 -2rpx 4rpx rgba(0, 0, 0, 0.1);
    text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);

    &:active {
      box-shadow:
        0 2rpx 4rpx rgba(0, 0, 0, 0.3),
        inset 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
      transform: translateY(1rpx);
    }
  }

  /* 10. 彩虹渐变风格 */
  .btn-rainbow {
    background: linear-gradient(45deg, #ff0000, #ff8000, #ffff00, #80ff00, #00ff80, #0080ff, #8000ff);
    background-size: 400% 400%;
    color: #fff;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
    animation: rainbowShift 2s ease infinite;
    border: none;

    &:active {
      transform: scale(0.95);
      animation-duration: 0.5s;
    }
  }

  @keyframes rainbowShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  /* 11. 阴阳太极风格 */
  .btn-taiji {
    background: radial-gradient(circle at 30% 50%, #000 0%, #000 25%, #fff 25%, #fff 50%, #000 50%, #000 75%, #fff 75%);
    color: #8b4513;
    border: 3rpx solid #d4af37;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 100%;
      height: 100%;
      background: rgba(139, 69, 19, 0.1);
      border-radius: 50%;
      transform: translate(-50%, -50%) scale(0);
      transition: transform 0.3s ease;
    }

    &:active::before {
      transform: translate(-50%, -50%) scale(1);
    }

    &:active {
      transform: rotate(180deg);
    }
  }

  /* 12. 星空宇宙风格 */
  .btn-cosmic {
    background: linear-gradient(135deg, #0c0c0c, #1a1a2e, #16213e, #0f3460);
    color: #fff;
    border: 2rpx solid #4a90e2;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background:
        radial-gradient(2rpx 2rpx at 20rpx 30rpx, #fff, transparent),
        radial-gradient(2rpx 2rpx at 40rpx 70rpx, #fff, transparent),
        radial-gradient(1rpx 1rpx at 90rpx 40rpx, #fff, transparent),
        radial-gradient(1rpx 1rpx at 130rpx 80rpx, #fff, transparent),
        radial-gradient(2rpx 2rpx at 160rpx 30rpx, #fff, transparent);
      background-repeat: repeat;
      background-size: 200rpx 100rpx;
      animation: starTwinkle 3s linear infinite;
      opacity: 0.8;
    }

    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 30rpx #4a90e2;
    }
  }

  @keyframes starTwinkle {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
  }

  .usage-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20rpx;
    padding: 32rpx;
    text-align: center;
    border: 1rpx solid rgba(139, 69, 19, 0.1);
  }

  .usage-title {
    display: block;
    font-size: 32rpx;
    font-weight: bold;
    color: #8b4513;
    margin-bottom: 16rpx;
  }

  .usage-text {
    display: block;
    font-size: 28rpx;
    color: #a0522d;
    line-height: 1.5;
  }
</style>
