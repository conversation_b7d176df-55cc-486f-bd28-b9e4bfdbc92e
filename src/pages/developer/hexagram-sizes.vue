<template>
  <PageWrapper title="卦象尺寸对比" :show-back="true">
    <view class="demo-container">
      <view class="size-section">
        <view class="size-title">🔍 HexagramDisplay 组件尺寸对比</view>
        <view class="size-desc">展示不同尺寸的卦象显示效果</view>
      </view>

      <!-- Tiny 尺寸 -->
      <view class="size-demo">
        <view class="demo-header">
          <view class="demo-label">Tiny 尺寸</view>
          <view class="demo-info">适用于8列紧凑布局</view>
        </view>
        <view class="demo-grid tiny-grid">
          <view 
            v-for="hexagram in sampleHexagrams" 
            :key="`tiny-${hexagram.id}`"
            class="demo-item"
          >
            <HexagramDisplay
              :hexagram="hexagram"
              size="tiny"
              :show-name="true"
              :interactive="true"
            />
          </view>
        </view>
      </view>

      <!-- Small 尺寸 -->
      <view class="size-demo">
        <view class="demo-header">
          <view class="demo-label">Small 尺寸</view>
          <view class="demo-info">适用于4列标准布局</view>
        </view>
        <view class="demo-grid small-grid">
          <view 
            v-for="hexagram in sampleHexagrams" 
            :key="`small-${hexagram.id}`"
            class="demo-item"
          >
            <HexagramDisplay
              :hexagram="hexagram"
              size="small"
              :show-name="true"
              :interactive="true"
            />
          </view>
        </view>
      </view>

      <!-- Medium 尺寸 -->
      <view class="size-demo">
        <view class="demo-header">
          <view class="demo-label">Medium 尺寸</view>
          <view class="demo-info">适用于3列标准布局</view>
        </view>
        <view class="demo-grid medium-grid">
          <view 
            v-for="hexagram in sampleHexagrams" 
            :key="`medium-${hexagram.id}`"
            class="demo-item"
          >
            <HexagramDisplay
              :hexagram="hexagram"
              size="medium"
              :show-name="true"
              :interactive="true"
            />
          </view>
        </view>
      </view>

      <!-- Large 尺寸 -->
      <view class="size-demo">
        <view class="demo-header">
          <view class="demo-label">Large 尺寸</view>
          <view class="demo-info">适用于单个或2列展示</view>
        </view>
        <view class="demo-grid large-grid">
          <view 
            v-for="hexagram in sampleHexagrams.slice(0, 4)" 
            :key="`large-${hexagram.id}`"
            class="demo-item"
          >
            <HexagramDisplay
              :hexagram="hexagram"
              size="large"
              :show-name="true"
              :interactive="true"
            />
          </view>
        </view>
      </view>

      <!-- 尺寸规格表 -->
      <view class="specs-table">
        <view class="specs-title">📏 尺寸规格对比</view>
        <view class="specs-grid">
          <view class="specs-row header">
            <view class="specs-cell">尺寸</view>
            <view class="specs-cell">阳爻宽度</view>
            <view class="specs-cell">阳爻高度</view>
            <view class="specs-cell">适用场景</view>
          </view>
          <view class="specs-row">
            <view class="specs-cell">Tiny</view>
            <view class="specs-cell">50rpx</view>
            <view class="specs-cell">8rpx</view>
            <view class="specs-cell">8列紧凑布局</view>
          </view>
          <view class="specs-row">
            <view class="specs-cell">Small</view>
            <view class="specs-cell">80rpx</view>
            <view class="specs-cell">12rpx</view>
            <view class="specs-cell">4列标准布局</view>
          </view>
          <view class="specs-row">
            <view class="specs-cell">Medium</view>
            <view class="specs-cell">130rpx</view>
            <view class="specs-cell">20rpx</view>
            <view class="specs-cell">3列标准布局</view>
          </view>
          <view class="specs-row">
            <view class="specs-cell">Large</view>
            <view class="specs-cell">220rpx</view>
            <view class="specs-cell">32rpx</view>
            <view class="specs-cell">单个或2列展示</view>
          </view>
        </view>
      </view>
    </view>
  </PageWrapper>
</template>

<script setup lang="ts">
  import { hexagrams } from '@/utils/hexagram';
  import PageWrapper from '@/components/PageWrapper.vue';
  import HexagramDisplay from '@/components/HexagramDisplay.vue';

  // 选择前8个卦象作为示例
  const sampleHexagrams = hexagrams.slice(0, 8);
</script>

<style lang="scss" scoped>
  .demo-container {
    padding: 32rpx;
  }

  .size-section {
    text-align: center;
    margin-bottom: 48rpx;
  }

  .size-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 16rpx;
  }

  .size-desc {
    font-size: 28rpx;
    color: #666;
  }

  .size-demo {
    background: white;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 32rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  }

  .demo-header {
    margin-bottom: 24rpx;
  }

  .demo-label {
    font-size: 32rpx;
    font-weight: 600;
    color: #865404;
    margin-bottom: 8rpx;
  }

  .demo-info {
    font-size: 24rpx;
    color: #666;
  }

  .demo-grid {
    display: grid;
    gap: 16rpx;
  }

  .tiny-grid {
    grid-template-columns: repeat(8, 1fr);
    gap: 12rpx;
  }

  .small-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 16rpx;
  }

  .medium-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 20rpx;
  }

  .large-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 24rpx;
  }

  .demo-item {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 16rpx;
    background: #f8f9fa;
    border-radius: 8rpx;
    transition: all 0.3s ease;

    &:hover {
      background: #e9ecef;
    }
  }

  .specs-table {
    background: white;
    border-radius: 16rpx;
    padding: 32rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  }

  .specs-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
    text-align: center;
  }

  .specs-grid {
    border-radius: 8rpx;
    overflow: hidden;
    border: 2rpx solid #e9ecef;
  }

  .specs-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 2fr;

    &.header {
      background: #865404;
      color: white;
      font-weight: 600;
    }

    &:not(.header):nth-child(even) {
      background: #f8f9fa;
    }
  }

  .specs-cell {
    padding: 16rpx 12rpx;
    font-size: 24rpx;
    text-align: center;
    border-right: 2rpx solid #e9ecef;

    &:last-child {
      border-right: none;
    }
  }
</style>
