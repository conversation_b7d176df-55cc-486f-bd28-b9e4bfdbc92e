<template>
  <PageWrapper title="缓存数据管理" :show-back="true">
    <view class="container">
      <!-- 概览统计 -->
      <view class="stats-overview">
        <view class="stats-card">
          <view class="stats-icon">👤</view>
          <view class="stats-info">
            <text class="stats-number">{{ userStats.totalUsers }}</text>
            <text class="stats-label">用户档案</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="stats-icon">📚</view>
          <view class="stats-info">
            <text class="stats-number">{{ hexagramStats.cachedCount }}/64</text>
            <text class="stats-label">卦象缓存</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="stats-icon">🎮</view>
          <view class="stats-info">
            <text class="stats-number">{{ gameStats.totalGames }}</text>
            <text class="stats-label">游戏记录</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="stats-icon">💾</view>
          <view class="stats-info">
            <text class="stats-number">{{ totalCacheSize }}KB</text>
            <text class="stats-label">总缓存</text>
          </view>
        </view>
      </view>

      <!-- 用户数据管理 -->
      <view class="section">
        <text class="section-title">👤 用户数据管理</text>

        <view class="option-item">
          <view class="option-info">
            <text class="option-name">查看用户数据</text>
            <text class="option-desc">查看当前用户的详细数据</text>
          </view>
          <button class="option-btn secondary" @tap="viewUserData">查看</button>
        </view>

        <view class="option-item">
          <view class="option-info">
            <text class="option-name">重置用户数据</text>
            <text class="option-desc">清除所有用户数据，恢复到初始状态</text>
          </view>
          <button class="option-btn danger" @tap="resetUserData">重置</button>
        </view>

        <view class="option-item">
          <view class="option-info">
            <text class="option-name">导出用户数据</text>
            <text class="option-desc">导出用户数据用于备份或分析</text>
          </view>
          <button class="option-btn secondary" @tap="exportUserData">导出</button>
        </view>

        <view class="option-item">
          <view class="option-info">
            <text class="option-name">清除用户缓存</text>
            <text class="option-desc">清除用户相关的所有缓存数据</text>
          </view>
          <button class="option-btn warning" @tap="clearUserCache">清除</button>
        </view>
      </view>

      <!-- 卦象数据管理 -->
      <view class="section">
        <text class="section-title">📚 卦象数据管理</text>

        <view class="option-item">
          <view class="option-info">
            <text class="option-name">查看缓存统计</text>
            <text class="option-desc">查看卦象内容缓存的使用情况</text>
          </view>
          <button class="option-btn secondary" @tap="viewCacheStats">查看</button>
        </view>

        <view class="option-item">
          <view class="option-info">
            <text class="option-name">清除卦象缓存</text>
            <text class="option-desc">清除所有卦象detail页面的浏览缓存</text>
          </view>
          <button class="option-btn secondary" @tap="clearHexagramCache">清除</button>
        </view>

        <view class="option-item">
          <view class="option-info">
            <text class="option-name">清理过期缓存</text>
            <text class="option-desc">仅清理已过期的卦象缓存</text>
          </view>
          <button class="option-btn secondary" @tap="cleanExpiredCache">清理</button>
        </view>

        <view class="option-item">
          <view class="option-info">
            <text class="option-name">预加载热门卦象</text>
            <text class="option-desc">预加载前10个最常访问的卦象</text>
          </view>
          <button class="option-btn primary" @tap="preloadPopularHexagrams">预加载</button>
        </view>

        <view class="option-item">
          <view class="option-info">
            <text class="option-name">测试HTML加载</text>
            <text class="option-desc">测试卦象1的HTML内容加载功能</text>
          </view>
          <button class="option-btn primary" @tap="testHtmlLoading">测试</button>
        </view>

        <view class="option-item">
          <view class="option-info">
            <text class="option-name">测试网络连接</text>
            <text class="option-desc">测试CDN服务器的网络连接状态</text>
          </view>
          <button class="option-btn secondary" @tap="testNetworkConnection">测试</button>
        </view>
      </view>

      <!-- 游戏数据管理 -->
      <view class="section">
        <text class="section-title">🎮 游戏数据管理</text>

        <view class="option-item">
          <view class="option-info">
            <text class="option-name">查看游戏统计</text>
            <text class="option-desc">查看游戏数据和统计信息</text>
          </view>
          <button class="option-btn secondary" @tap="viewGameStats">查看</button>
        </view>

        <view class="option-item">
          <view class="option-info">
            <text class="option-name">清理游戏历史</text>
            <text class="option-desc">清除所有游戏历史记录</text>
          </view>
          <button class="option-btn secondary" @tap="clearGameHistory">清理</button>
        </view>

        <view class="option-item">
          <view class="option-info">
            <text class="option-name">重新计算积分</text>
            <text class="option-desc">重新计算用户总积分</text>
          </view>
          <button class="option-btn secondary" @tap="recalculateScore">重算</button>
        </view>

        <view class="option-item">
          <view class="option-info">
            <text class="option-name">导出游戏数据</text>
            <text class="option-desc">导出游戏记录用于分析</text>
          </view>
          <button class="option-btn secondary" @tap="exportGameData">导出</button>
        </view>

        <view class="option-item">
          <view class="option-info">
            <text class="option-name">清除游戏缓存</text>
            <text class="option-desc">清除游戏相关的缓存数据</text>
          </view>
          <button class="option-btn warning" @tap="clearGameCache">清除</button>
        </view>
      </view>

      <!-- 系统缓存管理 -->
      <view class="section">
        <text class="section-title">🗂️ 系统缓存管理</text>

        <view class="option-item">
          <view class="option-info">
            <text class="option-name">查看存储使用情况</text>
            <text class="option-desc">查看本地存储的详细使用情况</text>
          </view>
          <button class="option-btn secondary" @tap="viewStorageUsage">查看</button>
        </view>

        <view class="option-item">
          <view class="option-info">
            <text class="option-name">清理所有缓存</text>
            <text class="option-desc">清除所有缓存数据（保留用户数据）</text>
          </view>
          <button class="option-btn warning" @tap="clearAllCache">清理</button>
        </view>

        <view class="option-item">
          <view class="option-info">
            <text class="option-name">完全重置</text>
            <text class="option-desc">清除所有数据，恢复到初始状态</text>
          </view>
          <button class="option-btn danger" @tap="fullReset">完全重置</button>
        </view>
      </view>

      <!-- 操作日志 -->
      <view v-if="operationLogs.length > 0" class="section">
        <text class="section-title">📝 操作日志</text>
        <view class="log-container">
          <text v-for="(log, index) in operationLogs" :key="index" class="log-item" :class="log.type">
            [{{ log.time }}] {{ log.message }}
          </text>
        </view>
        <button class="option-btn secondary" @tap="clearLogs">清除日志</button>
      </view>
    </view>
  </PageWrapper>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue';
  import PageWrapper from '@/components/PageWrapper.vue';
  import { userProfileManager } from '@/games/shared/services/user/user-profile';
  import { hexagramCache, storage } from '@/utils/storage';
  import { loadHexagramContent } from '@/utils/html-loader';
  import { CDN_DOMAIN } from '@/config';

  interface OperationLog {
    time: string;
    type: 'info' | 'success' | 'warning' | 'error';
    message: string;
  }

  // 响应式数据
  const operationLogs = ref<OperationLog[]>([]);
  const userInfo = ref<Record<string, unknown>>({});

  // 统计数据
  const userStats = computed(() => ({
    totalUsers: 1, // 当前只有一个用户
    currentUserId: userInfo.value.userId || '未知'
  }));

  const hexagramStats = computed(() => {
    try {
      return hexagramCache.getStats();
    } catch (error) {
      return { cachedCount: 0, totalSize: 0, expiredCount: 0, hitRate: 0 };
    }
  });

  const gameStats = computed(() => {
    const gamesPlayed = userInfo.value.gamesPlayed as Record<string, number> || {};
    return {
      totalGames: Object.values(gamesPlayed).reduce((sum: number, count: number) => sum + count, 0),
      gameTypes: Object.keys(gamesPlayed).length
    };
  });

  const totalCacheSize = computed(() => {
    return hexagramStats.value.totalSize + 50; // 估算其他缓存大小
  });

  // 添加日志
  const addLog = (type: OperationLog['type'], message: string) => {
    const now = new Date();
    const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
    operationLogs.value.unshift({ time, type, message });

    // 限制日志数量
    if (operationLogs.value.length > 30) {
      operationLogs.value = operationLogs.value.slice(0, 30);
    }
  };

  // 加载用户数据
  const loadUserData = async () => {
    try {
      const profile = userProfileManager.getCurrentProfile();
      if (profile) {
        userInfo.value = { ...profile };
        addLog('info', '用户数据加载成功');
      } else {
        addLog('warning', '未找到用户数据');
      }
    } catch (error) {
      addLog('error', `加载用户数据失败: ${error}`);
    }
  };

  // 用户数据管理方法
  const viewUserData = () => {
    const profile = userProfileManager.getCurrentProfile();
    if (profile) {
      console.log('用户数据:', profile);
      addLog('info', '用户数据已输出到控制台');

      uni.showModal({
        title: '用户数据',
        content: `用户ID: ${profile.userId}\n总积分: ${profile.totalScore}\n经验值: ${profile.experience}\n等级: ${profile.level}`,
        showCancel: false,
      });
    } else {
      addLog('error', '未找到用户数据');
    }
  };

  const resetUserData = () => {
    uni.showModal({
      title: '确认重置',
      content: '这将清除所有用户数据，包括积分、等级、游戏记录等，此操作不可恢复！',
      confirmText: '确认重置',
      confirmColor: '#ff4757',
      success: async (res) => {
        if (res.confirm) {
          try {
            const currentProfile = userProfileManager.getCurrentProfile();
            if (!currentProfile) {
              addLog('error', '未找到当前用户档案');
              return;
            }

            const userId = currentProfile.userId;
            uni.removeStorageSync(`user_profile_${userId}`);
            await userProfileManager.initializeProfile(userId);
            await loadUserData();

            addLog('success', '用户数据重置成功');
            uni.showToast({ title: '重置成功', icon: 'success' });
          } catch (error) {
            addLog('error', `重置失败: ${error}`);
            uni.showToast({ title: '重置失败', icon: 'error' });
          }
        }
      },
    });
  };

  const exportUserData = () => {
    const profile = userProfileManager.getCurrentProfile();
    if (profile) {
      const exportData = {
        exportTime: new Date().toISOString(),
        userData: profile,
      };

      console.log('导出的用户数据:', exportData);
      addLog('success', '用户数据已导出到控制台');
      uni.showToast({ title: '已导出到控制台', icon: 'success' });
    } else {
      addLog('error', '未找到用户数据');
    }
  };

  const clearUserCache = () => {
    uni.showModal({
      title: '确认清除',
      content: '这将清除用户相关的缓存数据，但保留核心用户信息',
      success: (res) => {
        if (res.confirm) {
          try {
            // 这里可以添加清除用户缓存的逻辑
            addLog('success', '用户缓存清除成功');
            uni.showToast({ title: '清除成功', icon: 'success' });
          } catch (error) {
            addLog('error', `清除失败: ${error}`);
          }
        }
      },
    });
  };

  // 卦象数据管理方法
  const viewCacheStats = () => {
    try {
      const stats = hexagramCache.getStats();
      addLog('info', `缓存统计: ${stats.cachedCount}/64个卦象已缓存，总大小${stats.totalSize}KB`);

      uni.showModal({
        title: '卦象缓存统计',
        content: `已缓存: ${stats.cachedCount}/64 个卦象\n总大小: ${stats.totalSize} KB\n过期数量: ${stats.expiredCount} 个\n缓存命中率: ${stats.hitRate}%`,
        showCancel: false,
      });
    } catch (error) {
      addLog('error', `获取缓存统计失败: ${error}`);
    }
  };

  const clearHexagramCache = () => {
    uni.showModal({
      title: '确认清除',
      content: '这将清除所有卦象detail页面的浏览缓存，下次访问时需要重新加载',
      success: (res) => {
        if (res.confirm) {
          try {
            hexagramCache.clearAll();
            addLog('success', '卦象缓存清除成功');
            uni.showToast({ title: '清除成功', icon: 'success' });
          } catch (error) {
            addLog('error', `清除缓存失败: ${error}`);
            uni.showToast({ title: '清除失败', icon: 'error' });
          }
        }
      },
    });
  };

  const cleanExpiredCache = () => {
    try {
      const statsBefore = hexagramCache.getStats();
      storage.cleanExpiredCache();

      const cleanedCount = statsBefore.expiredCount;
      addLog('success', `过期缓存清理完成，清理了${cleanedCount}个过期缓存`);
      uni.showToast({ title: `清理了${cleanedCount}个过期缓存`, icon: 'success' });
    } catch (error) {
      addLog('error', `清理过期缓存失败: ${error}`);
      uni.showToast({ title: '清理失败', icon: 'error' });
    }
  };

  const preloadPopularHexagrams = async () => {
    addLog('info', '开始预加载热门卦象...');
    const popularIds = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]; // 前10个卦象
    let successCount = 0;

    for (const id of popularIds) {
      try {
        await loadHexagramContent(id);
        successCount++;
        addLog('info', `卦象${id}预加载成功`);
      } catch (error) {
        addLog('warning', `卦象${id}预加载失败: ${error}`);
      }
    }

    addLog('success', `预加载完成，成功加载${successCount}/${popularIds.length}个卦象`);
    uni.showToast({ title: `预加载完成 ${successCount}/${popularIds.length}`, icon: 'success' });
  };

  const testHtmlLoading = async () => {
    addLog('info', '开始测试HTML加载功能...');
    addLog('info', `CDN域名: ${CDN_DOMAIN}`);

    try {
      hexagramCache.remove(1);
      addLog('info', '已清除卦象1的缓存');

      const startTime = Date.now();
      const content = await loadHexagramContent(1);
      const endTime = Date.now();
      const loadTime = endTime - startTime;

      if (content && content.length > 100) {
        addLog('success', `HTML加载成功！耗时: ${loadTime}ms，内容长度: ${content.length}`);
        uni.showModal({
          title: 'HTML加载测试',
          content: `✅ 加载成功！\n耗时: ${loadTime}ms\n内容长度: ${content.length}`,
          showCancel: false,
        });
      } else {
        addLog('warning', `HTML加载返回默认内容，可能加载失败`);
        uni.showModal({
          title: 'HTML加载测试',
          content: `⚠️ 返回默认内容\n可能是网络问题或CDN配置问题`,
          showCancel: false,
        });
      }
    } catch (error) {
      addLog('error', `HTML加载测试失败: ${error}`);
      uni.showModal({
        title: 'HTML加载测试',
        content: `❌ 加载失败\n错误: ${error}`,
        showCancel: false,
      });
    }
  };

  const testNetworkConnection = async () => {
    addLog('info', '开始测试网络连接...');

    const testUrls = [
      `${CDN_DOMAIN}/hexagram/1.dat`,
      `${CDN_DOMAIN}/hexagram/1.html`,
      `${CDN_DOMAIN}`,
      'https://www.baidu.com',
    ];

    const results = [];

    for (const url of testUrls) {
      addLog('info', `测试连接: ${url}`);

      try {
        const startTime = Date.now();

        const result = await new Promise<any>((resolve) => {
          uni.request({
            url,
            method: 'GET',
            timeout: 5000,
            success: (res) => {
              const endTime = Date.now();
              const responseTime = endTime - startTime;
              resolve({
                url,
                success: true,
                statusCode: res.statusCode,
                responseTime,
                dataLength: res.data ? String(res.data).length : 0,
              });
            },
            fail: (error) => {
              const endTime = Date.now();
              const responseTime = endTime - startTime;
              resolve({
                url,
                success: false,
                error: error.errMsg || error,
                responseTime,
              });
            },
          });
        });

        results.push(result);

        if (result.success) {
          addLog('success', `✅ ${url} - ${result.statusCode} (${result.responseTime}ms)`);
        } else {
          addLog('error', `❌ ${url} - ${result.error} (${result.responseTime}ms)`);
        }
      } catch (error) {
        addLog('error', `❌ ${url} - 测试异常: ${error}`);
        results.push({ url, success: false, error: String(error) });
      }
    }

    const successCount = results.filter((r: any) => r.success).length;
    const totalCount = results.length;

    addLog('info', `网络连接测试完成: ${successCount}/${totalCount} 成功`);
    uni.showModal({
      title: '网络连接测试',
      content: `测试完成: ${successCount}/${totalCount} 成功`,
      showCancel: false,
    });
  };

  // 游戏数据管理方法
  const viewGameStats = () => {
    const profile = userProfileManager.getCurrentProfile();
    if (profile) {
      const gamesPlayed = profile.gamesPlayed || {};
      const gameList = Object.entries(gamesPlayed).map(([gameId, count]) => `${gameId}: ${count}次`).join('\n');

      addLog('info', '游戏统计数据已输出到控制台');
      console.log('游戏统计:', gamesPlayed);

      uni.showModal({
        title: '游戏统计',
        content: `总游戏次数: ${gameStats.value.totalGames}\n游戏类型: ${gameStats.value.gameTypes}种\n\n${gameList}`,
        showCancel: false,
      });
    } else {
      addLog('error', '未找到游戏数据');
    }
  };

  const clearGameHistory = () => {
    uni.showModal({
      title: '确认清理',
      content: '这将清除所有游戏历史记录，但保留用户等级和总积分',
      success: async (res) => {
        if (res.confirm) {
          try {
            // 这里可以添加清理游戏历史的逻辑
            addLog('success', '游戏历史清理成功');
            uni.showToast({ title: '清理成功', icon: 'success' });
          } catch (error) {
            addLog('error', `清理失败: ${error}`);
          }
        }
      },
    });
  };

  const recalculateScore = async () => {
    try {
      // 这里可以添加重新计算积分的逻辑
      addLog('info', '积分重新计算完成');
      await loadUserData();
      uni.showToast({ title: '计算完成', icon: 'success' });
    } catch (error) {
      addLog('error', `计算失败: ${error}`);
    }
  };

  const exportGameData = () => {
    const profile = userProfileManager.getCurrentProfile();
    if (profile) {
      const gameData = {
        exportTime: new Date().toISOString(),
        gamesPlayed: profile.gamesPlayed,
        totalScore: profile.totalScore,
        experience: profile.experience,
        level: profile.level,
      };

      console.log('导出的游戏数据:', gameData);
      addLog('success', '游戏数据已导出到控制台');
      uni.showToast({ title: '已导出到控制台', icon: 'success' });
    } else {
      addLog('error', '未找到游戏数据');
    }
  };

  const clearGameCache = () => {
    uni.showModal({
      title: '确认清除',
      content: '这将清除游戏相关的缓存数据',
      success: (res) => {
        if (res.confirm) {
          try {
            // 这里可以添加清除游戏缓存的逻辑
            addLog('success', '游戏缓存清除成功');
            uni.showToast({ title: '清除成功', icon: 'success' });
          } catch (error) {
            addLog('error', `清除失败: ${error}`);
          }
        }
      },
    });
  };

  // 系统缓存管理方法
  const viewStorageUsage = () => {
    try {
      const storageInfo = uni.getStorageInfoSync();
      const usage = {
        keys: storageInfo.keys.length,
        currentSize: storageInfo.currentSize,
        limitSize: storageInfo.limitSize,
        usagePercent: ((storageInfo.currentSize / storageInfo.limitSize) * 100).toFixed(1)
      };

      addLog('info', `存储使用情况: ${usage.currentSize}KB/${usage.limitSize}KB (${usage.usagePercent}%)`);
      console.log('存储详情:', storageInfo);

      uni.showModal({
        title: '存储使用情况',
        content: `存储键数量: ${usage.keys}\n当前大小: ${usage.currentSize}KB\n限制大小: ${usage.limitSize}KB\n使用率: ${usage.usagePercent}%`,
        showCancel: false,
      });
    } catch (error) {
      addLog('error', `获取存储信息失败: ${error}`);
    }
  };

  const clearAllCache = () => {
    uni.showModal({
      title: '确认清理',
      content: '这将清除所有缓存数据，但保留用户核心数据',
      confirmText: '确认清理',
      confirmColor: '#ff9500',
      success: (res) => {
        if (res.confirm) {
          try {
            hexagramCache.clearAll();
            storage.cleanExpiredCache();
            addLog('success', '所有缓存清理成功');
            uni.showToast({ title: '清理成功', icon: 'success' });
          } catch (error) {
            addLog('error', `清理失败: ${error}`);
            uni.showToast({ title: '清理失败', icon: 'error' });
          }
        }
      },
    });
  };

  const fullReset = () => {
    uni.showModal({
      title: '⚠️ 危险操作',
      content: '这将清除所有数据，包括用户数据、游戏记录、缓存等，此操作不可恢复！',
      confirmText: '确认重置',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          uni.showModal({
            title: '最后确认',
            content: '您确定要执行完全重置吗？这将删除所有数据！',
            confirmText: '确认删除',
            confirmColor: '#ff4757',
            success: async (res2) => {
              if (res2.confirm) {
                try {
                  uni.clearStorageSync();
                  addLog('success', '完全重置成功，所有数据已清除');
                  uni.showToast({ title: '重置成功', icon: 'success' });

                  // 重新初始化
                  setTimeout(() => {
                    loadUserData();
                  }, 1000);
                } catch (error) {
                  addLog('error', `重置失败: ${error}`);
                  uni.showToast({ title: '重置失败', icon: 'error' });
                }
              }
            },
          });
        }
      },
    });
  };

  // 清除日志
  const clearLogs = () => {
    operationLogs.value = [];
  };

  // 页面加载时初始化
  onMounted(() => {
    loadUserData();
    addLog('info', '数据管理页面已加载');
  });
</script>

<style lang="scss" scoped>
  .container {
    padding: 32rpx;
    background: #f5f5f5;
    min-height: 100vh;
  }

  .stats-overview {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16rpx;
    margin-bottom: 32rpx;
  }

  .stats-card {
    background: white;
    border-radius: 16rpx;
    padding: 24rpx;
    display: flex;
    align-items: center;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  }

  .stats-icon {
    font-size: 48rpx;
    margin-right: 16rpx;
  }

  .stats-info {
    flex: 1;
  }

  .stats-number {
    display: block;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 4rpx;
  }

  .stats-label {
    display: block;
    font-size: 24rpx;
    color: #666;
  }

  .section {
    background: white;
    border-radius: 24rpx;
    margin-bottom: 32rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  }

  .section-title {
    display: block;
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    padding: 32rpx 32rpx 0;
    margin-bottom: 24rpx;
  }

  .option-item {
    display: flex;
    align-items: center;
    padding: 24rpx 32rpx;
    border-bottom: 2rpx solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }
  }

  .option-info {
    flex: 1;
    margin-right: 24rpx;
  }

  .option-name {
    display: block;
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 6rpx;
  }

  .option-desc {
    display: block;
    font-size: 24rpx;
    color: #666;
    line-height: 1.4;
  }

  .option-btn {
    padding: 12rpx 24rpx;
    border-radius: 12rpx;
    font-size: 24rpx;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;

    &.primary {
      background: linear-gradient(135deg, #8b4513, #a0522d);
      color: white;
      box-shadow: 0 4rpx 12rpx rgba(139, 69, 19, 0.3);

      &:active {
        background: linear-gradient(135deg, #7a3e11, #8b4513);
        transform: translateY(2rpx);
      }
    }

    &.secondary {
      background: #f8f9fa;
      color: #495057;
      border: 2rpx solid #dee2e6;

      &:active {
        background: #e9ecef;
        transform: translateY(2rpx);
      }
    }

    &.warning {
      background: linear-gradient(135deg, #ff9500, #ff8c00);
      color: white;
      box-shadow: 0 4rpx 12rpx rgba(255, 149, 0, 0.3);

      &:active {
        background: linear-gradient(135deg, #ff8c00, #ff7f00);
        transform: translateY(2rpx);
      }
    }

    &.danger {
      background: linear-gradient(135deg, #ff4757, #ff3742);
      color: white;
      box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.3);

      &:active {
        background: linear-gradient(135deg, #ff3742, #ff2837);
        transform: translateY(2rpx);
      }
    }
  }

  .log-container {
    max-height: 400rpx;
    overflow-y: auto;
    background: #f8f9fa;
    margin: 0 32rpx 24rpx;
    border-radius: 12rpx;
    padding: 16rpx;
  }

  .log-item {
    display: block;
    font-size: 22rpx;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    line-height: 1.4;
    margin-bottom: 6rpx;
    padding: 6rpx 12rpx;
    border-radius: 6rpx;

    &.info {
      color: #495057;
      background: rgba(108, 117, 125, 0.1);
    }

    &.success {
      color: #155724;
      background: rgba(40, 167, 69, 0.1);
    }

    &.warning {
      color: #856404;
      background: rgba(255, 193, 7, 0.1);
    }

    &.error {
      color: #721c24;
      background: rgba(220, 53, 69, 0.1);
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
</style>
