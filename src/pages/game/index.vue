<template>
  <PageWrapper :show-logo="true" :show-back="false" title="游戏大厅">
    <view class="container">
      <!-- 快捷统计 -->
      <view class="quick-stats">
        <!-- 左侧等级区域 -->
        <view class="level-section" @tap="goToProfile">
          <view class="level-header">
            <text class="level-icon">{{ userLevel.icon }}</text>
            <view class="level-info">
              <view class="level-title">{{ userLevel.title }}</view>
              <view class="level-score">{{ totalScore }}分</view>
            </view>
          </view>
          <view class="level-next">{{ scoreToNext }}分升级</view>
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: levelProgress + '%' }"></view>
          </view>
        </view>

        <!-- 分割线 -->
        <view class="divider"></view>

        <!-- 右侧统计区域 -->
        <view class="stats-section">
          <view class="stat-item" @tap="goToProfile">
            <text class="stat-value">{{ totalGames }}</text>
            <text class="stat-label">总游戏</text>
          </view>
          <view class="stat-item" @tap="goToAchievements">
            <text class="stat-value">{{ totalAchievements }}</text>
            <text class="stat-label">成就</text>
          </view>
        </view>
      </view>

      <!-- 游戏分类 -->
      <view class="category-tabs">
        <view
          v-for="category in categories"
          :key="category.id"
          :class="['category-tab', { active: activeCategory === category.id }]"
          @tap="switchCategory(category.id)"
        >
          {{ category.name }}
        </view>
      </view>

      <!-- 游戏列表 -->
      <view class="game-section">
        <GameCard v-for="game in filteredGames" :key="game.id" :game="game" @play="playGame" />
      </view>

      <!-- 添加TabBar -->
      <YiTabBar />
    </view>
  </PageWrapper>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue';
  import { onShow } from '@dcloudio/uni-app';
  import YiTabBar from '@/components/YiTabBar.vue';
  import GameCard from '@/games/shared/components/GameCard.vue';
  import PageWrapper from '@/components/PageWrapper.vue';
  import { getAllGames, GAME_CATEGORIES } from '@/games/registry';
  import { gameService } from '@/games/shared/services/game-service';
  import type { GameRegistry, GameCategory } from '@/games/shared/types';
  import type { UserLevel } from '@/games/shared/services/level/level-calculator';

  // 游戏分类
  const categories: GameCategory[] = GAME_CATEGORIES;

  // 游戏列表 - 使用响应式数据
  const games = ref<GameRegistry[]>(getAllGames());

  const activeCategory = ref('all');
  const totalGames = ref(0);
  const totalScore = ref(0);
  const userLevel = ref<UserLevel>({
    level: 1,
    title: '初学者',
    description: '初学者 - 等级 1',
    minExperience: 0,
    maxExperience: 1000,
    color: '#95A5A6',
    icon: '🌱',
    rewards: [],
  });
  const levelProgress = ref(0);
  const scoreToNext = ref(0);
  const totalAchievements = ref(0);

  // 过滤游戏
  const filteredGames = computed(() => {
    return games.value.filter((game) => {
      if (activeCategory.value === 'all') return true;
      return game.category === activeCategory.value;
    });
  });

  // 切换分类
  const switchCategory = (categoryId: string) => {
    activeCategory.value = categoryId;
  };

  // 开始游戏
  const playGame = (game: GameRegistry) => {
    uni.navigateTo({ url: game.route });
  };

  // 导航到用户档案
  const goToProfile = () => {
    uni.navigateTo({ url: '/pages/user/profile' });
  };

  // 导航到成就页面
  const goToAchievements = () => {
    uni.navigateTo({ url: '/pages/user/achievements' });
  };

  // 加载游戏统计
  const loadGameStats = () => {
    try {
      const userStats = gameService.getUserStats();
      console.log('用户统计数据:', userStats);

      if (userStats && userStats.gamesPlayed) {
        totalGames.value = Object.values(userStats.gamesPlayed).reduce(
          (sum: number, count: unknown) => sum + (count as number),
          0
        );
        console.log('总游戏数:', totalGames.value);

        // 为每个游戏加载统计数据
        games.value.forEach((game: GameRegistry) => {
          const gameStat = gameService.getGameStats(game.id);
          console.log(`游戏 ${game.id} 统计:`, gameStat);

          const bestScore = typeof gameStat?.bestScore === 'number' ? gameStat.bestScore : 0;
          const playCount = typeof gameStat?.gamesPlayed === 'number' ? gameStat.gamesPlayed : 0;

          console.log(`游戏 ${game.id} - 最高分: ${bestScore}, 游戏次数: ${playCount}`);

          // 更新游戏对象的统计数据
          game.bestScore = bestScore;
          game.playCount = playCount;
        });
      } else {
        console.warn('用户统计数据为空或无效');
        totalGames.value = 0;
      }
    } catch (error) {
      console.error('加载游戏统计失败:', error);
      totalGames.value = 0;
    }
  };

  // 加载用户档案信息
  const loadUserProfile = () => {
    try {
      const userStats = gameService.getUserStats();
      console.log('用户档案数据:', userStats);

      if (userStats) {
        totalScore.value = (userStats.totalScore as number) || 0;
        console.log('总分数:', totalScore.value);

        // 获取完整的等级信息
        const levelInfo = gameService.getUserLevel();
        console.log('等级信息:', levelInfo);
        if (levelInfo) {
          userLevel.value = levelInfo;
        }

        // 获取等级进度
        levelProgress.value = gameService.getLevelProgress() || 0;
        scoreToNext.value = gameService.getExperienceToNextLevel() || 0;
        totalAchievements.value = Array.isArray(userStats.achievements) ? userStats.achievements.length : 0;

        console.log(
          '等级进度:',
          levelProgress.value,
          '升级所需:',
          scoreToNext.value,
          '成就数:',
          totalAchievements.value
        );
      } else {
        console.warn('用户档案数据为空');
        // 设置默认值
        totalScore.value = 0;
        levelProgress.value = 0;
        scoreToNext.value = 1000;
        totalAchievements.value = 0;
      }
    } catch (error) {
      console.error('加载用户档案失败:', error);
      // 设置默认值
      totalScore.value = 0;
      levelProgress.value = 0;
      scoreToNext.value = 1000;
      totalAchievements.value = 0;
    }
  };

  // 初始化数据
  const initializeData = async () => {
    try {
      console.log('开始初始化数据...');
      // 初始化游戏服务和用户
      const userProfile = await gameService.initializeUser();
      console.log('游戏服务初始化完成，用户档案:', userProfile);

      // 加载数据
      loadGameStats();
      loadUserProfile();
      console.log('数据加载完成');
    } catch (error) {
      console.error('初始化失败:', error);
    }
  };

  // 刷新数据的函数
  const refreshData = async () => {
    console.log('刷新数据...');
    try {
      // 刷新用户数据（从存储重新加载）
      console.log('刷新用户数据...');
      await gameService.refreshUserData();

      loadGameStats();
      loadUserProfile();

      console.log('数据刷新完成');
    } catch (error) {
      console.error('刷新数据失败:', error);
    }
  };

  onMounted(async () => {
    await initializeData();
  });

  // 使用uni-app的页面生命周期
  onShow(() => {
    console.log('页面显示，检查是否需要刷新数据...');

    // 只在必要时刷新数据，避免不必要的重新加载
    // 检查用户档案是否存在，如果不存在才刷新
    const currentProfile = gameService.getCurrentUser();
    if (!currentProfile || currentProfile.totalScore === 0) {
      console.log('用户档案不存在或数据异常，刷新数据...');
      setTimeout(() => {
        refreshData();
      }, 100);
    } else {
      console.log('用户档案正常，跳过刷新');
      // 只更新显示数据，不重新加载
      loadGameStats();
      loadUserProfile();
    }
  });
</script>

<style lang="scss" scoped>
  .container {
    min-height: 100vh;
    background-color: #f8f4e9;
    padding: 40rpx;
    padding-bottom: 152rpx; // 为底部TabBar留出空间
  }

  .quick-stats {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
    border-radius: 32rpx;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
    margin-bottom: 40rpx;
    padding: 40rpx;
    display: flex;
    align-items: center; 

    // 左侧等级区域
    .level-section {
      flex: 0 0 45%;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
      }

      .level-header {
        display: flex;
        gap: 28rpx;
        margin-bottom: 16rpx;

        .level-icon {
          font-size: 48rpx;
          line-height: 1;
        }

        .level-info {
          text-align: left;

          .level-title {
            font-size: 36rpx;
            font-weight: bold;
            color: #8b4513;
            margin-bottom: 8rpx;
          }

          .level-score {
            font-size: 32rpx;
            color: #d2691e;
            font-weight: 600;
          }
        }
      }

      .level-next {
        font-size: 24rpx;
        color: #666;
        margin-bottom: 24rpx;
      }

      .progress-bar {
        height: 12rpx;
        background: rgba(139, 69, 19, 0.2);
        border-radius: 6rpx;
        overflow: hidden;
        margin: 0 auto;
        max-width: 240rpx;

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #8b4513, #a0522d);
          border-radius: 6rpx;
          transition: width 0.3s ease;
        }
      }
    }

    // 分割线
    .divider {
      flex: 0 0 auto;
      width: 2rpx;
      height: 120rpx;
      background: linear-gradient(to bottom, transparent, rgba(139, 69, 19, 0.3), transparent);
      margin: 0 20rpx;
    }

    // 右侧统计区域
    .stats-section {
      flex: 0 0 45%;
      display: flex;
      gap: 40rpx;

      .stat-item {
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        padding: 16rpx 24rpx;
        border-radius: 16rpx;

        &:active {
          transform: scale(0.95);
          background: rgba(139, 69, 19, 0.05);
        }

        .stat-value {
          display: block;
          font-size: 42rpx;
          font-weight: bold;
          color: #8b4513;
          margin-bottom: 8rpx;
          line-height: 1;
        }

        .stat-label {
          font-size: 24rpx;
          color: #666;
        }
      }
    }
  }

  .category-tabs {
    display: flex;
    gap: 24rpx;
    margin-bottom: 40rpx;
    padding: 0 8rpx;

    .category-tab {
      padding: 16rpx 32rpx;
      border-radius: 16rpx;
      font-size: 28rpx;
      color: #666;
      background: rgba(255, 255, 255, 0.6);
      transition: all 0.3s ease;

      &.active {
        background: #8b4513;
        color: white;
        font-weight: bold;
      }
    }
  }

  .game-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 32rpx;
    padding: 0 8rpx;
  }
</style>
