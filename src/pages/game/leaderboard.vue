<template>
  <PageWrapper title="排行榜" :show-logo="false">
    <view class="container">
      <!-- 排行榜类型切换 -->
      <view class="leaderboard-tabs">
        <view
          v-for="tab in leaderboardTabs"
          :key="tab.id"
          :class="['tab-item', { active: activeTab === tab.id }]"
          @tap="switchTab(tab.id)"
        >
          {{ tab.name }}
        </view>
      </view>

      <!-- 游戏选择（非全网排行榜时显示） -->
      <view v-if="activeTab !== 'global'" class="game-selector">
        <view class="game-tabs">
          <view
            v-for="game in gameOptions"
            :key="game.id"
            :class="['game-tab', { active: activeGame === game.id }]"
            @tap="switchGame(game.id)"
          >
            {{ game.name }}
          </view>
        </view>

        <!-- 难度选择 -->
        <view class="difficulty-tabs">
          <view
            v-for="difficulty in difficultyOptions"
            :key="difficulty.value"
            :class="['difficulty-tab', { active: activeDifficulty === difficulty.value }]"
            @tap="switchDifficulty(difficulty.value)"
          >
            {{ difficulty.name }}
          </view>
        </view>
      </view>

      <!-- 我的排名 -->
      <view v-if="myRank > 0" class="my-rank-card">
        <view class="rank-info">
          <text class="rank-number">#{{ myRank }}</text>
          <view class="rank-details">
            <text class="rank-label">我的排名</text>
            <text class="rank-score">{{ myScore }}分</text>
          </view>
        </view>
      </view>

      <!-- 排行榜列表 -->
      <view class="leaderboard-list">
        <view
          v-for="entry in leaderboardData"
          :key="entry.userId"
          :class="['leaderboard-item', { 'is-me': entry.userId === currentUserId }]"
        >
          <view class="rank-badge" :class="getRankClass(entry.rank)">
            <text v-if="entry.rank <= 3" class="rank-icon">{{ getRankIcon(entry.rank) }}</text>
            <text v-else class="rank-text">{{ entry.rank }}</text>
          </view>

          <view class="user-info">
            <text class="username">{{ entry.username }}</text>
            <view class="user-meta">
              <text class="user-level">{{ getUserLevelDisplay(entry.score) }}</text>
              <text class="user-score">{{ entry.score }}分</text>
            </view>
          </view>

          <view class="entry-time">
            <text>{{ formatTime(entry.lastUpdated) }}</text>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="leaderboardData.length === 0" class="empty-state">
        <text class="empty-icon">📊</text>
        <text class="empty-text">暂无排行榜数据</text>
      </view>
    </view>
  </PageWrapper>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, watch } from 'vue';
  import PageWrapper from '@/components/PageWrapper.vue';
  import { getAllGames } from '@/games/registry';
  import { gameService } from '@/games/shared/services/game-service';
  import { getLevelTitle } from '@/games/shared/services/level/level-titles';
  import type { GameDifficulty } from '@/games/shared/types';
  import type { RankingEntry } from '@/games/shared/services/ranking/ranking-engine';

  const activeTab = ref('global');
  const activeGame = ref('hexagram-match');
  const activeDifficulty = ref<GameDifficulty>('medium');
  const leaderboardData = ref<RankingEntry[]>([]);
  const myRank = ref(0);
  const myScore = ref(0);
  const currentUserId = ref('');

  // 排行榜类型
  const leaderboardTabs = [
    { id: 'global', name: '全网积分' },
    { id: 'game', name: '游戏排行' },
  ];

  // 游戏选项（从注册中心获取）
  const gameOptions = computed(() => {
    return getAllGames().map((game) => ({
      id: game.id,
      name: game.name,
    }));
  });

  // 难度选项
  const difficultyOptions = [
    { value: 'easy' as GameDifficulty, name: '简单' },
    { value: 'medium' as GameDifficulty, name: '中等' },
    { value: 'hard' as GameDifficulty, name: '困难' },
  ];

  // 获取排名图标
  const getRankIcon = (rank: number): string => {
    const icons = { 1: '🥇', 2: '🥈', 3: '🥉' };
    return icons[rank as keyof typeof icons] || '';
  };

  // 获取排名样式类
  const getRankClass = (rank: number): string => {
    if (rank === 1) return 'gold';
    if (rank === 2) return 'silver';
    if (rank === 3) return 'bronze';
    return 'normal';
  };

  // 格式化时间
  const formatTime = (timestamp: number): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return '今天';
    } else if (diffDays === 1) {
      return '昨天';
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString();
    }
  };

  // 获取用户等级显示（等级 + 称号）
  const getUserLevelDisplay = (score: number): string => {
    const level = Math.floor(score / 1000) + 1;
    const title = getLevelTitle(level);
    return `${title} Lv.${level}`;
  };

  // 切换排行榜类型
  const switchTab = (tabId: string) => {
    activeTab.value = tabId;
    loadLeaderboard();
  };

  // 切换游戏
  const switchGame = (gameId: string) => {
    activeGame.value = gameId;
    loadLeaderboard();
  };

  // 切换难度
  const switchDifficulty = (difficulty: GameDifficulty) => {
    activeDifficulty.value = difficulty;
    loadLeaderboard();
  };

  // 加载排行榜数据
  const loadLeaderboard = async () => {
    try {
      if (activeTab.value === 'global') {
        leaderboardData.value = await gameService.getRanking('global');
        myRank.value = await gameService.getUserRank(currentUserId.value, 'global');
      } else {
        leaderboardData.value = await gameService.getRanking('game', activeGame.value);
        myRank.value = await gameService.getUserRank(currentUserId.value, 'game', activeGame.value);
      }

      // 获取我的分数
      const myEntry = leaderboardData.value.find((entry) => entry.userId === currentUserId.value);
      myScore.value = myEntry?.score || 0;
    } catch (error) {
      console.error('加载排行榜失败:', error);
      leaderboardData.value = [];
      myRank.value = 0;
      myScore.value = 0;
    }
  };

  // 初始化
  onMounted(async () => {
    // 初始化用户并获取用户ID
    const userProfile = await gameService.initializeUser();
    currentUserId.value = userProfile.userId;
    loadLeaderboard();
  });

  // 监听参数变化
  watch([activeTab, activeGame, activeDifficulty], () => {
    loadLeaderboard();
  });
</script>

<style lang="scss" scoped>
  .container {
    min-height: 100vh;
    background-color: #f8f4e9;
    padding: 40rpx;
  }

  .leaderboard-tabs {
    display: flex;
    gap: 24rpx;
    margin-bottom: 40rpx;

    .tab-item {
      flex: 1;
      padding: 24rpx;
      background: white;
      border-radius: 16rpx;
      text-align: center;
      font-size: 32rpx;
      color: #666;
      transition: all 0.3s ease;

      &.active {
        background: #8b4513;
        color: white;
        font-weight: bold;
      }
    }
  }

  .game-selector {
    margin-bottom: 40rpx;

    .game-tabs {
      display: flex;
      gap: 16rpx;
      margin-bottom: 24rpx;

      .game-tab {
        padding: 16rpx 32rpx;
        background: white;
        border-radius: 16rpx;
        font-size: 28rpx;
        color: #666;
        transition: all 0.3s ease;

        &.active {
          background: #8b4513;
          color: white;
          font-weight: bold;
        }
      }
    }

    .difficulty-tabs {
      display: flex;
      gap: 16rpx;

      .difficulty-tab {
        padding: 12rpx 24rpx;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 12rpx;
        font-size: 24rpx;
        color: #666;
        transition: all 0.3s ease;

        &.active {
          background: #a0522d;
          color: white;
          font-weight: bold;
        }
      }
    }
  }

  .my-rank-card {
    background: linear-gradient(135deg, #8b4513, #a0522d);
    border-radius: 24rpx;
    padding: 32rpx;
    margin-bottom: 40rpx;

    .rank-info {
      display: flex;
      align-items: center;

      .rank-number {
        font-size: 64rpx;
        font-weight: bold;
        color: white;
        margin-right: 32rpx;
      }

      .rank-details {
        flex: 1;

        .rank-label {
          display: block;
          font-size: 28rpx;
          color: rgba(255, 255, 255, 0.8);
          margin-bottom: 8rpx;
        }

        .rank-score {
          font-size: 36rpx;
          font-weight: bold;
          color: white;
        }
      }
    }
  }

  .leaderboard-list {
    .leaderboard-item {
      display: flex;
      align-items: center;
      background: white;
      border-radius: 24rpx;
      padding: 32rpx;
      margin-bottom: 16rpx;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);

      &.is-me {
        border: 4rpx solid #8b4513;
        background: #f8f4e9;
      }

      .rank-badge {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 32rpx;
        font-weight: bold;

        &.gold {
          background: linear-gradient(135deg, #ffd700, #ffed4e);
          color: #b8860b;
        }

        &.silver {
          background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
          color: #666;
        }

        &.bronze {
          background: linear-gradient(135deg, #cd7f32, #daa520);
          color: #8b4513;
        }

        &.normal {
          background: #f0f0f0;
          color: #666;
        }

        .rank-icon {
          font-size: 40rpx;
        }

        .rank-text {
          font-size: 32rpx;
        }
      }

      .user-info {
        flex: 1;

        .username {
          display: block;
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 8rpx;
        }

        .user-meta {
          display: flex;
          gap: 24rpx;

          .user-level {
            font-size: 24rpx;
            color: #8b4513;
            background: rgba(139, 69, 19, 0.1);
            padding: 4rpx 12rpx;
            border-radius: 8rpx;
          }

          .user-score {
            font-size: 24rpx;
            color: #666;
          }
        }
      }

      .entry-time {
        font-size: 24rpx;
        color: #999;
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 120rpx 40rpx;

    .empty-icon {
      font-size: 96rpx;
      margin-bottom: 32rpx;
      opacity: 0.5;
    }

    .empty-text {
      font-size: 32rpx;
      color: #999;
    }
  }
</style>
