<template>
  <PageWrapper :show-back="true" title="即将推出">
    <view class="container">
      <view class="coming-soon">
        <view class="icon">🚧</view>
        <view class="title">游戏开发中</view>
        <view class="description">
          这个游戏正在紧张开发中，<br />
          敬请期待！
        </view>
        <button class="back-button" @tap="goBack">返回游戏大厅</button>
      </view>
    </view>
  </PageWrapper>
</template>

<script setup lang="ts">
  import PageWrapper from '@/components/PageWrapper.vue';

  const goBack = () => {
    uni.navigateBack();
  };
</script>

<style lang="scss" scoped>
  .container {
    min-height: 100vh;
    background-color: #f8f4e9;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40rpx;
  }

  .coming-soon {
    text-align: center;
    background: white;
    border-radius: 40rpx;
    padding: 80rpx 40rpx;
    box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.1);
    max-width: 600rpx;
    width: 100%;

    .icon {
      font-size: 128rpx;
      margin-bottom: 40rpx;
    }

    .title {
      font-size: 48rpx;
      font-weight: bold;
      color: #8b4513;
      margin-bottom: 32rpx;
    }

    .description {
      font-size: 32rpx;
      color: #666;
      line-height: 1.6;
      margin-bottom: 64rpx;
    }

    .back-button {
      background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
      color: white;
      font-size: 32rpx;
      padding: 24rpx 48rpx;
      border-radius: 24rpx;
      border: none;
      font-weight: 600;
      box-shadow: 0 8rpx 32rpx rgba(139, 69, 19, 0.3);
    }
  }
</style>
