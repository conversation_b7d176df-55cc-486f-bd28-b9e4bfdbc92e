<template>
  <PageWrapper :show-logo="true" logo-label="易境时空" :show-back="false">
    <view class="container">
      <view class="welcome">
        <text class="subtitle">探索易经智慧，指引今日生活</text>
      </view>

      <!-- <view class="nav-icons">
        <YiIcon name="search" class="nav-icon" size="20" />
        <YiIcon name="menu" class="nav-icon" size="20" />
      </view> -->

      <!-- 64gua.webp图片 -->
      <view class="gua-image-bg">
        <!-- #ifdef MP-WEIXIN -->
        <image src="/static/images/taiji.png" mode="aspectFill" style="width: 100%; height: 100%"></image>
        <!-- #endif -->
        <!-- #ifdef H5 -->
        <!-- H5平台使用CSS背景图 -->
        <!-- #endif -->
      </view>

      <TodayHexagram />
    </view>

    <YiTabBar />
  </PageWrapper>
</template>

<script lang="ts">
  import { defineComponent } from 'vue';
  import TodayHexagram from '@/components/TodayHexagram.vue';
  import YiTabBar from '@/components/YiTabBar.vue';
  import PageWrapper from '@/components/PageWrapper.vue';

  export default defineComponent({
    name: 'IndexPage',
    components: { TodayHexagram, YiTabBar, PageWrapper },
  });
</script>

<style lang="scss">
  /* 小程序专用：页面容器高度限制方案 */
  /* #ifdef MP-WEIXIN */
  page {
    height: 100vh; /* 固定页面高度 */
    overflow: hidden; /* 禁用页面级滚动 */
  }
  /* #endif */

  .container {
    padding: 40rpx;
    padding-top: 0; /* 顶部间距由PageWrapper统一管理 */
    position: relative;
    box-sizing: border-box;

    /* 小程序：限制容器高度，内部滚动 */
    /* #ifdef MP-WEIXIN */
    height: calc(100vh - 176rpx - 112rpx); /* 全屏 - 导航栏 - TabBar */
    overflow-y: auto; /* 容器内部滚动 */
    -webkit-overflow-scrolling: touch; /* iOS滚动优化 */
    padding-bottom: 40rpx; /* 底部内边距 */
    /* #endif */

    /* H5平台：保持原有滚动方式 */
    /* #ifndef MP-WEIXIN */
    min-height: auto;
    padding-bottom: 140rpx; /* TabBar(112rpx) + 额外间距(28rpx) */
    /* #endif */
  }

  /* 导航栏右侧图标样式 */
  .nav-icons {
    display: flex;
    align-items: center;
    gap: 24rpx;
  }

  .nav-icon {
    color: #666666;
    opacity: 0.8;
    transition: opacity 0.2s;

    &:active {
      opacity: 0.6;
    }
  }

  .welcome {
    margin-top: 40rpx; /* 顶部间距 */
    margin-bottom: 50rpx;

    .subtitle {
      font-size: 36rpx;
      color: #8b4513;
    }
  }

  .gua-image-bg {
    width: 100%;
    height: 480rpx;
    border-radius: 24rpx;
    overflow: hidden;
    margin-bottom: 50rpx;
    opacity: 0.9;

    /* #ifdef H5 */
    background: url('/static/images/taiji.png') center center / cover no-repeat;
    /* #endif */

    /* #ifdef MP-WEIXIN */
    position: relative;
    /* #endif */
  }

  /* 移除自定义tab-bar相关的样式 */
</style>
