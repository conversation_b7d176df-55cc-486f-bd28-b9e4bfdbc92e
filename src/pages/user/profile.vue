<template>
  <PageWrapper title="个人资料" :show-back="true">
    <view class="container">
      <!-- 头像区域 -->
      <view class="avatar-section">
        <view class="avatar-container" @tap="changeAvatar">
          <text class="avatar">{{ userInfo.avatar || '👤' }}</text>
          <view class="avatar-edit">
            <text class="edit-icon">📷</text>
          </view>
        </view>
        <text class="avatar-tip">点击更换头像</text>
      </view>

      <!-- 个人信息表单 -->
      <view class="form-section">
        <!-- 用户名 -->
        <view class="form-item" @tap="editUsername">
          <view class="item-label">
            <text class="label-text">用户名</text>
          </view>
          <view class="item-content">
            <text class="content-text">{{ userInfo.username || '未设置' }}</text>
            <text class="arrow">›</text>
          </view>
        </view>

        <!-- 性别 -->
        <view class="form-item" @tap="editGender">
          <view class="item-label">
            <text class="label-text">性别</text>
          </view>
          <view class="item-content">
            <text class="content-text">{{ getGenderText(userInfo.gender) }}</text>
            <text class="arrow">›</text>
          </view>
        </view>

        <!-- 地区 -->
        <view class="form-item" @tap="editRegion">
          <view class="item-label">
            <text class="label-text">地区</text>
          </view>
          <view class="item-content">
            <text class="content-text">{{ userInfo.region || '未设置' }}</text>
            <text class="arrow">›</text>
          </view>
        </view>

        <!-- 手机号 -->
        <view class="form-item" @tap="editPhone">
          <view class="item-label">
            <text class="label-text">手机号</text>
          </view>
          <view class="item-content">
            <text class="content-text">{{ formatPhone(userInfo.phone) }}</text>
            <text class="arrow">›</text>
          </view>
        </view>

        <!-- 个性签名 -->
        <view class="form-item" @tap="editSignature">
          <view class="item-label">
            <text class="label-text">个性签名</text>
          </view>
          <view class="item-content">
            <text class="content-text">{{ userInfo.signature || '这个人很懒，什么都没留下' }}</text>
            <text class="arrow">›</text>
          </view>
        </view>
      </view>

      <!-- 账户信息 -->
      <view class="account-section">
        <view class="section-title">账户信息</view>
        <view class="form-item">
          <view class="item-label">
            <text class="label-text">用户ID</text>
          </view>
          <view class="item-content">
            <text class="content-text">{{ userInfo.userId || '未知' }}</text>
          </view>
        </view>

        <view class="form-item">
          <view class="item-label">
            <text class="label-text">注册时间</text>
          </view>
          <view class="item-content">
            <text class="content-text">{{ formatDate(userInfo.createdAt) }}</text>
          </view>
        </view>
      </view>
    </view>
  </PageWrapper>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import PageWrapper from '@/components/PageWrapper.vue';
  import { gameService } from '@/games/shared/services/game-service';

  // 用户信息接口
  interface UserInfo {
    userId: string;
    username: string;
    avatar: string;
    gender: 'male' | 'female' | 'other' | '';
    region: string;
    phone: string;
    signature: string;
    createdAt: number;
  }

  // 用户信息数据
  const userInfo = ref<UserInfo>({
    userId: '',
    username: '',
    avatar: '👤',
    gender: '',
    region: '',
    phone: '',
    signature: '',
    createdAt: Date.now(),
  });

  // 头像选项
  const avatarOptions = ['👤', '🧑', '👩', '🧔', '👨', '🎓', '🤓', '😎', '🤔', '😊', '🌟', '🎯', '📚', '🎮'];

  // 获取性别文本
  const getGenderText = (gender: string): string => {
    const genderMap: Record<string, string> = {
      'male': '男',
      'female': '女',
      'other': '其他',
      '': '未设置'
    };
    return genderMap[gender] || '未设置';
  };

  // 格式化手机号
  const formatPhone = (phone: string): string => {
    if (!phone) return '未设置';
    if (phone.length === 11) {
      return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3');
    }
    return phone;
  };

  // 格式化日期
  const formatDate = (timestamp: number): string => {
    if (!timestamp) return '未知';
    const date = new Date(timestamp);
    return date.toLocaleDateString();
  };

  // 更换头像
  const changeAvatar = () => {
    uni.showActionSheet({
      itemList: avatarOptions,
      success: (res) => {
        userInfo.value.avatar = avatarOptions[res.tapIndex];
        saveUserInfo();
      }
    });
  };

  // 编辑用户名
  const editUsername = () => {
    uni.showModal({
      title: '编辑用户名',
      editable: true,
      placeholderText: userInfo.value.username || '请输入用户名',
      success: (res) => {
        if (res.confirm && res.content && res.content.trim()) {
          userInfo.value.username = res.content.trim();
          saveUserInfo();
        }
      }
    });
  };

  // 编辑性别
  const editGender = () => {
    uni.showActionSheet({
      itemList: ['男', '女', '其他'],
      success: (res) => {
        const genderMap = ['male', 'female', 'other'];
        userInfo.value.gender = genderMap[res.tapIndex] as 'male' | 'female' | 'other';
        saveUserInfo();
      }
    });
  };

  // 编辑地区
  const editRegion = () => {
    uni.showModal({
      title: '编辑地区',
      editable: true,
      placeholderText: userInfo.value.region || '请输入地区',
      success: (res) => {
        if (res.confirm && res.content) {
          userInfo.value.region = res.content.trim();
          saveUserInfo();
        }
      }
    });
  };

  // 编辑手机号
  const editPhone = () => {
    uni.showModal({
      title: '编辑手机号',
      editable: true,
      placeholderText: userInfo.value.phone || '请输入手机号',
      success: (res) => {
        if (res.confirm && res.content) {
          const phone = res.content.trim();
          if (/^1[3-9]\d{9}$/.test(phone)) {
            userInfo.value.phone = phone;
            saveUserInfo();
          } else {
            uni.showToast({
              title: '手机号格式不正确',
              icon: 'none'
            });
          }
        }
      }
    });
  };

  // 编辑个性签名
  const editSignature = () => {
    uni.showModal({
      title: '编辑个性签名',
      editable: true,
      placeholderText: userInfo.value.signature || '请输入个性签名',
      success: (res) => {
        if (res.confirm) {
          userInfo.value.signature = res.content?.trim() || '';
          saveUserInfo();
        }
      }
    });
  };

  // 保存用户信息
  const saveUserInfo = async () => {
    try {
      await gameService.updateUserProfile({
        username: userInfo.value.username,
        avatar: userInfo.value.avatar,
        // signature: userInfo.value.signature, // 暂时注释，等待后端支持
        // 其他字段可以根据需要添加
      });

      uni.showToast({
        title: '保存成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('保存用户信息失败:', error);
      uni.showToast({
        title: '保存失败',
        icon: 'error'
      });
    }
  };

  // 加载用户数据
  const loadUserData = async () => {
    try {
      const userProfile = await gameService.initializeUser();

      if (userProfile) {
        userInfo.value = {
          userId: userProfile.userId,
          username: userProfile.username,
          avatar: userProfile.avatar || '👤',
          gender: (userProfile as any).gender || '',
          region: (userProfile as any).region || '',
          phone: (userProfile as any).phone || '',
          signature: (userProfile as any).signature || '',
          createdAt: userProfile.createdAt || Date.now(),
        };
      }
    } catch (error) {
      console.error('加载用户数据失败:', error);
    }
  };

  onMounted(async () => {
    await loadUserData();
  });
</script>

<style lang="scss" scoped>
  .container {
    min-height: 100vh;
    background-color: #f8f4e9;
    padding: 32rpx;
  }

  /* 头像区域 */
  .avatar-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 40rpx;

    .avatar-container {
      position: relative;
      margin-bottom: 16rpx;

      .avatar {
        width: 160rpx;
        height: 160rpx;
        background: #f0f0f0;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 80rpx;
        border: 4rpx solid white;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
      }

      .avatar-edit {
        position: absolute;
        bottom: 8rpx;
        right: 8rpx;
        width: 48rpx;
        height: 48rpx;
        background: #8b4513;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);

        .edit-icon {
          font-size: 24rpx;
          color: white;
        }
      }
    }

    .avatar-tip {
      font-size: 24rpx;
      color: #666;
    }
  }

  /* 表单区域 */
  .form-section,
  .account-section {
    background: white;
    border-radius: 24rpx;
    margin-bottom: 32rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      padding: 32rpx 32rpx 16rpx;
    }

    .form-item {
      display: flex;
      align-items: center;
      padding: 32rpx;
      border-bottom: 1rpx solid #f0f0f0;
      transition: background-color 0.2s ease;

      &:last-child {
        border-bottom: none;
      }

      &:active {
        background-color: rgba(0, 0, 0, 0.05);
      }

      .item-label {
        width: 160rpx;
        flex-shrink: 0;

        .label-text {
          font-size: 28rpx;
          color: #333;
        }
      }

      .item-content {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .content-text {
          font-size: 28rpx;
          color: #666;
          flex: 1;
          text-align: right;
          margin-right: 16rpx;
        }

        .arrow {
          font-size: 32rpx;
          color: #ccc;
        }
      }
    }
  }

</style>
