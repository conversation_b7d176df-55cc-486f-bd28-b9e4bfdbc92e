<template>
  <PageWrapper :show-back="true" title="游戏数据">
    <view class="game-data-page">
      <!-- Tab 导航 -->
      <view class="tab-navigation">
        <view
          v-for="tab in tabs"
          :key="tab.id"
          :class="['tab-item', { active: activeTab === tab.id }]"
          @tap="switchTab(tab.id)"
        >
          <text class="tab-icon">{{ tab.icon }}</text>
          <text class="tab-text">{{ tab.name }}</text>
        </view>
      </view>

      <!-- Tab 内容 -->
      <view class="tab-content">
        <!-- 数据总览 Tab -->
        <view v-if="activeTab === 'overview'" class="overview-section">
        <view class="section-title">
          <text class="title-text">📊 游戏数据总览</text>
        </view>
        
        <view class="stats-grid">
          <view class="stat-card">
            <view class="stat-icon">🏆</view>
            <view class="stat-content">
              <text class="stat-value">{{ totalScore }}</text>
              <text class="stat-label">总积分</text>
            </view>
          </view>
          
          <view class="stat-card">
            <view class="stat-icon">🎯</view>
            <view class="stat-content">
              <text class="stat-value">{{ winRate }}%</text>
              <text class="stat-label">胜率</text>
            </view>
          </view>
          
          <view class="stat-card">
            <view class="stat-icon">🔥</view>
            <view class="stat-content">
              <text class="stat-value">{{ maxStreak }}</text>
              <text class="stat-label">最高连胜</text>
            </view>
          </view>
          
          <view class="stat-card">
            <view class="stat-icon">📈</view>
            <view class="stat-content">
              <text class="stat-value">#{{ ranking }}</text>
              <text class="stat-label">当前排名</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 游戏分类统计 -->
      <view class="category-section">
        <view class="section-title">
          <text class="title-text">🎯 游戏分类统计</text>
        </view>
        
        <view class="category-list">
          <view 
            v-for="category in gameCategories" 
            :key="category.id"
            class="category-card"
          >
            <view class="category-header">
              <view class="category-info">
                <text class="category-icon">{{ category.icon }}</text>
                <text class="category-name">{{ category.name }}</text>
              </view>
              <view class="category-stats">
                <text class="win-rate">胜率 {{ category.winRate }}%</text>
              </view>
            </view>
            
            <view class="category-details">
              <view class="detail-item">
                <text class="detail-label">最高分:</text>
                <text class="detail-value">{{ category.highScore }}</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">游戏次数:</text>
                <text class="detail-value">{{ category.playCount }}次</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">平均用时:</text>
                <text class="detail-value">{{ category.avgTime }}秒</text>
              </view>
            </view>
          </view>
        </view>
      </view>

        </view>

        <!-- 成就系统 Tab -->
        <view v-if="activeTab === 'achievements'" class="achievement-section">
          <!-- 成就统计 -->
          <view class="achievement-stats">
            <view class="stat-item">
              <text class="stat-value">{{ unlockedCount }}</text>
              <text class="stat-label">已解锁</text>
            </view>
            <view class="stat-item">
              <text class="stat-value">{{ totalAchievements }}</text>
              <text class="stat-label">总成就</text>
            </view>
            <view class="stat-item">
              <text class="stat-value">{{ Math.round((unlockedCount / totalAchievements) * 100) }}%</text>
              <text class="stat-label">完成度</text>
            </view>
          </view>

          <!-- 成就分类 -->
          <view class="category-tabs">
            <view
              v-for="category in achievementCategories"
              :key="category.id"
              :class="['category-tab', { active: activeCategory === category.id }]"
              @tap="switchCategory(category.id)"
            >
              {{ category.name }}
            </view>
          </view>

          <view class="achievement-list">
            <view
              v-for="achievement in filteredAchievements"
              :key="achievement.id"
              class="achievement-card"
              :class="{ unlocked: achievement.unlocked }"
            >
              <view class="achievement-icon">
                <text class="icon-text">{{ achievement.unlocked ? achievement.icon : '🔒' }}</text>
                <view v-if="achievement.unlocked" class="unlock-badge">✓</view>
              </view>
              <view class="achievement-content">
                <text class="achievement-name">{{ achievement.name }}</text>
                <text class="achievement-desc">{{ achievement.description }}</text>
                <view class="achievement-meta">
                  <text class="achievement-reward">+{{ achievement.points }}分</text>
                  <text :class="['achievement-rarity', achievement.rarity]">{{ getRarityText(achievement.rarity) }}</text>
                </view>
                <view v-if="!achievement.unlocked && achievement.progress" class="progress-bar">
                  <view
                    class="progress-fill"
                    :style="{ width: achievement.progress.percentage + '%' }"
                  ></view>
                  <text class="progress-text">{{ achievement.progress.current }}/{{ achievement.progress.total }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 记录详情 Tab -->
        <view v-if="activeTab === 'history'" class="history-section">
          <view class="section-title">
            <text class="title-text">📝 游戏记录</text>
            <view class="filter-tabs">
              <view
                v-for="filter in gameFilters"
                :key="filter.id"
                :class="['filter-tab', { active: activeFilter === filter.id }]"
                @tap="switchFilter(filter.id)"
              >
                {{ filter.name }}
              </view>
            </view>
          </view>

        <view class="history-list">
          <view
            v-for="record in filteredGameHistory"
            :key="record.timestamp"
            class="history-card"
            @tap="showGameDetail(record)"
          >
            <view class="game-info">
              <text class="game-name">{{ getGameName(record.gameId) }}</text>
              <text class="game-time">{{ formatTime(record.timestamp) }}</text>
              <text class="game-duration">{{ formatDuration(record.duration) }}</text>
            </view>
            <view class="game-result">
              <text class="score">{{ record.score }}分</text>
              <text v-if="record.accuracy" class="accuracy">{{ record.accuracy }}%</text>
              <view v-if="record.isNewRecord" class="new-record">🏆</view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-if="filteredGameHistory.length === 0" class="empty-state">
          <text class="empty-icon">📝</text>
          <text class="empty-text">暂无游戏记录</text>
        </view>
      </view>

      <!-- 游戏详情弹窗 -->
      <view v-if="showDetail" class="detail-modal" @tap="hideDetail">
        <view class="detail-content" @tap.stop>
          <view class="detail-header">
            <text class="detail-title">游戏详情</text>
            <text class="close-btn" @tap="hideDetail">×</text>
          </view>
          <view v-if="selectedRecord" class="detail-body">
            <view class="detail-item">
              <text class="detail-label">游戏类型</text>
              <text class="detail-value">{{ getGameName(selectedRecord.gameId) }}</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">最终得分</text>
              <text class="detail-value">{{ selectedRecord.score }}分</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">游戏时长</text>
              <text class="detail-value">{{ formatDuration(selectedRecord.duration) }}</text>
            </view>
            <view v-if="selectedRecord.accuracy" class="detail-item">
              <text class="detail-label">准确率</text>
              <text class="detail-value">{{ selectedRecord.accuracy }}%</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">游戏时间</text>
              <text class="detail-value">{{ formatTime(selectedRecord.timestamp) }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </PageWrapper>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue';
  import PageWrapper from '@/components/PageWrapper.vue';
  import { gameService } from '@/games/shared/services/game-service';

  interface GameCategory {
    id: string;
    icon: string;
    name: string;
    winRate: number;
    highScore: number;
    playCount: number;
    avgTime: number;
  }

  interface Achievement {
    id: string;
    icon: string;
    name: string;
    description: string;
    unlocked: boolean;
    category: string;
    points: number;
    rarity: 'common' | 'rare' | 'epic' | 'legendary';
    progress?: {
      current: number;
      total: number;
      percentage: number;
    };
  }

  interface GameRecord {
    id: string;
    gameName: string;
    score: number;
    result: 'win' | 'lose';
    playTime: string;
  }

  // Tab 相关
  const activeTab = ref('overview');
  const tabs = [
    { id: 'overview', name: '数据总览', icon: '📊' },
    { id: 'achievements', name: '成就系统', icon: '🏆' },
    { id: 'history', name: '记录详情', icon: '📝' },
  ];

  // 基础数据
  const totalScore = ref(12580);
  const winRate = ref(78);
  const maxStreak = ref(15);
  const ranking = ref(42);

  // 游戏分类数据
  const gameCategories = ref<GameCategory[]>([
    {
      id: 'hexagram-name-to-image',
      icon: '📖',
      name: '卦名配图',
      winRate: 85,
      highScore: 980,
      playCount: 45,
      avgTime: 32,
    },
    {
      id: 'hexagram-image-to-name',
      icon: '🔮',
      name: '看图识卦',
      winRate: 72,
      highScore: 1250,
      playCount: 38,
      avgTime: 28,
    },
  ]);

  // 成就数据
  const achievements = ref<Achievement[]>([
    {
      id: 'first-game',
      icon: '🌱',
      name: '初出茅庐',
      description: '完成第一场游戏',
      unlocked: true,
      category: 'game',
      points: 100,
      rarity: 'common',
    },
    {
      id: 'win-streak-10',
      icon: '🔥',
      name: '百战百胜',
      description: '连胜10场游戏',
      unlocked: true,
      category: 'streak',
      points: 500,
      rarity: 'rare',
    },
    {
      id: 'master-level',
      icon: '🎓',
      name: '易学大师',
      description: '总积分达到50000',
      unlocked: false,
      category: 'score',
      points: 1000,
      rarity: 'epic',
      progress: {
        current: 12580,
        total: 50000,
        percentage: 25,
      },
    },
    {
      id: 'perfect-score',
      icon: '💎',
      name: '完美主义',
      description: '单局满分100次',
      unlocked: false,
      category: 'special',
      points: 2000,
      rarity: 'legendary',
      progress: {
        current: 23,
        total: 100,
        percentage: 23,
      },
    },
  ]);

  // 成就系统相关
  const activeCategory = ref('all');
  const achievementCategories = [
    { id: 'all', name: '全部' },
    { id: 'game', name: '游戏成就' },
    { id: 'score', name: '分数成就' },
    { id: 'streak', name: '连胜成就' },
    { id: 'special', name: '特殊成就' },
  ];

  // 计算属性 - 成就统计
  const unlockedCount = computed(() => {
    return achievements.value.filter(a => a.unlocked).length;
  });

  const totalAchievements = computed(() => {
    return achievements.value.length;
  });

  const filteredAchievements = computed(() => {
    if (activeCategory.value === 'all') {
      return achievements.value;
    }
    return achievements.value.filter(a => a.category === activeCategory.value);
  });

  // 游戏记录相关
  const gameHistory = ref<any[]>([]);
  const activeFilter = ref('all');
  const showDetail = ref(false);
  const selectedRecord = ref<any>(null);

  // 游戏筛选选项
  const gameFilters = [
    { id: 'all', name: '全部' },
    { id: 'hexagram-image-to-name', name: '看图识卦' },
    { id: 'hexagram-name-to-image', name: '卦名配图' },
  ];

  // 游戏名称映射
  const gameNames: Record<string, string> = {
    'hexagram-image-to-name': '看图识卦',
    'hexagram-name-to-image': '卦名配图',
  };

  // 计算属性 - 过滤后的游戏历史
  const filteredGameHistory = computed(() => {
    if (activeFilter.value === 'all') {
      return gameHistory.value;
    }
    return gameHistory.value.filter((record) => record.gameId === activeFilter.value);
  });

  // 工具方法
  const getGameName = (gameId: string): string => {
    return gameNames[gameId] || gameId;
  };

  const formatTime = (timestamp: number): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return '今天';
    } else if (diffDays === 1) {
      return '昨天';
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const formatDuration = (duration: number): string => {
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // 交互方法
  const switchTab = (tabId: string) => {
    activeTab.value = tabId;
  };

  const switchCategory = (categoryId: string) => {
    activeCategory.value = categoryId;
  };

  const switchFilter = (filterId: string) => {
    activeFilter.value = filterId;
  };

  const showGameDetail = (record: any) => {
    selectedRecord.value = record;
    showDetail.value = true;
  };

  const hideDetail = () => {
    showDetail.value = false;
    selectedRecord.value = null;
  };

  const getRarityText = (rarity: string): string => {
    const rarityMap: Record<string, string> = {
      'common': '普通',
      'rare': '稀有',
      'epic': '史诗',
      'legendary': '传说'
    };
    return rarityMap[rarity] || '普通';
  };

  // 加载数据
  const loadGameData = async () => {
    try {
      // 这里可以从 gameService 获取真实数据
      console.log('加载游戏数据...');

      // 模拟游戏历史数据
      gameHistory.value = [
        {
          gameId: 'hexagram-image-to-name',
          score: 850,
          accuracy: 85,
          duration: 120,
          timestamp: Date.now() - 2 * 60 * 60 * 1000, // 2小时前
          isNewRecord: true,
        },
        {
          gameId: 'hexagram-name-to-image',
          score: 720,
          accuracy: 72,
          duration: 95,
          timestamp: Date.now() - 3 * 60 * 60 * 1000, // 3小时前
          isNewRecord: false,
        },
        {
          gameId: 'hexagram-image-to-name',
          score: 450,
          accuracy: 65,
          duration: 180,
          timestamp: Date.now() - 24 * 60 * 60 * 1000, // 昨天
          isNewRecord: false,
        },
      ];
    } catch (error) {
      console.error('加载游戏数据失败:', error);
    }
  };

  onMounted(() => {
    loadGameData();
  });
</script>

<style lang="scss" scoped>
  .game-data-page {
    background-color: #f8f4e9;
    min-height: 100vh;
  }

  /* Tab 导航 */
  .tab-navigation {
    display: flex;
    background: white;
    border-radius: 24rpx;
    margin: 32rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .tab-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 24rpx 16rpx;
      transition: all 0.3s ease;
      position: relative;

      &.active {
        background: #8b4513;
        color: white;

        .tab-icon {
          transform: scale(1.1);
        }
      }

      .tab-icon {
        font-size: 32rpx;
        margin-bottom: 8rpx;
        transition: transform 0.3s ease;
      }

      .tab-text {
        font-size: 24rpx;
        font-weight: bold;
      }
    }
  }

  /* Tab 内容 */
  .tab-content {
    padding: 0 32rpx 32rpx;
  }

  .section-title {
    margin-bottom: 24rpx;

    .title-text {
      font-size: 36rpx;
      font-weight: bold;
      color: #8b4513;
    }
  }

  /* 数据总览 */
  .overview-section {
    margin-bottom: 40rpx;

    .stats-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 24rpx;

      .stat-card {
        background: white;
        border-radius: 24rpx;
        padding: 32rpx;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        gap: 24rpx;

        .stat-icon {
          font-size: 48rpx;
        }

        .stat-content {
          .stat-value {
            font-size: 36rpx;
            font-weight: bold;
            color: #8b4513;
            display: block;
            margin-bottom: 4rpx;
          }

          .stat-label {
            font-size: 24rpx;
            color: #666;
          }
        }
      }
    }
  }

  /* 游戏分类统计 */
  .category-section {
    margin-bottom: 40rpx;

    .category-list {
      display: flex;
      flex-direction: column;
      gap: 24rpx;

      .category-card {
        background: white;
        border-radius: 24rpx;
        padding: 32rpx;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);

        .category-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 24rpx;

          .category-info {
            display: flex;
            align-items: center;
            gap: 16rpx;

            .category-icon {
              font-size: 32rpx;
            }

            .category-name {
              font-size: 32rpx;
              font-weight: bold;
              color: #333;
            }
          }

          .category-stats {
            .win-rate {
              font-size: 28rpx;
              color: #8b4513;
              font-weight: bold;
            }
          }
        }

        .category-details {
          display: flex;
          justify-content: space-between;

          .detail-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8rpx;

            .detail-label {
              font-size: 24rpx;
              color: #666;
            }

            .detail-value {
              font-size: 28rpx;
              font-weight: bold;
              color: #8b4513;
            }
          }
        }
      }
    }
  }

  /* 成就系统 */
  .achievement-section {
    padding:30rpx;

    .achievement-stats {
      display: flex;
      justify-content: space-around;
      background: white;
      border-radius: 24rpx;
      padding: 32rpx;
      margin-bottom: 24rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

      .stat-item {
        text-align: center;

        .stat-value {
          font-size: 48rpx;
          font-weight: bold;
          color: #8b4513;
          display: block;
          margin-bottom: 8rpx;
        }

        .stat-label {
          font-size: 24rpx;
          color: #666;
        }
      }
    }

    .category-tabs {
      display: flex;
      gap: 16rpx;
      margin-bottom: 24rpx;
      overflow-x: auto;

      .category-tab {
        padding: 16rpx 32rpx;
        background: white;
        border-radius: 20rpx;
        font-size: 24rpx;
        color: #666;
        white-space: nowrap;
        transition: all 0.3s ease;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

        &.active {
          background: #8b4513;
          color: white;
        }
      }
    }

    .achievement-list {
      display: flex;
      flex-direction: column;
      gap: 24rpx;

      .achievement-card {
        background: white;
        border-radius: 24rpx;
        padding: 32rpx;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        gap: 24rpx;
        opacity: 0.6;

        &.unlocked {
          opacity: 1;
        }

        .achievement-icon {
          width: 80rpx;
          height: 80rpx;
          background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
          border-radius: 20rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          .icon-text {
            font-size: 40rpx;
          }
        }

        .achievement-content {
          flex: 1;

          .achievement-name {
            font-size: 32rpx;
            font-weight: bold;
            color: #333;
            display: block;
            margin-bottom: 8rpx;
          }

          .achievement-desc {
            font-size: 24rpx;
            color: #666;
            display: block;
            margin-bottom: 16rpx;
          }

          .progress-bar {
            position: relative;
            height: 12rpx;
            background: #f0f0f0;
            border-radius: 6rpx;
            overflow: hidden;

            .progress-fill {
              height: 100%;
              background: linear-gradient(90deg, #8b4513 0%, #a0522d 100%);
              border-radius: 6rpx;
              transition: width 0.3s ease;
            }

            .progress-text {
              position: absolute;
              top: -32rpx;
              right: 0;
              font-size: 20rpx;
              color: #8b4513;
              font-weight: bold;
            }
          }
        }
      }
    }
  }

  /* 历史记录 */
  .history-section {
    padding:30rpx;
    .history-list {
      display: flex;
      flex-direction: column;
      gap: 16rpx;

      .history-card {
        background: white;
        border-radius: 16rpx;
        padding: 24rpx;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
        display: flex;
        justify-content: space-between;
        align-items: center;

        .game-info {
          .game-name {
            font-size: 28rpx;
            font-weight: bold;
            color: #333;
            display: block;
            margin-bottom: 4rpx;
          }

          .game-time {
            font-size: 22rpx;
            color: #999;
          }
        }

        .game-result {
          display: flex;
          align-items: center;
          gap: 16rpx;

          .score {
            font-size: 24rpx;
            font-weight: bold;
            color: #8b4513;
          }

          .result-badge {
            padding: 8rpx 16rpx;
            border-radius: 20rpx;
            font-size: 20rpx;

            &.win {
              background: #e8f5e8;
              color: #4caf50;
            }

            &.lose {
              background: #ffebee;
              color: #f44336;
            }

            .result-text {
              font-weight: bold;
            }
          }
        }
      }
    }

    /* 筛选标签 */
    .section-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24rpx;

      .filter-tabs {
        display: flex;
        gap: 16rpx;

        .filter-tab {
          padding: 12rpx 24rpx;
          background: #f0f0f0;
          border-radius: 20rpx;
          font-size: 24rpx;
          color: #666;
          transition: all 0.3s ease;

          &.active {
            background: #8b4513;
            color: white;
          }
        }
      }
    }

    /* 空状态 */
    .empty-state {
      text-align: center;
      padding: 80rpx 40rpx;
      color: #999;

      .empty-icon {
        font-size: 80rpx;
        display: block;
        margin-bottom: 16rpx;
      }

      .empty-text {
        font-size: 28rpx;
      }
    }
  }

  /* 详情弹窗 */
  .detail-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .detail-content {
      background: white;
      border-radius: 24rpx;
      width: 80%;
      max-width: 600rpx;
      max-height: 80vh;
      overflow: hidden;

      .detail-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 32rpx;
        border-bottom: 1rpx solid #f0f0f0;

        .detail-title {
          font-size: 36rpx;
          font-weight: bold;
          color: #333;
        }

        .close-btn {
          font-size: 48rpx;
          color: #999;
          width: 48rpx;
          height: 48rpx;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .detail-body {
        padding: 32rpx;

        .detail-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16rpx 0;
          border-bottom: 1rpx solid #f8f8f8;

          &:last-child {
            border-bottom: none;
          }

          .detail-label {
            font-size: 28rpx;
            color: #666;
          }

          .detail-value {
            font-size: 28rpx;
            color: #333;
            font-weight: bold;
          }
        }
      }
    }
  }
</style>
