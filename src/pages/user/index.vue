<template>
  <PageWrapper :show-nav-bar="false">
    <view class="container">
      <!-- 用户档案卡片 -->
      <view class="user-profile-card">
        <view class="profile-header">
          <view class="avatar-section">
            <view class="avatar-container">
              <text class="avatar">{{ userInfo.avatar || userLevel.icon }}</text>
              <view class="level-badge">
                <text class="level-text">Lv.{{ userLevel.level }}</text>
              </view>
            </view>
          </view>

          <view class="user-info">
            <view class="username-row">
              <text class="username">{{ username }}</text>
              <view class="edit-btn" @tap="handleEditProfile">
                <text class="edit-icon">✏️</text>
              </view>
            </view>
            <text class="user-title">{{ userLevel.title }}</text>
            <text class="user-signature">{{ userSignature }}</text>
          </view>
        </view>

        <view class="profile-stats">
          <view class="stat-item">
            <text class="stat-value">{{ totalScore }}</text>
            <text class="stat-label">总积分</text>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <text class="stat-value">{{ totalAchievements }}</text>
            <text class="stat-label">成就数</text>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <text class="stat-value">{{ joinDays }}</text>
            <text class="stat-label">学习天数</text>
          </view>
        </view>

        <view class="level-progress">
          <view class="progress-info">
            <text class="progress-label">经验值</text>
            <text class="progress-text">{{ currentExp }}/{{ nextLevelExp }}</text>
          </view>
          <view class="progress-bar">
            <view
              class="progress-fill"
              :style="{ width: levelProgress + '%' }"
            ></view>
          </view>
          <text class="next-level-text">距离下一级还需 {{ scoreToNext }} 积分</text>
        </view>
      </view>

      <!-- 功能菜单区域 -->
      <view class="function-menu-section">
        <view class="menu-grid">
          <view
            v-for="menu in menuItems"
            :key="menu.id"
            class="menu-card"
            @tap="handleMenuClick(menu)"
          >
            <view class="menu-icon">
              <text class="icon-text">{{ menu.icon }}</text>
            </view>
            <text class="menu-title">{{ menu.title }}</text>
          </view>
        </view>
      </view>

      <view class="version-info">
        <text>版本 1.0.0</text>
      </view>

      <!-- 使用自定义tabBar -->
      <YiTabBar />
    </view>
  </PageWrapper>
</template>

<script lang="ts">
  import { defineComponent, ref, onMounted } from 'vue';
  import YiIcon from '@/components/YiIcon.vue';
  import YiTabBar from '@/components/YiTabBar.vue';
  import PageWrapper from '@/components/PageWrapper.vue';
  import { gameService } from '@/games/shared/services/game-service';
  import { getLevelInfo } from '@/games/shared/services/level/level-titles';
  import type { UserLevel } from '@/games/shared/services/level/level-calculator';

  interface UserInfo {
    avatar?: string;
    nickname?: string;
    signature?: string;
  }

  interface MenuItem {
    id: string;
    icon: string;
    title: string;
    description?: string;
    stats?: string;
    action: string;
  }



  export default defineComponent({
    name: 'UserPage',
    components: { YiIcon, YiTabBar, PageWrapper },
    setup() {
      const userInfo = ref<UserInfo>({});
      const username = ref('易学者');
      const totalScore = ref(0);
      const totalAchievements = ref(0);
      const userRank = ref(0);
      // 使用统一的等级系统获取默认等级信息
      const defaultLevelInfo = getLevelInfo(1);
      const userLevel = ref<UserLevel>({
        level: 1,
        title: defaultLevelInfo.title,
        description: `${defaultLevelInfo.title} - 等级 1`,
        minExperience: 0,
        maxExperience: 999,
        color: '#8b4513',
        icon: defaultLevelInfo.icon,
        rewards: [],
      });
      const levelProgress = ref(0);
      const scoreToNext = ref(0);

      // 用户档案相关数据
      const userSignature = ref('"学而时习之，不亦说乎"');
      const joinDays = ref(45);
      const currentExp = ref(850);
      const nextLevelExp = ref(1000);



      // 功能菜单数据
      const menuItems = ref<MenuItem[]>([
        {
          id: 'game-data',
          icon: '📊',
          title: '游戏数据',
          action: 'goToGameData',
        },
        {
          id: 'settings',
          icon: '⚙️',
          title: '系统设置',
          action: 'goToSettings',
        },
        {
          id: 'debug',
          icon: '🔧',
          title: '调试信息',
          action: 'goToDebug',
        },
        {
          id: 'about',
          icon: 'ℹ️',
          title: '关于我们',
          action: 'goToAbout',
        },
      ]);



      // 编辑个人档案
      const handleEditProfile = () => {
        uni.navigateTo({
          url: '/pages/user/profile'
        });
      };

      // 菜单点击处理
      const handleMenuClick = (menu: MenuItem) => {
        switch (menu.action) {
          case 'goToGameData':
            goToGameData();
            break;
          case 'goToSettings':
            goToSettings();
            break;
          case 'goToDebug':
            goToDebug();
            break;
          case 'goToAbout':
            goToAbout();
            break;
          default:
            uni.showToast({
              title: '功能开发中',
              icon: 'none',
            });
        }
      };

      // 导航到游戏数据页面
      const goToGameData = () => {
        uni.navigateTo({ url: '/pages/user/game-data' });
      };

      // 导航到系统设置页面（暂时指向开发选项）
      const goToSettings = () => {
        uni.navigateTo({ url: '/pages/developer/index' });
      };

      // 导航到调试信息页面
      const goToDebug = () => {
        uni.navigateTo({ url: '/pages/developer/index' });
      };

      // 导航到关于我们页面（暂时显示提示）
      const goToAbout = () => {
        uni.showToast({
          title: '关于我们页面开发中',
          icon: 'none',
        });
      };

      // 保留原有的一些导航方法
      const goToDeveloperOptions = () => {
        uni.navigateTo({ url: '/pages/developer/index' });
      };

      // 加载用户数据
      const loadUserData = async () => {
        try {
          const userProfile = await gameService.initializeUser();
          const userStats = gameService.getUserStats();

          if (userProfile && userStats) {
            username.value = userProfile.username;
            totalScore.value = (userStats.totalScore as number) || 0;
            totalAchievements.value = userProfile.achievements.length;

            // 更新用户信息
            userInfo.value = {
              avatar: userProfile.avatar || '👤',
              nickname: userProfile.username,
              signature: (userProfile as any).signature || '"学而时习之，不亦说乎"'
            };

            // 更新用户等级信息 - 使用统一的等级系统
            const level = userStats.level as number;
            const levelInfo = getLevelInfo(level);
            userLevel.value = {
              level,
              title: levelInfo.title,
              description: `${levelInfo.title} - 等级 ${level}`,
              minExperience: (level - 1) * 1000,
              maxExperience: level * 1000 - 1,
              color: '#8b4513',
              icon: levelInfo.icon,
              rewards: [],
            };

            const experience = userStats.experience as number;
            const currentLevelExp = experience % 1000;
            levelProgress.value = currentLevelExp / 10;
            scoreToNext.value = 1000 - currentLevelExp;

            // 更新经验值显示
            currentExp.value = currentLevelExp;
            nextLevelExp.value = 1000;

            // 获取用户排名
            userRank.value = await gameService.getUserRank(userProfile.userId, 'global');
          }
        } catch (error) {
          console.error('加载用户数据失败:', error);
        }
      };

      // 页面加载时获取用户数据
      onMounted(async () => {
        await loadUserData();
      });

      return {
        userInfo,
        username,
        totalScore,
        totalAchievements,
        userRank,
        userLevel,
        levelProgress,
        scoreToNext,
        userSignature,
        joinDays,
        currentExp,
        nextLevelExp,
        menuItems,
        handleEditProfile,
        handleMenuClick,
        goToGameData,
        goToSettings,
        goToDebug,
        goToAbout,
        goToDeveloperOptions,
      };
    },
  });
</script>

<style lang="scss">
  .container {
    min-height: 100vh;
    background-color: #f8f4e9;
    padding: 16rpx 32rpx 32rpx;
  }

  /* 用户档案卡片 */
  .user-profile-card {
    border-radius: 32rpx;
    margin-bottom: 40rpx;

    .profile-header {
      display: flex;
      align-items: flex-start;
      gap: 32rpx;
      margin-bottom: 32rpx;

      .avatar-section {
        position: relative;

        .avatar-container {
          position: relative;

          .avatar {
            width: 120rpx;
            height: 120rpx;
            background: #f0f0f0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 56rpx;
          }

          .level-badge {
            position: absolute;
            bottom: -8rpx;
            right: -8rpx;
            background: #8b4513;
            border-radius: 20rpx;
            padding: 4rpx 12rpx;
            box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

            .level-text {
              font-size: 20rpx;
              font-weight: bold;
              color: white;
            }
          }
        }
      }

      .user-info {
        flex: 1;

        .username-row {
          display: flex;
          align-items: center;
          gap: 16rpx;
          margin-bottom: 8rpx;

          .username {
            font-size: 40rpx;
            font-weight: bold;
            color: #333;
          }

          .edit-btn {
            width: 40rpx;
            height: 40rpx;
            background: #f0f0f0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;

            .edit-icon {
              font-size: 20rpx;
            }
          }
        }

        .user-title {
          font-size: 28rpx;
          color: #8b4513;
          font-weight: bold;
          margin-bottom: 8rpx;
          display: block;
        }

        .user-signature {
          font-size: 24rpx;
          color: #666;
          font-style: italic;
          line-height: 1.4;
          display: block;
        }
      }
    }

    .profile-stats {
      display: flex;
      align-items: center;
      justify-content: space-around;
      background: white;
      border-radius: 24rpx;
      padding: 24rpx;
      margin-bottom: 32rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8rpx;

        .stat-value {
          font-size: 36rpx;
          font-weight: bold;
          color: #8b4513;
        }

        .stat-label {
          font-size: 24rpx;
          color: #666;
        }
      }

      .stat-divider {
        width: 2rpx;
        height: 60rpx;
        background: #e0e0e0;
      }
    }

    .level-progress {
      background: white;
      border-radius: 24rpx;
      padding: 24rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

      .progress-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16rpx;

        .progress-label {
          font-size: 28rpx;
          color: #333;
          font-weight: bold;
        }

        .progress-text {
          font-size: 24rpx;
          color: #666;
        }
      }

      .progress-bar {
        height: 16rpx;
        background: #f0f0f0;
        border-radius: 8rpx;
        overflow: hidden;
        margin-bottom: 12rpx;

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #8b4513 0%, #a0522d 100%);
          border-radius: 8rpx;
          transition: width 0.3s ease;
        }
      }

      .next-level-text {
        text-align: center;
        font-size: 22rpx;
        color: #666;
        display: block;
      }
    }
  }





  /* 功能菜单区域 */
  .function-menu-section {
    margin-bottom: 40rpx;

    .menu-grid {
      background: white;
      border-radius: 24rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
      overflow: hidden;

      .menu-card {
        background: transparent;
        border-radius: 0;
        padding: 32rpx;
        display: flex;
        align-items: center;
        gap: 24rpx;
        transition: background-color 0.2s ease;
        border-bottom: 1rpx solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        &:active {
          background-color: rgba(0, 0, 0, 0.05);
        }

        .menu-icon {
          width: 48rpx;
          height: 48rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;

          .icon-text {
            font-size: 40rpx;
          }
        }

        .menu-title {
          font-size: 32rpx;
          color: #333;
          line-height: 1.2;
          flex: 1;
        }
      }
    }
  }

  .version-info {
    text-align: center;
    padding: 40rpx;
    color: #999;
    font-size: 24rpx;
  }
</style>
