/**
 * 用户设置状态管理（与本地存储同步）
 */

import { defineStore } from 'pinia';
import { ref, watch } from 'vue';
import { storage } from '@/utils/storage';
import { STORAGE_KEYS } from '@/utils/constants';

export const useSettingsStore = defineStore('settings', () => {
  // 用户界面设置
  const fontSize = ref(storage.get(STORAGE_KEYS.FONT_SIZE, 16));

  // 监听变化并自动保存到本地存储
  watch(fontSize, (newValue) => {
    storage.set(STORAGE_KEYS.FONT_SIZE, newValue);
  });

  // 方法
  const setFontSize = (size: number) => {
    fontSize.value = Math.max(12, Math.min(24, size)); // 限制范围 12-24
  };

  const resetSettings = () => {
    fontSize.value = 16;
  };

  // 获取当前设置的摘要
  const getSettingsSummary = () => {
    return {
      fontSize: fontSize.value,
    };
  };

  return {
    // 状态
    fontSize,

    // 方法
    setFontSize,
    resetSettings,
    getSettingsSummary,
  };
});
