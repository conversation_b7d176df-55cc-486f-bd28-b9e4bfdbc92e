/**
 * 应用运行时状态管理
 */

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { Hexagram } from '@/utils/hexagram';

export const useAppStore = defineStore('app', () => {
  // 当前页面状态
  const currentPage = ref('');
  const isLoading = ref(false);
  const loadingText = ref('加载中...');

  // 当前卦象
  const currentHexagram = ref<Hexagram | null>(null);
  const currentHexagramId = ref<number | null>(null);

  // 计算属性
  const hasCurrentHexagram = computed(() => currentHexagram.value !== null);

  // 方法
  const setLoading = (loading: boolean, text?: string) => {
    isLoading.value = loading;
    if (text) {
      loadingText.value = text;
    }
  };

  const setCurrentHexagram = (hexagram: Hexagram | null) => {
    currentHexagram.value = hexagram;
    currentHexagramId.value = hexagram?.id || null;
  };

  const resetApp = () => {
    currentPage.value = '';
    isLoading.value = false;
    currentHexagram.value = null;
    currentHexagramId.value = null;
  };

  return {
    // 状态
    currentPage,
    isLoading,
    loadingText,
    currentHexagram,
    currentHexagramId,

    // 计算属性
    hasCurrentHexagram,

    // 方法
    setLoading,
    setCurrentHexagram,
    resetApp,
  };
});
