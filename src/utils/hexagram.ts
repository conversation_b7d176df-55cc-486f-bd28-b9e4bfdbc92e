export type Hexagram = {
  id: number;
  name: string;
  label: string;
  binary: string;
  phase: string;
  description: string;
  idBagong: number; // 八宫卦序编号
  idXiantian: number; // 先天卦序编号
  code: string; // 卦象符号
};

/**
 * 卦象数据
 * binary字段表示六爻，从下到上排列
 * 1表示阳爻，0表示阴爻
 * idBagong字段表示八宫卦序编号
 * code字段表示卦象符号（如䷀、䷁等）
 */
/* eslint-disable */
export const hexagrams: Hexagram[] = [
  {
    id: 1,
    name: '乾卦',
    label: '乾为天',
    binary: '111111',
    phase: '刚健不息，自强不息',
    description: '乾卦剛健，有天地，然後萬物生焉',
    idBagong: 1,
    idXiantian: 64,
    code: '䷀',
  },
  {
    id: 2,
    name: '坤卦',
    label: '坤为地',
    binary: '000000',
    phase: '柔顺包容，厚德载物',
    description: '坤卦柔順，有天地，然後萬物生焉',
    idBagong: 33,
    idXiantian: 1,
    code: '䷁',
  },
  {
    id: 3,
    name: '屯卦',
    label: '水雷屯',
    binary: '010001',
    phase: '艰难创始，蓄势待发',
    description: '屯卦出现而不会失去居所，盈天地之间者唯万物，故受之以屯；屯者，盈也，屯者，物之始生也',
    idBagong: 19,
    idXiantian: 35,
    code: '䷂',
  },
  {
    id: 4,
    name: '蒙卦',
    label: '山水蒙',
    binary: '100010',
    phase: '启蒙教育，循序渐进',
    description: '蒙卦错杂而显着，物生必蒙，故受之以蒙；蒙者，蒙也，物之稺也',
    idBagong: 53,
    idXiantian: 18,
    code: '䷃',
  },
  {
    id: 5,
    name: '需卦',
    label: '水天需',
    binary: '010111',
    phase: '耐心等待，守正待时',
    description: '需卦是不前进，物稺不可不养也，故受之以需；需者，饮食之道也',
    idBagong: 39,
    idXiantian: 59,
    code: '䷄',
  },
  {
    id: 6,
    name: '讼卦',
    label: '天水讼',
    binary: '111010',
    phase: '避免争执，慎防纠纷',
    description: '讼卦是不亲和，饮食必有讼，故受之以讼',
    idBagong: 55,
    idXiantian: 24,
    code: '䷅',
  },
  {
    id: 7,
    name: '师卦',
    label: '地水师',
    binary: '000010',
    phase: '统率众人，纪律严明',
    description: '师卦忧苦，讼必有众起，故受之以师；师者，众也',
    idBagong: 24,
    idXiantian: 17,
    code: '䷆',
  },
  {
    id: 8,
    name: '比卦',
    label: '水地比',
    binary: '010000',
    phase: '亲近和睦，诚信团结',
    description: '比卦和乐，众必有所比，故 受之以比；比者，比也',
    idBagong: 40,
    idXiantian: 3,
    code: '䷇',
  },
  {
    id: 9,
    name: '小畜卦',
    label: '风天小畜',
    binary: '110111',
    phase: '积蓄力量，小有成就',
    description: '小畜卦是积蓄少，比必有所畜也，故受之以小畜',
    idBagong: 42,
    idXiantian: 60,
    code: '䷈',
  },
  {
    id: 10,
    name: '履卦',
    label: '天泽履',
    binary: '111011',
    phase: '谨慎行事，循礼而行',
    description: '履卦是不停留，物畜然后有礼，故受之以履',
    idBagong: 30,
    idXiantian: 56,
    code: '䷉',
  },
  {
    id: 11,
    name: '泰卦',
    label: '地天泰',
    binary: '000111',
    phase: '阴阳交融，通达安泰',
    description: '否卦泰卦是状况相反，履而泰，然后安，故受之以泰；泰者，通也',
    idBagong: 36,
    idXiantian: 57,
    code: '䷊',
  },
  {
    id: 12,
    name: '否卦',
    label: '天地否',
    binary: '111000',
    phase: '闭塞不通，韬光养晦',
    description: '否卦泰卦是状况相反，物不可以终通，故受之以否',
    idBagong: 4,
    idXiantian: 8,
    code: '䷋',
  },
  {
    id: 13,
    name: '同人卦',
    label: '天火同人',
    binary: '111101',
    phase: '同心同力，与人交往',
    description: '物不可以終否，故受之以同人，同人卦彼此親近',
    idBagong: 56,
    idXiantian: 48,
    code: '䷌',
  },
  {
    id: 14,
    name: '大有卦',
    label: '火天大有',
    binary: '101111',
    phase: '大有所获，持盈保泰',
    description: '大有卦拥有众多，与人同者，物必归焉，故受之以大有',
    idBagong: 8,
    idXiantian: 62,
    code: '䷍',
  },
  {
    id: 15,
    name: '谦卦',
    label: '地山谦',
    binary: '000100',
    phase: '谦虚谨慎，卑以自牧',
    description: '谦卦轻己，有大者不可以盈，故受之以谦',
    idBagong: 62,
    idXiantian: 9,
    code: '䷎',
  },
  {
    id: 16,
    name: '豫卦',
    label: '雷地豫',
    binary: '001000',
    phase: '愉悦安乐，居安思危',
    description: '豫卦懈怠，有大而能谦，必豫，故受之以豫',
    idBagong: 10,
    idXiantian: 5,
    code: '䷏',
  },
  {
    id: 17,
    name: '随卦',
    label: '泽雷随',
    binary: '011001',
    phase: '随从时机，顺势而为',
    description: ' 随卦没有事故，豫必有随，故受之以随',
    idBagong: 16,
    idXiantian: 39,
    code: '䷐',
  },
  {
    id: 18,
    name: '蛊卦',
    label: '山风蛊',
    binary: '100110',
    phase: '整治弊乱，拨乱反正',
    description: '蛊卦整饬积弊，以喜随人者必有事，故受之以蛊；蛊者，事也',
    idBagong: 48,
    idXiantian: 26,
    code: '䷑',
  },
  {
    id: 19,
    name: '临卦',
    label: '地泽临',
    binary: '000011',
    phase: '亲临指导，教化民众',
    description: '临卦给与，有事而后可大，故受之以临；临者，大也',
    idBagong: 35,
    idXiantian: 49,
    code: '䷒',
  },
  {
    id: 20,
    name: '观卦',
    label: '风地观',
    binary: '110000',
    phase: '观察学习，省察自身',
    description: '观卦求取，物大然后可观，故受之以观',
    idBagong: 5,
    idXiantian: 4,
    code: '䷓',
  },
  {
    id: 21,
    name: '噬嗑卦',
    label: '火雷噬嗑',
    binary: '101001',
    phase: '铲除障碍，公正断案',
    description: '噬嗑卦讲究 饮食，可观而后有所合，故受之以噬嗑；嗑者，合也',
    idBagong: 46,
    idXiantian: 38,
    code: '䷔',
  },
  {
    id: 22,
    name: '贲卦',
    label: '山火贲',
    binary: '100101',
    phase: '文饰美化，返璞归真',
    description: '贲卦没有颜色，物不可以苟合而已，故受之以贲；贲者，饰也',
    idBagong: 26,
    idXiantian: 42,
    code: '䷕',
  },
  {
    id: 23,
    name: '剥卦',
    label: '山地剥',
    binary: '100000',
    phase: '阴盛阳衰，谨慎守成',
    description: '剥卦是朽烂，致饰然后亨，则尽矣，故受之以剥；剥者，剥也',
    idBagong: 6,
    idXiantian: 2,
    code: '䷖',
  },
  {
    id: 24,
    name: '复卦',
    label: '地雷复',
    binary: '000001',
    phase: '循环往复，万物复兴',
    description: ' 复卦是返回，物不可以终尽，剥穷上反下，故受之以复',
    idBagong: 34,
    idXiantian: 33,
    code: '䷗',
  },
  {
    id: 25,
    name: '无妄卦',
    label: '天雷无妄',
    binary: '111001',
    phase: '顺其自然，不妄作为',
    description: '无妄卦是灾难，复则不妄矣，故受之以无妄',
    idBagong: 45,
    idXiantian: 40,
    code: '䷘',
  },
  {
    id: 26,
    name: '大畜卦',
    label: '山天大畜',
    binary: '100111',
    phase: '大量积累，厚积薄发',
    description: '大畜卦把握时机，有无妄，然后可畜，故受之以大畜',
    idBagong: 27,
    idXiantian: 58,
    code: '䷙',
  },
  {
    id: 27,
    name: '颐卦',
    label: '山雷颐',
    binary: '100001',
    phase: '修身养性，自求口实',
    description: '颐卦是养之以正，物畜然后可养，故受之以颐； 颐者，养也',
    idBagong: 47,
    idXiantian: 34,
    code: '䷚',
  },
  {
    id: 28,
    name: '大过卦',
    label: '泽风大过',
    binary: '011110',
    phase: '非常行动，慎防过激',
    description: '大过卦是颠覆，不养则不可动，故受之以大过',
    idBagong: 15,
    idXiantian: 31,
    code: '䷛',
  },
  {
    id: 29,
    name: '坎卦',
    label: '坎为水',
    binary: '010010',
    phase: '险陷重重，诚信脱困',
    description: '坎卦往下流，物不可以终过，故受之以坎；坎者，陷也',
    idBagong: 17,
    idXiantian: 19,
    code: '䷜',
  },
  {
    id: 30,
    name: '离卦',
    label: '离为火',
    binary: '101101',
    phase: '光明美丽，依附中和',
    description: '离卦往上烧，陷必有所丽，故受之以离离者，丽也',
    idBagong: 49,
    idXiantian: 46,
    code: '䷝',
  },
  {
    id: 31,
    name: '咸卦',
    label: '泽山咸',
    binary: '011100',
    phase: '感应沟通，以诚相待',
    description: '咸卦是迅速，乾、坤象天地，咸、恆明夫妇；乾坤乃造化之本，夫妇实人伦之原',
    idBagong: 60,
    idXiantian: 15,
    code: '䷞',
  },
  {
    id: 32,
    name: '恒卦',
    label: '雷风恒',
    binary: '001110',
    phase: '持之以恒，守常不变',
    description: '恆卦是长久，夫妇之道，不可以不久也，故受之以恆；恆者，久也',
    idBagong: 12,
    idXiantian: 29,
    code: '䷟',
  },
  {
    id: 33,
    name: '遁卦',
    label: '天山遁',
    binary: '111100',
    phase: '退避隐忍，以待时机',
    description: '遯卦就会退避，物不可以久居其所，故受之以遯；遯者，退也',
    idBagong: 3,
    idXiantian: 16,
    code: '䷠',
  },
  {
    id: 34,
    name: '大壮卦',
    label: '雷天大壮',
    binary: '001111',
    phase: '强盛壮大，非礼勿履',
    description: '大壮卦就会停止，物不可以终遯，故受之以大壮',
    idBagong: 37,
    idXiantian: 61,
    code: '䷡',
  },
  {
    id: 35,
    name: '晋卦',
    label: '火地晋',
    binary: '101000',
    phase: '明德上进，昭示四方',
    description: '晋卦是白昼，物不可以终壮，故受之以晋；晋者，进也',
    idBagong: 7,
    idXiantian: 6,
    code: '䷢',
  },
  {
    id: 36,
    name: '明夷卦',
    label: '地火明夷',
    binary: '000101',
    phase: '韬光养晦，内明外柔',
    description: '明夷卦是诛灭，进必有所伤，故受之以明夷；夷者， 伤也',
    idBagong: 23,
    idXiantian: 41,
    code: '䷣',
  },
  {
    id: 37,
    name: '家人卦',
    label: '风火家人',
    binary: '110101',
    phase: '家庭伦理，各守其位',
    description: '家人卦是和睦于内，伤于外者必反其家，故受之以家人',
    idBagong: 43,
    idXiantian: 44,
    code: '䷤',
  },
  {
    id: 38,
    name: '睽卦',
    label: '火泽睽',
    binary: '101011',
    phase: '求同存异，化分为合',
    description: '睽卦是乖离于外，家道穷必乖，故受之以睽；睽者，乖也',
    idBagong: 29,
    idXiantian: 54,
    code: '䷥',
  },
  {
    id: 39,
    name: '蹇卦',
    label: '水山蹇',
    binary: '010100',
    phase: '险阻在前，反身修德',
    description: '蹇卦是险难，乖必有难，故受之以蹇；蹇者，难也',
    idBagong: 61,
    idXiantian: 11,
    code: '䷦',
  },
  {
    id: 40,
    name: '解卦',
    label: '雷水解',
    binary: '001010',
    phase: '解除困境，柔道致治',
    description: '解卦是缓解，物不可以终难，故受之以解；解者，缓也',
    idBagong: 11,
    idXiantian: 21,
    code: '䷧',
  },
  {
    id: 41,
    name: '损卦',
    label: '山泽损',
    binary: '100011',
    phase: '减损私欲，以诚益人',
    description: '损卦兴盛的开始，缓必有所失，故受之以损',
    idBagong: 28,
    idXiantian: 50,
    code: '䷨',
  },
  {
    id: 42,
    name: '益卦',
    label: '风雷益',
    binary: '110001',
    phase: '损上益下，利有攸往',
    description: '益卦衰退的开始，损而不已必益，故受之以益',
    idBagong: 44,
    idXiantian: 36,
    code: '䷩',
  },
  {
    id: 43,
    name: '夬卦',
    label: '泽天夬',
    binary: '011111',
    phase: '果决刚断，清除小人',
    description: '夬卦是决断，刚爻决去柔爻，君子的作风成长，小人的作风受困，益而不已必决，故受之以夬；夬者， 决也',
    idBagong: 38,
    idXiantian: 63,
    code: '䷪',
  },
  {
    id: 44,
    name: '姤卦',
    label: '天风姤',
    binary: '111110',
    phase: '相遇机缘，防微杜渐',
    description: '姤卦是相遇，柔爻遇到刚爻，决必有所遇，故受之以姤；姤者，遇也',
    idBagong: 2,
    idXiantian: 32,
    code: '䷫',
  },
  {
    id: 45,
    name: '萃卦',
    label: '泽地萃',
    binary: '011000',
    phase: '荟萃聚集，谨慎防乱',
    description: '萃卦聚合，物相遇而后聚，故受之以萃；萃者，聚也',
    idBagong: 59,
    idXiantian: 7,
    code: '䷬',
  },
  {
    id: 46,
    name: '升卦',
    label: '地风升',
    binary: '000110',
    phase: '顺势上升，积小成大',
    description: '升卦不下来，聚而上者谓之升，故受之以升',
    idBagong: 13,
    idXiantian: 25,
    code: '䷭',
  },
  {
    id: 47,
    name: '困卦',
    label: '泽水困',
    binary: '011010',
    phase: '困境求通，守正待机',
    description: '困卦相遇受阻，升而不已必困，故受之以困',
    idBagong: 58,
    idXiantian: 23,
    code: '䷮',
  },
  {
    id: 48,
    name: '井卦',
    label: '水风井',
    binary: '010110',
    phase: '修德养民，井然有序',
    description: '井卦畅通，困乎上者必反下，故受之以井',
    idBagong: 14,
    idXiantian: 27,
    code: '䷯',
  },
  {
    id: 49,
    name: '革卦',
    label: '泽火革',
    binary: '011101',
    phase: '变革创新，顺天应人',
    description: '革卦是除去旧的，井道不可不革，故受之以革',
    idBagong: 21,
    idXiantian: 47,
    code: '䷰',
  },
  {
    id: 50,
    name: '鼎卦',
    label: '火风鼎',
    binary: '101110',
    phase: '鼎新立国，稳重图变',
    description: '鼎卦是採取新的，革物者莫若鼎，故受之以鼎',
    idBagong: 51,
    idXiantian: 30,
    code: '䷱',
  },
  {
    id: 51,
    name: '震卦',
    label: '震为雷',
    binary: '001001',
    phase: '恐惧修省，临危不乱',
    description: '震卦是发动，主器者莫若长子，故受之以震；震者，动也',
    idBagong: 9,
    idXiantian: 37,
    code: '䷲',
  },
  {
    id: 52,
    name: '艮卦',
    label: '艮为山',
    binary: '100100',
    phase: '适可而止，动静得宜',
    description: '艮卦是阻止，物不可以终动，止之，故受之以艮；艮者，止也',
    idBagong: 25,
    idXiantian: 10,
    code: '䷳',
  },
  {
    id: 53,
    name: '渐卦',
    label: '风山渐',
    binary: '110100',
    phase: '循序渐进，不疾不徐',
    description: '渐卦是女子出嫁等待男方行聘，物不可以终止，故受之以渐；渐者，进也',
    idBagong: 32,
    idXiantian: 12,
    code: '䷴',
  },
  {
    id: 54,
    name: '归妹卦',
    label: '雷泽归妹',
    binary: '001011',
    phase: '婚嫁之道，守德避失',
    description: '归妹卦是女子有终身归宿，进必有所归，故受之以归妹',
    idBagong: 64,
    idXiantian: 53,
    code: '䷵',
  },
  {
    id: 55,
    name: '丰卦',
    label: '雷火丰',
    binary: '001101',
    phase: '丰盛光明，持盈保泰',
    description: '丰卦是故旧多，得其所归者必大，故受之以丰；丰者，大也',
    idBagong: 22,
    idXiantian: 45,
    code: '䷶',
  },
  {
    id: 56,
    name: '旅卦',
    label: '火山旅',
    binary: '101100',
    phase: '羁旅漂泊，柔顺得中',
    description: '旅卦是亲友少，穷大者必失其居，故受之以旅',
    idBagong: 50,
    idXiantian: 14,
    code: '䷷',
  },
  {
    id: 57,
    name: '巽卦',
    label: '巽为风',
    binary: '110110',
    phase: '谦逊柔顺，申命行事',
    description: '巽卦隐伏于内，旅而无所容，故受之以巽；巽者，入也',
    idBagong: 41,
    idXiantian: 28,
    code: '䷸',
  },
  {
    id: 58,
    name: '兑卦',
    label: '兑为泽',
    binary: '011011',
    phase: '欣悦沟通，和而不流',
    description: '兑卦显现于外，入而后说之，故受之以兑；兑者，说也',
    idBagong: 57,
    idXiantian: 55,
    code: '䷹',
  },
  {
    id: 59,
    name: '涣卦',
    label: '风水涣',
    binary: '110010',
    phase: '涣散分离，凝聚人心',
    description: '涣卦是离散，说而后散之，故受之以涣；涣者，离也',
    idBagong: 54,
    idXiantian: 20,
    code: '䷺',
  },
  {
    id: 60,
    name: '节卦',
    label: '水泽节',
    binary: '010011',
    phase: '节制有度，不逾规范',
    description: '节卦是节制，物不可以终离，故受之以节',
    idBagong: 18,
    idXiantian: 51,
    code: '䷻',
  },
  {
    id: 61,
    name: '中孚卦',
    label: '风泽中孚',
    binary: '110011',
    phase: '诚信立身，感化万物',
    description: '中孚卦是诚信，节而信之，故受之以中孚',
    idBagong: 31,
    idXiantian: 52,
    code: '䷼',
  },
  {
    id: 62,
    name: '小过卦',
    label: '雷山小过',
    binary: '001100',
    phase: '小有过越，行为有度',
    description: '小过卦是越过，有其信者必行之，故受之以小过',
    idBagong: 63,
    idXiantian: 13,
    code: '䷽',
  },
  {
    id: 63,
    name: '既济卦',
    label: '水火既济',
    binary: '010101',
    phase: '事已成功，慎防懈怠',
    description: '既济卦是安定，有过物者，必济，故受之以既济',
    idBagong: 20,
    idXiantian: 43,
    code: '䷾',
  },
  {
    id: 64,
    name: '未济卦',
    label: '火水未济',
    binary: '101010',
    phase: '事未完成，新的开始',
    description: '未济卦是男子穷途末路，物不可穷也，故受之以未济终焉',
    idBagong: 52,
    idXiantian: 22,
    code: '䷿',
  },
];
/* eslint-enable */

export const getHexagramById = (id: number): Hexagram => {
  return hexagrams.find((h) => h.id === id) || hexagrams[0];
};

/**
 * 获取所有卦象
 * @returns 所有卦象的数组
 */
export const getAllHexagrams = (): Hexagram[] => {
  return [...hexagrams];
};

/**
 * 根据二进制字符串查找卦象
 * @param binary 二进制字符串
 * @returns 匹配的卦象，如果没有找到则返回乾卦
 */
export const getHexagramByBinary = (binary: string): Hexagram => {
  const match = hexagrams.find((h) => h.binary === binary);
  return match || hexagrams[0];
};

/**
 * 验证所有卦象的binary是否唯一
 * @returns 是否所有binary都唯一
 */
export const validateHexagramBinaries = (): boolean => {
  const binaries = hexagrams.map((h) => h.binary);
  const uniqueBinaries = new Set(binaries);
  return binaries.length === uniqueBinaries.size;
};

// 导出卦象列表
export const hexagramList = hexagrams;

/**
 * 根据爻数组获取对应的卦象
 * @param yaos 爻数组，从下到上，1表示阳爻，0表示阴爻
 * @returns 对应的卦象，如果找不到则返回null
 */
export const getHexagramByYaos = (yaos: number[]): Hexagram | null => {
  if (yaos.length !== 6) {
    return null;
  }

  // 将爻数组转换为二进制字符串
  const binary = yaos.join('');

  // 查找对应的卦象
  return hexagrams.find((hexagram) => hexagram.binary === binary) || null;
};

/**
 * 获取错卦（阴阳全反）
 * @param hexagram 原卦象
 * @returns 错卦
 */
export const getCuoGua = (hexagram: Hexagram): Hexagram | null => {
  // 将所有爻的阴阳反转
  const cuoBinary = hexagram.binary
    .split('')
    .map((bit) => (bit === '1' ? '0' : '1'))
    .join('');

  return hexagrams.find((h) => h.binary === cuoBinary) || null;
};

/**
 * 获取综卦（上下颠倒）
 * @param hexagram 原卦象
 * @returns 综卦
 */
export const getZongGua = (hexagram: Hexagram): Hexagram | null => {
  // 将卦象上下颠倒
  const zongBinary = hexagram.binary.split('').reverse().join('');

  return hexagrams.find((h) => h.binary === zongBinary) || null;
};

/**
 * 获取互卦（中间四爻组成新卦）
 * @param hexagram 原卦象
 * @returns 互卦
 */
export const getHuGua = (hexagram: Hexagram): Hexagram | null => {
  const yaos = hexagram.binary.split('').map(Number);

  // 取中间四爻（第2、3、4、5爻）
  const middleYaos = yaos.slice(1, 5);

  // 构成新的六爻：上卦为第3、4、5爻，下卦为第2、3、4爻
  const huYaos = [
    middleYaos[0], // 第2爻
    middleYaos[1], // 第3爻
    middleYaos[2], // 第4爻
    middleYaos[1], // 第3爻（重复）
    middleYaos[2], // 第4爻（重复）
    middleYaos[3], // 第5爻
  ];

  const huBinary = huYaos.join('');

  return hexagrams.find((h) => h.binary === huBinary) || null;
};
