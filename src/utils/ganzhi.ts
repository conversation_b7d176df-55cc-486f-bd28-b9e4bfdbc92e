/**
 * 天干地支工具函数
 * 包含十天干、十二地支的基础数据和相关计算函数
 */

// 天干类型定义
export type Tiangan = {
  id: number;
  name: string;
  pinyin: string;
  wuxing: string; // 五行属性
  yinyang: '阳' | '阴'; // 阴阳属性
  color: string; // 对应颜色
  direction: string; // 对应方位
  season: string; // 对应季节
  description: string; // 描述
};

// 地支类型定义
export type Dizhi = {
  id: number;
  name: string;
  pinyin: string;
  wuxing: string; // 五行属性
  yinyang: '阳' | '阴'; // 阴阳属性
  shichen: string; // 对应时辰
  zodiac: string; // 对应生肖
  icon: string; // 生肖emoji图标
  month: number; // 对应月份
  direction: string; // 对应方位
  season: string; // 对应季节
  description: string; // 描述
};

// 干支组合类型定义
export type Ganzhi = {
  tiangan: Tiangan;
  dizhi: Dizhi;
  name: string; // 组合名称，如"甲子"
  id: number; // 1-60的序号
  nayin: string; // 纳音五行
  description: string; // 组合描述
};

// 十天干数据
export const tianganList: Tiangan[] = [
  {
    id: 1,
    name: '甲',
    pinyin: 'jiǎ',
    wuxing: '木',
    yinyang: '阳',
    color: '青绿',
    direction: '东',
    season: '春',
    description: '甲木参天，象征大树，具有向上生长的特性，代表仁德、生机',
  },
  {
    id: 2,
    name: '乙',
    pinyin: 'yǐ',
    wuxing: '木',
    yinyang: '阴',
    color: '翠绿',
    direction: '东',
    season: '春',
    description: '乙木花草，象征柔韧的植物，具有适应性强的特点，代表温和、包容',
  },
  {
    id: 3,
    name: '丙',
    pinyin: 'bǐng',
    wuxing: '火',
    yinyang: '阳',
    color: '红',
    direction: '南',
    season: '夏',
    description: '丙火太阳，象征阳光普照，具有光明温暖的特性，代表热情、光明',
  },
  {
    id: 4,
    name: '丁',
    pinyin: 'dīng',
    wuxing: '火',
    yinyang: '阴',
    color: '橙红',
    direction: '南',
    season: '夏',
    description: '丁火灯烛，象征温和的火光，具有照明指引的作用，代表智慧、文明',
  },
  {
    id: 5,
    name: '戊',
    pinyin: 'wù',
    wuxing: '土',
    yinyang: '阳',
    color: '黄',
    direction: '中',
    season: '长夏',
    description: '戊土城墙，象征厚重的土地，具有承载万物的特性，代表稳重、包容',
  },
  {
    id: 6,
    name: '己',
    pinyin: 'jǐ',
    wuxing: '土',
    yinyang: '阴',
    color: '黄褐',
    direction: '中',
    season: '长夏',
    description: '己土田园，象征肥沃的土壤，具有孕育生命的能力，代表温润、养育',
  },
  {
    id: 7,
    name: '庚',
    pinyin: 'gēng',
    wuxing: '金',
    yinyang: '阳',
    color: '白',
    direction: '西',
    season: '秋',
    description: '庚金刀剑，象征坚硬的金属，具有刚强果断的特性，代表正义、刚毅',
  },
  {
    id: 8,
    name: '辛',
    pinyin: 'xīn',
    wuxing: '金',
    yinyang: '阴',
    color: '银白',
    direction: '西',
    season: '秋',
    description: '辛金珠玉，象征精美的金属，具有精致细腻的特点，代表美丽、纯洁',
  },
  {
    id: 9,
    name: '壬',
    pinyin: 'rén',
    wuxing: '水',
    yinyang: '阳',
    color: '黑',
    direction: '北',
    season: '冬',
    description: '壬水江河，象征奔流的大水，具有包容万物的特性，代表智慧、流动',
  },
  {
    id: 10,
    name: '癸',
    pinyin: 'guǐ',
    wuxing: '水',
    yinyang: '阴',
    color: '深蓝',
    direction: '北',
    season: '冬',
    description: '癸水雨露，象征滋润的小水，具有润物无声的特点，代表柔和、滋养',
  },
];

// 十二地支数据
export const dizhiList: Dizhi[] = [
  {
    id: 1,
    name: '子',
    pinyin: 'zǐ',
    wuxing: '水',
    yinyang: '阳',
    shichen: '23:00-01:00',
    zodiac: '鼠',
    icon: '🐭',
    month: 11,
    direction: '北',
    season: '冬',
    description: '子时夜半，万物归藏，象征新的开始和潜藏的力量',
  },
  {
    id: 2,
    name: '丑',
    pinyin: 'chǒu',
    wuxing: '土',
    yinyang: '阴',
    shichen: '01:00-03:00',
    zodiac: '牛',
    icon: '🐮',
    month: 12,
    direction: '东北',
    season: '冬',
    description: '丑时鸡鸣，勤劳踏实，象征坚韧不拔的品格',
  },
  {
    id: 3,
    name: '寅',
    pinyin: 'yín',
    wuxing: '木',
    yinyang: '阳',
    shichen: '03:00-05:00',
    zodiac: '虎',
    icon: '🐯',
    month: 1,
    direction: '东北',
    season: '春',
    description: '寅时平旦，阳气初生，象征勇猛和新生的力量',
  },
  {
    id: 4,
    name: '卯',
    pinyin: 'mǎo',
    wuxing: '木',
    yinyang: '阴',
    shichen: '05:00-07:00',
    zodiac: '兔',
    icon: '🐰',
    month: 2,
    direction: '东',
    season: '春',
    description: '卯时日出，万物复苏，象征温和与生机勃勃',
  },
  {
    id: 5,
    name: '辰',
    pinyin: 'chén',
    wuxing: '土',
    yinyang: '阳',
    shichen: '07:00-09:00',
    zodiac: '龙',
    icon: '🐲',
    month: 3,
    direction: '东南',
    season: '春',
    description: '辰时食时，龙腾四海，象征变化和神秘的力量',
  },
  {
    id: 6,
    name: '巳',
    pinyin: 'sì',
    wuxing: '火',
    yinyang: '阴',
    shichen: '09:00-11:00',
    zodiac: '蛇',
    icon: '🐍',
    month: 4,
    direction: '东南',
    season: '夏',
    description: '巳时隅中，智慧深藏，象征智慧和神秘的洞察力',
  },
  {
    id: 7,
    name: '午',
    pinyin: 'wǔ',
    wuxing: '火',
    yinyang: '阳',
    shichen: '11:00-13:00',
    zodiac: '马',
    icon: '🐴',
    month: 5,
    direction: '南',
    season: '夏',
    description: '午时日中，阳气最盛，象征热情奔放和积极向上',
  },
  {
    id: 8,
    name: '未',
    pinyin: 'wèi',
    wuxing: '土',
    yinyang: '阴',
    shichen: '13:00-15:00',
    zodiac: '羊',
    icon: '🐑',
    month: 6,
    direction: '西南',
    season: '夏',
    description: '未时日昳，温和包容，象征和谐与慈爱',
  },
  {
    id: 9,
    name: '申',
    pinyin: 'shēn',
    wuxing: '金',
    yinyang: '阳',
    shichen: '15:00-17:00',
    zodiac: '猴',
    icon: '🐵',
    month: 7,
    direction: '西南',
    season: '秋',
    description: '申时晡时，机智灵活，象征智慧和变通能力',
  },
  {
    id: 10,
    name: '酉',
    pinyin: 'yǒu',
    wuxing: '金',
    yinyang: '阴',
    shichen: '17:00-19:00',
    zodiac: '鸡',
    icon: '🐔',
    month: 8,
    direction: '西',
    season: '秋',
    description: '酉时日入，收获时节，象征勤勉和收获的喜悦',
  },
  {
    id: 11,
    name: '戌',
    pinyin: 'xū',
    wuxing: '土',
    yinyang: '阳',
    shichen: '19:00-21:00',
    zodiac: '狗',
    icon: '🐕',
    month: 9,
    direction: '西北',
    season: '秋',
    description: '戌时黄昏，忠诚守护，象征忠诚和责任感',
  },
  {
    id: 12,
    name: '亥',
    pinyin: 'hài',
    wuxing: '水',
    yinyang: '阴',
    shichen: '21:00-23:00',
    zodiac: '猪',
    icon: '🐷',
    month: 10,
    direction: '西北',
    season: '冬',
    description: '亥时人定，安详宁静，象征包容和内敛的智慧',
  },
];

/**
 * 根据天干地支ID获取干支组合
 * @param tianganId 天干ID (1-10)
 * @param dizhiId 地支ID (1-12)
 * @returns 干支组合信息
 */
export const getGanzhiCombination = (tianganId: number, dizhiId: number): Ganzhi | null => {
  const tiangan = tianganList.find((t) => t.id === tianganId);
  const dizhi = dizhiList.find((d) => d.id === dizhiId);

  if (!tiangan || !dizhi) return null;

  // 计算六十甲子的序号
  const ganzhiId = ((tianganId - 1) * 6 + Math.floor((dizhiId - 1) / 2) + 1) % 60 || 60;

  return {
    tiangan,
    dizhi,
    name: `${tiangan.name}${dizhi.name}`,
    id: ganzhiId,
    nayin: getNayin(tianganId, dizhiId),
    description: `${tiangan.description.split('，')[0]}配${dizhi.description.split('，')[0]}`,
  };
};

/**
 * 获取纳音五行
 * @param tianganId 天干ID
 * @param dizhiId 地支ID
 * @returns 纳音五行
 */
export const getNayin = (tianganId: number, dizhiId: number): string => {
  // 完整的纳音对照表 - 六十甲子纳音配对
  const nayinTable: { [key: string]: string } = {
    // 第一轮（甲子-乙亥）
    甲子: '海中金',
    乙丑: '海中金',
    丙寅: '炉中火',
    丁卯: '炉中火',
    戊辰: '大林木',
    己巳: '大林木',
    庚午: '路旁土',
    辛未: '路旁土',
    壬申: '剑锋金',
    癸酉: '剑锋金',
    甲戌: '山头火',
    乙亥: '山头火',

    // 第二轮（丙子-丁亥）
    丙子: '洞下水',
    丁丑: '洞下水',
    戊寅: '城墙土',
    己卯: '城墙土',
    庚辰: '白腊金',
    辛巳: '白腊金',
    壬午: '杨柳木',
    癸未: '杨柳木',
    甲申: '泉中水',
    乙酉: '泉中水',
    丙戌: '屋上土',
    丁亥: '屋上土',

    // 第三轮（戊子-己亥）
    戊子: '霹雷火',
    己丑: '霹雷火',
    庚寅: '松柏木',
    辛卯: '松柏木',
    壬辰: '常流水',
    癸巳: '常流水',
    甲午: '沙中金',
    乙未: '沙中金',
    丙申: '山下火',
    丁酉: '山下火',
    戊戌: '平地木',
    己亥: '平地木',

    // 第四轮（庚子-辛亥）
    庚子: '壁上土',
    辛丑: '壁上土',
    壬寅: '金箔金',
    癸卯: '金箔金',
    甲辰: '佛灯火',
    乙巳: '佛灯火',
    丙午: '天河水',
    丁未: '天河水',
    戊申: '大驿土',
    己酉: '大驿土',
    庚戌: '钗钏金',
    辛亥: '钗钏金',

    // 第五轮（壬子-癸亥）
    壬子: '桑松木',
    癸丑: '桑松木',
    甲寅: '大溪水',
    乙卯: '大溪水',
    丙辰: '沙中土',
    丁巳: '沙中土',
    戊午: '天上火',
    己未: '天上火',
    庚申: '石榴木',
    辛酉: '石榴木',
    壬戌: '大海水',
    癸亥: '大海水',
  };

  const tiangan = tianganList.find((t) => t.id === tianganId);
  const dizhi = dizhiList.find((d) => d.id === dizhiId);

  if (!tiangan || !dizhi) return '未知';

  const key = `${tiangan.name}${dizhi.name}`;
  return nayinTable[key] || '未知';
};

/**
 * 获取当前时辰对应的地支
 * @param hour 小时 (0-23)
 * @returns 对应的地支
 */
export const getCurrentDizhi = (hour: number): Dizhi => {
  // 时辰对照表
  const timeMap = [
    { start: 23, end: 1, dizhiId: 1 }, // 子时
    { start: 1, end: 3, dizhiId: 2 }, // 丑时
    { start: 3, end: 5, dizhiId: 3 }, // 寅时
    { start: 5, end: 7, dizhiId: 4 }, // 卯时
    { start: 7, end: 9, dizhiId: 5 }, // 辰时
    { start: 9, end: 11, dizhiId: 6 }, // 巳时
    { start: 11, end: 13, dizhiId: 7 }, // 午时
    { start: 13, end: 15, dizhiId: 8 }, // 未时
    { start: 15, end: 17, dizhiId: 9 }, // 申时
    { start: 17, end: 19, dizhiId: 10 }, // 酉时
    { start: 19, end: 21, dizhiId: 11 }, // 戌时
    { start: 21, end: 23, dizhiId: 12 }, // 亥时
  ];

  for (const time of timeMap) {
    if (time.start === 23) {
      // 子时跨日处理
      if (hour >= 23 || hour < 1) {
        return dizhiList[time.dizhiId - 1];
      }
    } else if (hour >= time.start && hour < time.end) {
      return dizhiList[time.dizhiId - 1];
    }
  }

  return dizhiList[0]; // 默认返回子时
};

/**
 * 根据年份获取对应的天干地支
 * @param year 年份
 * @returns 年份对应的干支
 */
export const getYearGanzhi = (year: number): Ganzhi | null => {
  // 以1984年甲子年为基准
  const baseYear = 1984;
  const yearDiff = year - baseYear;

  const tianganId = ((yearDiff % 10) + 1 + 10) % 10 || 10;
  const dizhiId = ((yearDiff % 12) + 1 + 12) % 12 || 12;

  return getGanzhiCombination(tianganId, dizhiId);
};

/**
 * 生成完整的六十甲子表
 * @returns 六十甲子数组
 */
export const generateJiaziTable = (): Ganzhi[] => {
  const jiaziTable: Ganzhi[] = [];

  for (let i = 0; i < 60; i++) {
    const tianganId = (i % 10) + 1;
    const dizhiId = (i % 12) + 1;
    const ganzhi = getGanzhiCombination(tianganId, dizhiId);

    if (ganzhi) {
      ganzhi.id = i + 1;
      jiaziTable.push(ganzhi);
    }
  }

  return jiaziTable;
};
