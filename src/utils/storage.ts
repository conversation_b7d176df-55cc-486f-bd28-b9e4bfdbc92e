/**
 * 本地存储工具函数
 */

import { ref, watch } from 'vue';
import { getStringByteSize } from '@/utils/tools';

// 存储错误类型
export enum StorageErrorType {
  QUOTA_EXCEEDED = 'quota_exceeded',
  PARSE_ERROR = 'parse_error',
  ACCESS_DENIED = 'access_denied',
  UNKNOWN_ERROR = 'unknown_error',
}

// 存储错误接口
export interface StorageError {
  type: StorageErrorType;
  message: string;
  key?: string;
  originalError?: Error;
}

// 错误处理选项
export interface StorageOptions {
  onError?: (error: StorageError) => void;
  silent?: boolean; // 是否静默处理错误
}

// 错误处理工具
const createStorageError = (
  type: StorageErrorType,
  message: string,
  key?: string,
  originalError?: Error
): StorageError => ({
  type,
  message,
  key,
  originalError,
});

const handleStorageError = (error: unknown, key: string, operation: string, options?: StorageOptions): StorageError => {
  let errorType = StorageErrorType.UNKNOWN_ERROR;
  let message = `Storage ${operation} failed for key: ${key}`;

  // 根据错误信息判断错误类型
  const errorMessage = error instanceof Error ? error.message : String(error);
  const errorName = error instanceof Error ? error.name : '';

  if (errorMessage.includes('QuotaExceededError') || errorMessage.includes('quota')) {
    errorType = StorageErrorType.QUOTA_EXCEEDED;
    message = `Storage quota exceeded when ${operation} key: ${key}`;
  } else if (errorName === 'SyntaxError' || errorMessage.includes('JSON')) {
    errorType = StorageErrorType.PARSE_ERROR;
    message = `JSON parse error when ${operation} key: ${key}`;
  } else if (errorMessage.includes('access') || errorMessage.includes('permission')) {
    errorType = StorageErrorType.ACCESS_DENIED;
    message = `Storage access denied when ${operation} key: ${key}`;
  }

  const storageError = createStorageError(errorType, message, key, error instanceof Error ? error : undefined);

  // 处理错误
  if (options?.onError) {
    options.onError(storageError);
  } else if (!options?.silent) {
    console.error(`[Storage Error] ${message}`, error);
  }

  return storageError;
};

/**
 * 响应式本地存储 hook
 */
export function useStorage<T>(key: string, defaultValue: T) {
  const storedValue = ref<T>(defaultValue);

  // 从存储中读取值
  const loadValue = () => {
    try {
      const item = uni.getStorageSync(key);
      if (item !== '') {
        storedValue.value = JSON.parse(item);
      }
    } catch (error) {
      console.warn(`Failed to load storage key: ${key}`, error);
      storedValue.value = defaultValue;
    }
  };

  // 保存值到存储
  const saveValue = (value: T) => {
    try {
      uni.setStorageSync(key, JSON.stringify(value));
      storedValue.value = value;
    } catch (error) {
      console.error(`Failed to save storage key: ${key}`, error);
    }
  };

  // 初始化时加载值
  loadValue();

  // 监听值的变化并自动保存
  watch(
    storedValue,
    (newValue) => {
      saveValue(newValue);
    },
    { deep: true }
  );

  return {
    value: storedValue,
    setValue: saveValue,
  };
}

/**
 * 简单的存储工具函数（非响应式）
 */
export const storage = {
  // 设置存储
  set<T>(key: string, value: T, options?: StorageOptions): boolean {
    try {
      uni.setStorageSync(key, JSON.stringify(value));
      return true;
    } catch (error) {
      handleStorageError(error, key, 'set', options);
      return false;
    }
  },

  // 获取存储 - 强制提供默认值版本
  get<T>(key: string, defaultValue: T, options?: StorageOptions): T {
    try {
      const item = uni.getStorageSync(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      handleStorageError(error, key, 'get', options);
      return defaultValue;
    }
  },

  // 获取存储 - 可能返回 null 版本
  getOrNull<T>(key: string, options?: StorageOptions): T | null {
    try {
      const item = uni.getStorageSync(key);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      handleStorageError(error, key, 'get', options);
      return null;
    }
  },

  // 删除存储
  remove(key: string): void {
    try {
      uni.removeStorageSync(key);
    } catch (error) {
      console.error(`Failed to remove storage for key: ${key}`, error);
    }
  },

  // 清空所有存储
  clear(): void {
    try {
      uni.clearStorageSync();
    } catch (error) {
      console.error('Failed to clear storage:', error);
    }
  },

  // 带过期时间的缓存
  setCache<T>(key: string, value: T, expireHours: number = 24): void {
    const data = {
      value,
      expire: Date.now() + expireHours * 60 * 60 * 1000,
      timestamp: Date.now(),
    };
    this.set(`cache_${key}`, data);
  },

  getCache<T>(key: string): T | null {
    const cached = this.getOrNull<{ value: T; expire: number }>(`cache_${key}`);
    if (cached && Date.now() < cached.expire) {
      return cached.value;
    }
    if (cached) {
      this.remove(`cache_${key}`); // 清理过期缓存
    }
    return null;
  },

  // 清理过期缓存
  cleanExpiredCache(): void {
    try {
      uni.getStorageInfo({
        success: (res) => {
          const now = Date.now();
          res.keys.forEach((key) => {
            if (key.startsWith('cache_')) {
              const cached = this.getOrNull<{ expire: number }>(key);
              if (cached && cached.expire && now > cached.expire) {
                this.remove(key);
              }
            }
          });
        },
      });
    } catch (error) {
      console.warn('Clean cache failed:', error);
    }
  },
};

/**
 * 存储配额管理器
 */
export const storageManager = {
  // 获取存储使用情况
  getUsage(): Promise<{ used: number; total: number; percentage: number }> {
    return new Promise((resolve) => {
      uni.getStorageInfo({
        success: (res) => {
          const used = res.currentSize || 0;
          const total = res.limitSize || 10240; // 默认10MB
          const percentage = Math.round((used / total) * 100);
          resolve({ used, total, percentage });
        },
        fail: () => {
          resolve({ used: 0, total: 10240, percentage: 0 });
        },
      });
    });
  },

  // 清理策略
  async cleanup(
    options: {
      clearExpiredCache?: boolean;
      clearOldHistory?: boolean;
      maxHistoryDays?: number;
    } = {}
  ): Promise<void> {
    const { clearExpiredCache = true, clearOldHistory = false, maxHistoryDays = 30 } = options;

    if (clearExpiredCache) {
      storage.cleanExpiredCache();
    }

    if (clearOldHistory) {
      // 清理旧的游戏历史记录
      const cutoffTime = Date.now() - maxHistoryDays * 24 * 60 * 60 * 1000;

      uni.getStorageInfo({
        success: (res) => {
          res.keys.forEach((key) => {
            if (key.startsWith('score_history_')) {
              const history = storage.get(key, []);
              const filteredHistory = history.filter(
                (record: Record<string, unknown>) => record.timestamp && (record.timestamp as number) > cutoffTime
              );

              if (filteredHistory.length !== history.length) {
                storage.set(key, filteredHistory);
              }
            }
          });
        },
      });
    }
  },

  // 配额超出回调
  onQuotaExceeded: null as ((usage: { used: number; total: number; percentage: number }) => void) | null,

  // 检查并处理配额
  async checkQuota(): Promise<boolean> {
    const usage = await this.getUsage();

    if (usage.percentage > 90) {
      if (this.onQuotaExceeded) {
        this.onQuotaExceeded(usage);
      } else {
        console.warn(`Storage usage high: ${usage.percentage}%`);
        // 自动清理
        await this.cleanup({ clearExpiredCache: true, clearOldHistory: true });
      }
      return false;
    }

    return true;
  },
};

/**
 * 业务相关的存储工具
 */

// 卦象内容缓存（优化版）
export const hexagramCache = {
  // 获取缓存的卦象内容
  get: (id: number): string | null => {
    return storage.getCache<string>(`hexagram_${id}`);
  },

  // 设置缓存（默认7天过期，因为内容相对稳定）
  set: (id: number, content: string, hours: number = 24 * 7): void => {
    // 检查内容大小，如果超过50KB则不缓存
    const contentSize = getStringByteSize(content);
    if (contentSize > 50 * 1024) {
      console.warn(`卦象${id}内容过大(${Math.round(contentSize / 1024)}KB)，跳过缓存`);
      return;
    }

    storage.setCache(`hexagram_${id}`, content, hours);
    console.log(`卦象${id}已缓存，大小: ${Math.round(contentSize / 1024)}KB`);
  },

  // 删除指定卦象缓存
  remove: (id: number): void => {
    storage.remove(`cache_hexagram_${id}`);
  },

  // 检查缓存是否存在
  has: (id: number): boolean => {
    return storage.getCache<string>(`hexagram_${id}`) !== null;
  },

  // 获取缓存信息（大小、过期时间等）
  getInfo: (id: number) => {
    const cached = storage.getOrNull<{ value: string; expire: number; timestamp: number }>(`cache_hexagram_${id}`);
    if (!cached) return null;

    const size = getStringByteSize(cached.value);
    const isExpired = Date.now() > cached.expire;
    const remainingHours = Math.max(0, Math.round((cached.expire - Date.now()) / (1000 * 60 * 60)));

    return {
      size: Math.round(size / 1024), // KB
      isExpired,
      remainingHours,
      cachedAt: new Date(cached.timestamp).toLocaleString(),
    };
  },

  // 清理所有卦象缓存
  clearAll: (): void => {
    for (let i = 1; i <= 64; i++) {
      storage.remove(`cache_hexagram_${i}`);
    }
    console.log('已清理所有卦象缓存');
  },

  // 获取缓存统计信息
  getStats: () => {
    let totalSize = 0;
    let cachedCount = 0;
    let expiredCount = 0;

    for (let i = 1; i <= 64; i++) {
      const info = hexagramCache.getInfo(i);
      if (info) {
        cachedCount++;
        totalSize += info.size;
        if (info.isExpired) expiredCount++;
      }
    }

    return {
      totalSize, // KB
      cachedCount,
      expiredCount,
      hitRate: Math.round((cachedCount / 64) * 100), // 缓存命中率
    };
  },
};
