// 八卦相关数据和工具函数

export interface Bagua {
  name: string;
  symbol: string;
  nature: string;
  element: string;
  binary: string;
  direction: string;
  family: string;
  body: string;
  description: string;
  phenomena: string; // 天时
  geography: string; // 地理
  character: string; // 人物
  personality: string; // 人事
  soma: string; // 身体
}

// 八卦基本信息列表
export const baguaList: Bagua[] = [
  {
    name: '乾',
    symbol: '☰',
    nature: '天',
    element: '金',
    binary: '111',
    direction: '西北',
    family: '父',
    body: '首',
    description: '乾为天，刚健中正，自强不息',
    phenomena: '天，冰，雹，霰',
    geography: '西北方，京都，大郡，形胜之地，高亢之所',
    character: '君，父，大人，老人，长者，官宦，名人，公门人',
    personality: '刚健武勇，果决，多动少静，高上下屈',
    soma: '首，骨，肺',
  },
  {
    name: '兑',
    symbol: '☱',
    nature: '泽',
    element: '金',
    binary: '011',
    direction: '西',
    family: '少女',
    body: '口',
    description: '兑为泽，喜悦沟通，和悦相处',
    phenomena: '雨泽，新月，星',
    geography: '西方，水际，缺池，废井，山崩破裂之地，其地为刚卤',
    character: '少女，妾，歌妓，伶人，译人，巫师',
    personality: '喜悦，口，谗毁，谤说，饮食',
    soma: '舌，口喉，肺，痰，涎',
  },
  {
    name: '离',
    symbol: '☲',
    nature: '火',
    element: '火',
    binary: '101',
    direction: '南',
    family: '中女',
    body: '目',
    description: '离为火，光明美丽，文明礼仪',
    phenomena: '日，电，虹，霓，霞',
    geography: '南方，干亢之地，窖灶，炉冶之所，刚燥厥地，其地面阳',
    character: '中女，文人，大腹，目疾人，甲胄之士',
    personality: '文画之所，聪明才学，相见虚心，书事',
    soma: '目，心，上焦',
  },
  {
    name: '震',
    symbol: '☳',
    nature: '雷',
    element: '木',
    binary: '001',
    direction: '东',
    family: '长男',
    body: '足',
    description: '震为雷，奋发向上，积极进取',
    phenomena: '雷',
    geography: '东方，树木，闹市，大途，竹林，草木茂盛之所',
    character: '长男',
    personality: '起动，怒，虚惊，鼓动噪，多动少静',
    soma: '足，肝，发，声音',
  },
  {
    name: '巽',
    symbol: '☴',
    nature: '风',
    element: '木',
    binary: '110',
    direction: '东南',
    family: '长女',
    body: '股',
    description: '巽为风，谦逊温和，随风而动',
    phenomena: '风',
    geography: '东南方，草木茂秀之所，花果菜园',
    character: '长女，秀士，寡妇之人，山林仙道之人',
    personality: '柔和，不定，鼓舞，利市三倍，进退不果',
    soma: '肱，股，气，风疾',
  },
  {
    name: '坎',
    symbol: '☵',
    nature: '水',
    element: '水',
    binary: '010',
    direction: '北',
    family: '中男',
    body: '耳',
    description: '坎为水，智慧深沉，险中求胜',
    phenomena: '月，雨，雪，霜，露',
    geography: '北方，江湖，溪涧，泉井，卑湿之地(沟渎，池沼，凡有水处)',
    character: '中男，江湖之人，舟人，盗贼',
    personality: '险陷卑下，外示以柔，内序以利，漂泊不成，随波逐流',
    soma: '耳，血，肾',
  },
  {
    name: '艮',
    symbol: '☶',
    nature: '山',
    element: '土',
    binary: '100',
    direction: '东北',
    family: '少男',
    body: '手',
    description: '艮为山，稳重踏实，止于至善',
    phenomena: '云，雾，山岚',
    geography: '东北方，山径路，近山城，丘陵，坟墓',
    character: '少男，闲人，山中人',
    personality: '阻隔，守静，进退不决，反背，止住，不见',
    soma: '手指，骨，鼻，背',
  },
  {
    name: '坤',
    symbol: '☷',
    nature: '地',
    element: '土',
    binary: '000',
    direction: '西南',
    family: '母',
    body: '腹',
    description: '坤为地，厚德载物，包容万象',
    phenomena: '云阴，雾气',
    geography: '西南方，田野，乡里，平地',
    character: '老母，后母，农夫，乡人，众人，大腹人',
    personality: '吝啬，柔顺，懦弱，众多',
    soma: '腹，脾，胃，肉',
  },
];

// 根据八卦名称获取详细信息
export function getBaguaDetail(name: string): Bagua | undefined {
  return baguaList.find((bagua) => bagua.name === name);
}

// BaguaCircle 组件相关类型和数据
export type BaguaName = '乾' | '兑' | '离' | '震' | '巽' | '坎' | '艮' | '坤';

export interface PositionInfo {
  index: number;
  sequence: string;
  direction: string;
}

export interface PositionedBagua extends Bagua {
  positionIndex: number;
  sequence: string;
}

// 先天八卦位置映射（伏羲八卦）- 逆时针：乾、巽、坎、艮、坤、震、离、兑
export const xiantianPositions: Record<BaguaName, PositionInfo> = {
  乾: { index: 0, sequence: '一', direction: '南' },
  巽: { index: 1, sequence: '五', direction: '西南' },
  坎: { index: 2, sequence: '六', direction: '西' },
  艮: { index: 3, sequence: '七', direction: '西北' },
  坤: { index: 4, sequence: '八', direction: '北' },
  震: { index: 5, sequence: '四', direction: '东北' },
  离: { index: 6, sequence: '三', direction: '东' },
  兑: { index: 7, sequence: '二', direction: '东南' },
};

// 后天八卦位置映射（文王八卦）
export const houtianPositions: Record<BaguaName, PositionInfo> = {
  离: { index: 0, sequence: '九', direction: '南' },
  坤: { index: 1, sequence: '二', direction: '西南' },
  兑: { index: 2, sequence: '七', direction: '西' },
  乾: { index: 3, sequence: '六', direction: '西北' },
  坎: { index: 4, sequence: '一', direction: '北' },
  艮: { index: 5, sequence: '八', direction: '东北' },
  震: { index: 6, sequence: '三', direction: '东' },
  巽: { index: 7, sequence: '四', direction: '东南' },
};

// 获取定位后的八卦数据
export function getPositionedBaguaData(layout: 'xiantian' | 'houtian'): PositionedBagua[] {
  const positions = layout === 'xiantian' ? xiantianPositions : houtianPositions;

  return baguaList
    .map((gua: Bagua) => ({
      ...gua,
      direction: positions[gua.name as BaguaName].direction,
      positionIndex: positions[gua.name as BaguaName].index,
      sequence: positions[gua.name as BaguaName].sequence,
    }))
    .sort((a, b) => a.positionIndex - b.positionIndex);
}

// 根据八卦名称获取基本信息
export function getBaguaInfo(name: string): Bagua | undefined {
  return baguaList.find((gua) => gua.name === name);
}
