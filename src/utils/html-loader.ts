/**
 * HTML内容加载器
 * 用于加载远程HTML内容，支持本地回退、缓存和解压缩
 */

import { CDN_DOMAIN } from '@/config';
import { hexagramCache } from '@/utils/storage';
import { isCompressedContent, decompressContent } from '@/utils/html-decompressor';

/**
 * 缓存配置选项
 */
interface CacheOptions {
  /**
   * 缓存过期时间（小时）
   * @default 168 (7天)
   */
  expireHours?: number;

  /**
   * 是否强制刷新缓存
   * @default false
   */
  forceRefresh?: boolean;
}

/**
 * 加载HTML内容
 * @param id - 内容ID
 * @param contentType - 内容类型，用于构建URL路径和默认内容
 * @param cacheOptions - 缓存配置选项
 * @returns Promise<string> - 返回HTML内容
 */
export function loadHtmlContent(
  id: number,
  contentType: string = 'hexagram',
  cacheOptions: CacheOptions = {}
): Promise<string> {
  const { expireHours = 168, forceRefresh = false } = cacheOptions; // 默认7天过期
  return new Promise<string>((resolve) => {
    const defaultContent = `<div class="v-container zy"><div class="v-author"><span>${contentType}${id}</span></div><div>内容加载失败</div></div>`;
    const cdnUrl = `${CDN_DOMAIN}/${contentType}/${id}.dat`;

    // #ifdef H5
    console.log(`[html-loader] H5环境，先尝试本地文件: /src/assets/${id}.html`);
    uni.request({
      url: `/src/assets/${id}.html`,
      method: 'GET',
      success: (localRes) => {
        console.log(
          `[html-loader] 本地文件请求响应: 状态${localRes.statusCode}, 数据类型${typeof localRes.data}, 长度${localRes.data ? String(localRes.data).length : 0}`
        );

        if (localRes.statusCode === 200 && localRes.data) {
          const content = String(localRes.data);
          console.log(`[html-loader] ✅ 本地文件加载成功，内容长度: ${content.length}`);
          resolve(content);
        } else {
          console.log(`[html-loader] ❌ 本地文件加载失败，转向远程加载`);
          loadRemote();
        }
      },
      fail: (localError) => {
        console.log(`[html-loader] ❌ 本地文件请求失败: ${localError.errMsg || localError}`);
        loadRemote();
      },
    });
    // #endif

    // #ifndef H5
    console.log(`[html-loader] 非H5环境，直接使用远程加载`);
    loadRemote();
    // #endif

    function loadRemote() {
      console.log(
        `[html-loader] 🔍 检查缓存: ${contentType}${id} (过期时间: ${expireHours}小时, 强制刷新: ${forceRefresh})`
      );

      // 检查是否强制刷新
      if (forceRefresh) {
        console.log(`[html-loader] 🔄 强制刷新缓存: ${contentType}${id}`);
        hexagramCache.remove(id);
      } else {
        // 先检查缓存
        const cachedContent = hexagramCache.get(id);
        if (cachedContent) {
          console.log(`[html-loader] ✅ 缓存命中: ${contentType}${id}，内容长度: ${cachedContent.length}`);
          resolve(cachedContent);
          return;
        }
      }

      console.log(`[html-loader] ❌ 缓存未命中，开始远程加载: ${contentType}${id}`);
      console.log(`[html-loader] 🌐 发起远程请求: ${cdnUrl}`);

      uni.request({
        url: cdnUrl,
        method: 'GET',
        timeout: 10000, // 10秒超时
        success: (res) => {
          console.log(
            `[html-loader] 🌐 远程请求响应: 状态${res.statusCode}, 数据类型${typeof res.data}, 长度${res.data ? String(res.data).length : 0}`
          );

          if (res.statusCode === 200 && res.data) {
            let content;
            const dataString = String(res.data);

            console.log(`[html-loader] 📄 原始数据预览: ${dataString.substring(0, 100)}...`);

            // 检查是否是压缩内容并解压
            if (isCompressedContent(dataString)) {
              console.log(`[html-loader] 🗜️ 检测到压缩内容，开始解压: ${contentType}${id}`);
              content = decompressContent(dataString);
              if (!content) {
                console.log(`[html-loader] ❌ 解压失败: ${contentType}${id}`);
                return;
              } else {
                console.log(`[html-loader] ✅ 解压成功: ${contentType}${id}，解压后长度: ${content.length}`);
              }
            } else {
              console.log(`[html-loader] 📄 原始HTML内容，长度: ${dataString.length}`);
              content = dataString;
            }

            // 缓存内容（使用自定义过期时间）
            console.log(`[html-loader] 💾 缓存内容: ${contentType}${id} (过期时间: ${expireHours}小时)`);
            hexagramCache.set(id, content, expireHours);

            console.log(`[html-loader] ✅ 加载完成: ${contentType}${id}，最终内容长度: ${content.length}`);
            resolve(content);
          } else {
            console.log(`[html-loader] ❌ 远程请求失败: 状态${res.statusCode}, URL: ${cdnUrl}`);
            resolve(defaultContent);
          }
        },
        fail: (error) => {
          console.log(`[html-loader] ❌ 远程请求异常: ${error.errMsg || error}, URL: ${cdnUrl}`);
          resolve(defaultContent);
        },
      });
    }
  });
}

/**
 * 加载卦象HTML内容（向后兼容）
 * @param id - 卦象ID
 * @param cacheOptions - 缓存配置选项
 * @returns Promise<string> - 返回HTML内容
 */
export function loadHexagramContent(id: number, cacheOptions?: CacheOptions): Promise<string> {
  console.log(`[html-loader] 🎯 加载卦象内容: ${id}`);
  return loadHtmlContent(id, 'hexagram', cacheOptions);
}
