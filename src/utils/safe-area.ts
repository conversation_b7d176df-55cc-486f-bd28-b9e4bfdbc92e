/**
 * 安全区域适配工具
 * 解决小程序页面顶部与状态栏、关闭按钮重叠问题
 * 注意：H5环境不需要安全区域适配
 */

// 系统信息缓存
let systemInfo: UniApp.GetSystemInfoResult | null = null;

/**
 * 检查是否需要安全区域适配
 * H5环境不需要，APP和小程序需要
 */
export function needsSafeAreaAdaptation(): boolean {
  // #ifdef H5
  return false;
  // #endif

  // #ifdef APP-PLUS || MP
  return true;
  // #endif

  return false;
}

/**
 * 获取系统信息
 */
export function getSystemInfo(): Promise<UniApp.GetSystemInfoResult> {
  return new Promise((resolve) => {
    if (systemInfo) {
      resolve(systemInfo);
      return;
    }

    uni.getSystemInfo({
      success: (res) => {
        systemInfo = res;
        resolve(res);
      },
      fail: () => {
        // 提供默认值
        const defaultInfo = {
          statusBarHeight: 20,
          safeAreaInsets: { top: 20, bottom: 0, left: 0, right: 0 },
          windowHeight: 667,
          windowWidth: 375,
        } as UniApp.GetSystemInfoResult;
        systemInfo = defaultInfo;
        resolve(defaultInfo);
      },
    });
  });
}

/**
 * 获取状态栏高度
 */
export async function getStatusBarHeight(): Promise<number> {
  if (!needsSafeAreaAdaptation()) {
    return 0;
  }

  const info = await getSystemInfo();
  return info.statusBarHeight || 20;
}

/**
 * 获取导航栏高度（状态栏 + 标题栏）
 */
export async function getNavigationBarHeight(): Promise<number> {
  if (!needsSafeAreaAdaptation()) {
    return 0;
  }

  const statusBarHeight = await getStatusBarHeight();

  // #ifdef MP-WEIXIN
  // 微信小程序胶囊按钮信息
  try {
    const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
    if (menuButtonInfo && menuButtonInfo.height) {
      // 胶囊按钮高度 + 上下间距
      const navigationBarHeight = (menuButtonInfo.top - statusBarHeight) * 2 + menuButtonInfo.height;
      return statusBarHeight + navigationBarHeight;
    }
  } catch (error) {
    console.warn('获取胶囊按钮信息失败:', error);
  }
  // #endif

  // #ifdef MP-ALIPAY
  // 支付宝小程序默认导航栏高度
  return statusBarHeight + 44;
  // #endif

  // #ifdef MP-BAIDU
  // 百度小程序默认导航栏高度
  return statusBarHeight + 44;
  // #endif

  // #ifdef MP-TOUTIAO
  // 字节跳动小程序默认导航栏高度
  return statusBarHeight + 44;
  // #endif

  // #ifdef H5
  // H5环境不需要额外的导航栏高度
  return 0;
  // #endif

  // 其他平台默认值
  return statusBarHeight + 44;
}

/**
 * 获取安全区域顶部距离
 * @returns 安全区域顶部距离，单位rpx
 */
export async function getSafeAreaTop(): Promise<number> {
  if (!needsSafeAreaAdaptation()) {
    return 0;
  }

  const info = await getSystemInfo();

  // #ifdef MP-WEIXIN
  // 微信小程序使用适中的固定值，避免与胶囊按钮重合
  return 30; // 60rpx 状态栏 + 60rpx 内容间距，总共约68px
  // #endif

  // #ifndef MP-WEIXIN
  // 使用安全区域信息
  // 注意：getSystemInfo() 总是提供默认的 safeAreaInsets 值
  // @ts-ignore: 条件编译导致的 TypeScript 误报，此代码在非微信小程序环境下是可达的
  const safeAreaInsets = info.safeAreaInsets!;
  if (safeAreaInsets.top) {
    return (safeAreaInsets.top + 44) * 2; // 安全区域顶部 + 导航栏高度，转换为rpx
  }

  // 使用状态栏高度作为后备方案
  return ((info.statusBarHeight || 20) + 44) * 2; // 转换为rpx
  // #endif
}

/**
 * 获取安全区域底部距离
 */
export async function getSafeAreaBottom(): Promise<number> {
  const info = await getSystemInfo();

  // 使用安全区域信息
  if (info.safeAreaInsets && info.safeAreaInsets.bottom) {
    return info.safeAreaInsets.bottom;
  }

  // 检查是否有底部安全区域（如iPhone X系列）
  if (info.model && info.model.includes('iPhone')) {
    const screenHeight = info.screenHeight || info.windowHeight;
    const windowHeight = info.windowHeight;
    if (screenHeight && windowHeight && screenHeight - windowHeight > 0) {
      return screenHeight - windowHeight;
    }
  }

  return 0;
}

/**
 * 获取完整的安全区域信息
 */
export async function getSafeAreaInfo() {
  const [statusBarHeight, navigationBarHeight, safeAreaTop, safeAreaBottom] = await Promise.all([
    getStatusBarHeight(),
    getNavigationBarHeight(),
    getSafeAreaTop(),
    getSafeAreaBottom(),
  ]);

  return {
    statusBarHeight,
    navigationBarHeight,
    safeAreaTop,
    safeAreaBottom,
  };
}

/**
 * 生成安全区域样式
 */
export async function getSafeAreaStyle(
  options: {
    includeTop?: boolean;
    includeBottom?: boolean;
    customTop?: number;
    customBottom?: number;
  } = {}
) {
  const { includeTop = true, includeBottom = false, customTop, customBottom } = options;

  const safeAreaInfo = await getSafeAreaInfo();

  const style: Record<string, string> = {};

  if (includeTop) {
    const topValue = customTop !== undefined ? customTop : safeAreaInfo.safeAreaTop;
    style.paddingTop = `${topValue}rpx`;
  }

  if (includeBottom) {
    const bottomValue = customBottom !== undefined ? customBottom : safeAreaInfo.safeAreaBottom;
    style.paddingBottom = `${bottomValue}rpx`;
  }

  return style;
}

/**
 * 检查是否为刘海屏
 */
export async function isNotchScreen(): Promise<boolean> {
  const info = await getSystemInfo();

  // 检查安全区域
  if (info.safeAreaInsets && info.safeAreaInsets.top > 20) {
    return true;
  }

  // 检查状态栏高度
  if (info.statusBarHeight && info.statusBarHeight > 20) {
    return true;
  }

  // 检查设备型号
  if (info.model) {
    const model = info.model.toLowerCase();
    const notchKeywords = ['iphone x', 'iphone 1', 'iphone 2', 'iphone 3', 'iphone 4'];
    return notchKeywords.some((keyword) => model.includes(keyword));
  }

  return false;
}

/**
 * 获取微信小程序胶囊按钮信息
 */
export function getCapsuleButtonInfo() {
  // #ifdef MP-WEIXIN
  try {
    const capsule = uni.getMenuButtonBoundingClientRect();
    return {
      width: capsule.width,
      height: capsule.height,
      top: capsule.top,
      right: capsule.right,
      bottom: capsule.bottom,
      left: capsule.left,
    };
  } catch (error) {
    console.warn('获取胶囊按钮信息失败:', error);
    return null;
  }
  // #endif

  // #ifndef MP-WEIXIN
  // @ts-ignore: 条件编译导致的 TypeScript 误报，此代码在非微信小程序环境下是可达的
  return null;
  // #endif
}

/**
 * 获取导航栏布局信息（专门用于logo和标题与胶囊按钮对齐）
 */
export async function getNavBarLayoutInfo() {
  const systemInfo = await getSystemInfo();
  const capsuleInfo = getCapsuleButtonInfo();

  // #ifdef MP-WEIXIN
  if (capsuleInfo && systemInfo) {
    // 状态栏高度
    const statusBarHeight = systemInfo.statusBarHeight || 20;

    // 胶囊按钮信息
    const capsuleHeight = capsuleInfo.height;
    const capsuleTop = capsuleInfo.top;
    const capsuleBottom = capsuleInfo.bottom;
    const capsuleRight = capsuleInfo.right;
    const capsuleLeft = capsuleInfo.left;

    // 导航栏高度 = 胶囊按钮底部位置 + 胶囊按钮距离顶部的间距
    const navBarHeight = capsuleBottom + (capsuleTop - statusBarHeight);

    // 胶囊按钮垂直居中位置（相对于状态栏底部）
    const capsuleCenterY = capsuleTop + capsuleHeight / 2 - statusBarHeight;

    // 可用于放置logo和标题的区域
    const availableWidth = capsuleLeft - 32; // 左侧留32px边距
    const availableHeight = capsuleHeight;

    return {
      // 基础信息
      statusBarHeight,
      navBarHeight,

      // 胶囊按钮信息
      capsule: {
        width: capsuleInfo.width,
        height: capsuleHeight,
        top: capsuleTop,
        right: capsuleRight,
        bottom: capsuleBottom,
        left: capsuleLeft,
        centerY: capsuleCenterY, // 相对于状态栏底部的垂直居中位置
      },

      // 布局信息
      layout: {
        // 内容区域（logo、标题）的可用宽度
        contentWidth: availableWidth,
        // 内容区域的高度（与胶囊按钮同高）
        contentHeight: availableHeight,
        // 内容区域的垂直居中位置（相对于状态栏底部）
        contentCenterY: capsuleCenterY,
        // 内容区域距离左边的距离
        contentLeft: 16,
        // 内容区域距离顶部的距离（状态栏 + 胶囊按钮顶部位置）
        contentTop: capsuleTop,
      },

      // 样式辅助
      styles: {
        // 导航栏容器样式
        navBar: {
          height: `${navBarHeight * 2}rpx`,
          paddingTop: `${statusBarHeight * 2}rpx`,
        },
        // 内容区域样式（logo、标题容器）
        content: {
          height: `${availableHeight * 2}rpx`,
          lineHeight: `${availableHeight * 2}rpx`,
          marginTop: `${(capsuleTop - statusBarHeight) * 2}rpx`,
          marginLeft: '32rpx',
          marginRight: `${(systemInfo.windowWidth - capsuleLeft + 16) * 2}rpx`,
        },
        // logo样式
        logo: {
          height: `${Math.min(availableHeight - 8, 32) * 2}rpx`, // 最大64rpx，留16rpx上下间距
          verticalAlign: 'middle',
        },
        // 标题样式
        title: {
          lineHeight: `${availableHeight * 2}rpx`,
          verticalAlign: 'middle',
        },
      },
    };
  }
  // #endif

  // 其他平台的默认值
  const statusBarHeight = systemInfo?.statusBarHeight || 20;
  const navBarHeight = 44;

  return {
    statusBarHeight,
    navBarHeight,
    capsule: null,
    layout: {
      contentWidth: (systemInfo?.windowWidth || 375) - 64,
      contentHeight: navBarHeight,
      contentCenterY: navBarHeight / 2,
      contentLeft: 16,
      contentTop: statusBarHeight,
    },
    styles: {
      navBar: {
        height: `${navBarHeight * 2}rpx`,
        paddingTop: `${statusBarHeight * 2}rpx`,
      },
      content: {
        height: `${navBarHeight * 2}rpx`,
        lineHeight: `${navBarHeight * 2}rpx`,
        marginLeft: '32rpx',
        marginRight: '32rpx',
      },
      logo: {
        height: '64rpx',
        verticalAlign: 'middle',
      },
      title: {
        lineHeight: `${navBarHeight * 2}rpx`,
        verticalAlign: 'middle',
      },
    },
  };
}
