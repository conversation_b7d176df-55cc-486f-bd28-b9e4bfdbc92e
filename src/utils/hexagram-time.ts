// 时间卦算法、天干地支、五行等工具
import { Solar } from 'lunar-javascript';
import type { Hexagram } from '@/utils/hexagram';

const tian<PERSON>an = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
const diZhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];

/**
 * 八卦二进制映射
 * 乾(1)=111, 兑(2)=011, 离(3)=101, 震(4)=001, 巽(5)=110, 坎(6)=010, 艮(7)=100, 坤(8)=000
 * 注意：每个八卦的三个爻位都是从下到上排列
 */
const baGuaBin = ['111', '011', '101', '001', '110', '010', '100', '000'];

/**
 * 将上下卦数字转换为六爻二进制
 * @param up 上卦数字 (1-8)
 * @param down 下卦数字 (1-8)
 * @returns 六爻二进制字符串，从下到上排列 (下卦 + 上卦)
 *
 * 说明：
 * 1. 六爻从下到上依次是：初爻、二爻、三爻、四爻、五爻、上爻
 * 2. 下卦(内卦)对应初爻、二爻、三爻
 * 3. 上卦(外卦)对应四爻、五爻、上爻
 * 4. 生成的binary字符串格式："下卦三爻 + 上卦三爻"，与hexagram.ts定义一致
 */
export function getHexagramBinary(up: number, down: number): string {
  if (up < 1 || up > 8 || down < 1 || down > 8) {
    console.warn('上卦或下卦数字超出范围(1-8):', up, down);
    return '111111'; // 默认返回乾卦
  }

  // 获取上下卦对应的三爻二进制值
  const upBin = baGuaBin[up - 1];
  const downBin = baGuaBin[down - 1];

  // 组合为 "下卦+上卦" (downBin + upBin) 形成完整的六爻binary
  return upBin + downBin;
}

// --- 天干地支与四柱 ---

interface GanZhi {
  yearTG: string;
  yearDZ: string;
  monthTG: string;
  monthDZ: string;
  dayTG: string;
  dayDZ: string;
  hourTG: string;
  hourDZ: string;
}

export function getGanZhi(date = new Date()): GanZhi {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();

  const solar = Solar.fromYmd(year, month, day);
  const lunar = solar.getLunar();

  const yearGZ = lunar.getYearInGanZhi();
  const monthGZ = lunar.getMonthInGanZhi();
  const dayGZ = lunar.getDayInGanZhi();

  // 时柱地支
  const hourIndex = Math.floor((hour + 1) / 2) % 12;
  const hourDZ = diZhi[hourIndex];

  // 时柱天干 (五鼠遁元)
  const dayTGIndex = tianGan.indexOf(dayGZ[0]);
  const hourTG = tianGan[(dayTGIndex * 2 + hourIndex) % 10];

  return {
    yearTG: yearGZ[0],
    yearDZ: yearGZ[1],
    monthTG: monthGZ[0],
    monthDZ: monthGZ[1],
    dayTG: dayGZ[0],
    dayDZ: dayGZ[1],
    hourTG,
    hourDZ,
  };
}

// --- 梅花易数时间起卦 ---

export function getTimeHexagram(date = new Date()): { up: number; down: number; yao: number } {
  const n = date.getFullYear();
  const m = date.getMonth() + 1;
  const d = date.getDate();
  const h = date.getHours();

  // 分别计算上卦和下卦，避免它们总是相同
  const yearMonth = n + m;
  const dayHour = d + h;

  // 上卦用年月，下卦用日时，使结果更多样化
  const up = yearMonth % 8 || 8;
  const down = dayHour % 8 || 8;
  const yao = (n + m + d + h) % 6 || 6; // 变爻使用所有数据

  return { up, down, yao };
}

// --- 卦象推算宜忌 ---

const allMatters = [
  '出行',
  '婚嫁',
  '开业',
  '安葬',
  '签约',
  '求财',
  '祭祀',
  '搬家',
  '动土',
  '见贵',
  '求医',
  '修造',
  '纳财',
  '祈福',
  '赴任',
  '考试',
  '交易',
  '栽种',
  '入宅',
  '立约',
  '求嗣',
  '分居',
  '诉讼',
  '会友',
  '解除',
  '纳采',
  '上梁',
];

/**
 * 64卦归属的八宫及其五行
 * 用于确定每个卦象的核心五行属性
 */
const hexagramPalaceWuxing: { [key: number]: string } = {
  // 乾宫 (金)
  1: '金',
  44: '金',
  33: '金',
  12: '金',
  20: '金',
  23: '金',
  35: '金',
  14: '金',
  // 兑宫 (金)
  58: '金',
  47: '金',
  45: '金',
  31: '金',
  39: '金',
  15: '金',
  62: '金',
  54: '金',
  // 离宫 (火)
  30: '火',
  56: '火',
  50: '火',
  64: '火',
  4: '火',
  59: '火',
  6: '火',
  13: '火',
  // 震宫 (木)
  51: '木',
  16: '木',
  40: '木',
  32: '木',
  46: '木',
  48: '木',
  28: '木',
  17: '木',
  // 巽宫 (木)
  57: '木',
  9: '木',
  37: '木',
  42: '木',
  25: '木',
  21: '木',
  27: '木',
  18: '木',
  // 坎宫 (水)
  29: '水',
  60: '水',
  3: '水',
  63: '水',
  49: '水',
  55: '水',
  36: '水',
  7: '水',
  // 艮宫 (土)
  52: '土',
  22: '土',
  26: '土',
  41: '土',
  38: '土',
  10: '土',
  61: '土',
  53: '土',
  // 坤宫 (土)
  2: '土',
  24: '土',
  19: '土',
  11: '土',
  34: '土',
  43: '土',
  5: '土',
  8: '土',
};

/**
 * 五行与事项的宜忌规则
 * 基于五行生克关系制定的通用规则
 */
const wuxingYiJiRules = {
  金: {
    yi: ['签约', '交易', '见贵', '考试', '开业', '纳财', '婚嫁', '赴任', '修造'],
    ji: ['诉讼', '分居', '安葬', '动土', '栽种', '求医'],
  },
  木: {
    yi: ['出行', '栽种', '求嗣', '祈福', '搬家', '开业', '动土', '考试', '交易', '签约', '立约'],
    ji: ['安葬', '诉讼', '分居', '修造', '见贵'],
  },
  水: {
    yi: ['求财', '交易', '出行', '赴任', '考试', '纳财', '搬家', '签约'],
    ji: ['动土', '安葬', '修造', '分居', '婚嫁'],
  },
  火: {
    yi: ['祈福', '婚嫁', '开业', '见贵', '考试', '签约', '交易', '纳财'],
    ji: ['安葬', '诉讼', '求医', '分居', '栽种', '出行'],
  },
  土: {
    yi: ['修造', '动土', '入宅', '安葬', '搬家', '祈福', '栽种', '求嗣'],
    ji: ['出行', '诉讼', '分居', '求财'],
  },
};

const phaseKeywords = [
  { key: '婚', matters: ['婚嫁', '纳采'] },
  { key: '财', matters: ['求财', '纳财', '交易'] },
  { key: '安', matters: ['安葬', '入宅', '修造'] },
  { key: '动', matters: ['动土', '出行', '搬家', '上梁'] },
  { key: '考', matters: ['考试', '赴任'] },
  { key: '讼', matters: ['诉讼', '分居'] },
  { key: '祈', matters: ['祈福', '祭祀'] },
  { key: '修', matters: ['修造', '解除'] },
  { key: '交易', matters: ['交易', '签约', '立约'] },
  { key: '见', matters: ['见贵', '会友'] },
  { key: '官', matters: ['赴任', '见贵'] },
  { key: '医', matters: ['求医'] },
  { key: '嗣', matters: ['求嗣'] },
];

interface YiJiResult {
  yi: string[];
  ji: string[];
}

const allMattersSet = new Set(allMatters);

/**
 * 填充数组到指定数量
 * @param target 目标数组
 * @param count 期望数量
 * @param source 数据源
 * @returns 填充后的数组
 */
const padItems = (target: string[], count: number, source: string[]): string[] => {
  const needed = count - target.length;
  if (needed <= 0) {
    return target.slice(0, count);
  }
  const itemsToAdd = source.slice(0, needed);
  return [...target, ...itemsToAdd];
};

export function getYiJi(hexagram: Pick<Hexagram, 'id' | 'phase'>): YiJiResult {
  if (!hexagram || typeof hexagram.id !== 'number') {
    return { yi: ['祈福', '求财', '开业', '交易'], ji: ['诉讼', '分居', '安葬'] };
  }

  // 1. 获取卦象的宫位五行及基础规则
  const wuxing = hexagramPalaceWuxing[hexagram.id as keyof typeof hexagramPalaceWuxing] || '土';
  const rules = wuxingYiJiRules[wuxing as keyof typeof wuxingYiJiRules] || { yi: [], ji: [] };
  const yiBuilder = new Set(rules.yi);
  const jiBuilder = new Set(rules.ji);

  // 2. 根据卦辞 (phase) 关键词增强宜
  if (hexagram.phase) {
    phaseKeywords.forEach((item) => {
      if (hexagram.phase?.includes(item.key)) {
        item.matters.forEach((matter) => yiBuilder.add(matter));
      }
    });
  }

  // 3. 过滤并去重，确保事项有效且不冲突
  const finalYi = new Set<string>();
  yiBuilder.forEach((item) => {
    if (allMattersSet.has(item)) {
      finalYi.add(item);
    }
  });

  const finalJi = new Set<string>();
  jiBuilder.forEach((item) => {
    if (allMattersSet.has(item) && !finalYi.has(item)) {
      finalJi.add(item);
    }
  });

  // 4. 补全至固定数量
  const yiArray = [...finalYi];
  const jiArray = [...finalJi];
  const remainingMatters = allMatters.filter((m) => !finalYi.has(m) && !finalJi.has(m));

  const paddedYi = padItems(yiArray, 6, remainingMatters);
  // 更新剩余事项池，防止重复
  const remainingForJi = remainingMatters.filter((m) => !paddedYi.includes(m));
  const paddedJi = padItems(jiArray, 6, remainingForJi);

  return { yi: paddedYi, ji: paddedJi };
}
