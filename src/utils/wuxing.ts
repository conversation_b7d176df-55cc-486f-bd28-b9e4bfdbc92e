// 五行相关数据和工具函数

// 五行元素类型定义
export interface WuxingElement {
  icon: string;
  nature: string;
}

// 五行关系类型定义
export interface WuxingRelation {
  id: number;
  from: {
    name: string;
    icon: string;
    class: string;
  };
  to: {
    name: string;
    icon: string;
    class: string;
  };
  description: string;
}

// 五行详细属性
export const wuxingProperties: Record<string, WuxingElement> = {
  木: {
    icon: '🌲',
    nature: '生发向上，条达舒畅',
  },
  火: {
    icon: '🔥',
    nature: '炎热向上，光明温暖',
  },
  土: {
    icon: '🏔️',
    nature: '承载化育，厚德载物',
  },
  金: {
    icon: '🪙',
    nature: '清洁肃杀，收敛内聚',
  },
  水: {
    icon: '💧',
    nature: '滋润向下，寒凉收藏',
  },
};

// 辅助函数：获取五行元素图标
const getWuxingIcon = (name: string): string => {
  return wuxingProperties[name]?.icon || '';
};

// 相生关系数据
export const shengRelations: WuxingRelation[] = [
  {
    id: 1,
    from: { name: '木', icon: getWuxingIcon('木'), class: 'wood' },
    to: { name: '火', icon: getWuxingIcon('火'), class: 'fire' },
    description: '木生火：钻木取火，木料燃烧',
  },
  {
    id: 2,
    from: { name: '火', icon: getWuxingIcon('火'), class: 'fire' },
    to: { name: '土', icon: getWuxingIcon('土'), class: 'earth' },
    description: '火生土：火烧成灰，归于尘土',
  },
  {
    id: 3,
    from: { name: '土', icon: getWuxingIcon('土'), class: 'earth' },
    to: { name: '金', icon: getWuxingIcon('金'), class: 'metal' },
    description: '土生金：土中藏金，矿物生成',
  },
  {
    id: 4,
    from: { name: '金', icon: getWuxingIcon('金'), class: 'metal' },
    to: { name: '水', icon: getWuxingIcon('水'), class: 'water' },
    description: '金生水：金属凝露，收集水分',
  },
  {
    id: 5,
    from: { name: '水', icon: getWuxingIcon('水'), class: 'water' },
    to: { name: '木', icon: getWuxingIcon('木'), class: 'wood' },
    description: '水生木：水润万物，滋养草木',
  },
];

// 相克关系数据
export const keRelations: WuxingRelation[] = [
  {
    id: 1,
    from: { name: '木', icon: getWuxingIcon('木'), class: 'wood' },
    to: { name: '土', icon: getWuxingIcon('土'), class: 'earth' },
    description: '木克土：树根破土，植物耗土',
  },
  {
    id: 2,
    from: { name: '土', icon: getWuxingIcon('土'), class: 'earth' },
    to: { name: '水', icon: getWuxingIcon('水'), class: 'water' },
    description: '土克水：土能吸水，堤坝挡水',
  },
  {
    id: 3,
    from: { name: '水', icon: getWuxingIcon('水'), class: 'water' },
    to: { name: '火', icon: getWuxingIcon('火'), class: 'fire' },
    description: '水克火：水能灭火，寒能胜热',
  },
  {
    id: 4,
    from: { name: '火', icon: getWuxingIcon('火'), class: 'fire' },
    to: { name: '金', icon: getWuxingIcon('金'), class: 'metal' },
    description: '火克金：烈火熔金，高温炼铁',
  },
  {
    id: 5,
    from: { name: '金', icon: getWuxingIcon('金'), class: 'metal' },
    to: { name: '木', icon: getWuxingIcon('木'), class: 'wood' },
    description: '金克木：金刀伐木，斧锯断树',
  },
];

// 工具函数：获取元素数据
export const getElementData = (elementName: string): WuxingElement => {
  return (
    wuxingProperties[elementName] || {
      icon: '',
      nature: '',
    }
  );
};

// 工具函数：获取所有元素名称
export const getAllElementNames = (): string[] => {
  return Object.keys(wuxingProperties);
};

// 工具函数：根据元素名称获取相生关系
export const getShengRelationsByElement = (elementName: string): WuxingRelation[] => {
  return shengRelations.filter((relation) => relation.from.name === elementName || relation.to.name === elementName);
};

// 工具函数：根据元素名称获取相克关系
export const getKeRelationsByElement = (elementName: string): WuxingRelation[] => {
  return keRelations.filter((relation) => relation.from.name === elementName || relation.to.name === elementName);
};

// 工具函数：获取元素的生克关系
export const getElementRelations = (elementName: string) => {
  return {
    sheng: getShengRelationsByElement(elementName),
    ke: getKeRelationsByElement(elementName),
  };
};

// 五行归类数据类型定义
export interface WuxingCategory {
  category: string;
  wood: string;
  fire: string;
  earth: string;
  metal: string;
  water: string;
}

// 五行归类数据
export const wuxingCategories: WuxingCategory[] = [
  // 八卦
  {
    category: '八卦',
    wood: '震巽',
    fire: '离',
    earth: '坤艮',
    metal: '乾兑',
    water: '坎',
  },
  // 天干
  {
    category: '天干',
    wood: '甲乙',
    fire: '丙丁',
    earth: '戊己',
    metal: '庚辛',
    water: '壬癸',
  },
  // 地支
  {
    category: '地支',
    wood: '寅卯',
    fire: '午巳',
    earth: '辰戌丑未',
    metal: '申酉',
    water: '子亥',
  },
  // 四时
  {
    category: '四时',
    wood: '春',
    fire: '夏',
    earth: '长夏/四季',
    metal: '秋',
    water: '冬',
  },
  // 五方
  {
    category: '五方',
    wood: '东',
    fire: '南',
    earth: '中',
    metal: '西',
    water: '北',
  },
  // 天 - 五气
  {
    category: '五气',
    wood: '风',
    fire: '热',
    earth: '湿',
    metal: '燥',
    water: '寒',
  },
  // 五化
  {
    category: '五化',
    wood: '生',
    fire: '长',
    earth: '化',
    metal: '收',
    water: '藏',
  },
  // 五星
  {
    category: '五星',
    wood: '木星',
    fire: '火星',
    earth: '土星',
    metal: '金星',
    water: '水星',
  },
  // 五灵
  {
    category: '五灵',
    wood: '青龙',
    fire: '朱雀',
    earth: '麒麟',
    metal: '白虎',
    water: '玄武',
  },
  // 五时
  {
    category: '五时',
    wood: '旦',
    fire: '日中',
    earth: '日西',
    metal: '日入',
    water: '夜',
  },
  // 五数
  {
    category: '五数',
    wood: '三八',
    fire: '二七',
    earth: '五十',
    metal: '四九',
    water: '一六',
  },
  // 地 - 五音
  {
    category: '五音',
    wood: '角(3)',
    fire: '徵(5)',
    earth: '宫(1)',
    metal: '商(2)',
    water: '羽(6)',
  },
  // 五事
  {
    category: '五事',
    wood: '貌',
    fire: '视',
    earth: '思',
    metal: '言',
    water: '听',
  },
  // 五色
  {
    category: '五色',
    wood: '青',
    fire: '赤',
    earth: '黄',
    metal: '白',
    water: '黑',
  },
  // 五味
  {
    category: '五味',
    wood: '酸',
    fire: '苦',
    earth: '甘',
    metal: '辛',
    water: '咸',
  },
  // 五臭
  {
    category: '五臭',
    wood: '臊',
    fire: '焦',
    earth: '香',
    metal: '腥',
    water: '腐',
  },
  // 五伦
  {
    category: '五伦',
    wood: '父子',
    fire: '长幼',
    earth: '朋友',
    metal: '君臣',
    water: '夫妻',
  },
  // 五常
  {
    category: '五常',
    wood: '仁',
    fire: '礼',
    earth: '信',
    metal: '义',
    water: '智',
  },
  // 人 - 五脏
  {
    category: '五脏',
    wood: '肝',
    fire: '心',
    earth: '脾',
    metal: '肺',
    water: '肾',
  },
  // 五腑
  {
    category: '五腑',
    wood: '胆',
    fire: '小肠',
    earth: '胃',
    metal: '大肠',
    water: '膀胱',
  },
  // 五体
  {
    category: '五体',
    wood: '筋',
    fire: '脉',
    earth: '肉',
    metal: '皮',
    water: '骨',
  },
  // 五华
  {
    category: '五华',
    wood: '爪',
    fire: '面',
    earth: '唇',
    metal: '毛',
    water: '发',
  },
  // 五官
  {
    category: '五官',
    wood: '目',
    fire: '舌',
    earth: '口',
    metal: '鼻',
    water: '耳',
  },
  // 五液
  {
    category: '五液',
    wood: '泪',
    fire: '汗',
    earth: '涎',
    metal: '涕',
    water: '唾',
  },
  // 五声
  {
    category: '五声',
    wood: '呼',
    fire: '笑',
    earth: '歌',
    metal: '哭',
    water: '呻',
  },
  // 五志
  {
    category: '五志',
    wood: '怒',
    fire: '喜',
    earth: '思',
    metal: '悲',
    water: '恐',
  },
  // 五变
  {
    category: '五变',
    wood: '握',
    fire: '忧',
    earth: '哕',
    metal: '咳',
    water: '栗',
  },
  // 五神
  {
    category: '五神',
    wood: '魂',
    fire: '神',
    earth: '意',
    metal: '魄',
    water: '志',
  },
];
