/**
 * 内容解压缩工具
 * 用于解压缩远程加载的HTML内容
 */

// 直接静态导入pako（适用于小程序环境）
import pakoLib from 'pako';

// 声明pako的类型
interface PakoLib {
  gzip: (data: Uint8Array, options?: any) => Uint8Array;
  ungzip: (data: Uint8Array, options?: any) => string | Uint8Array;
}

let pakoInstance: PakoLib | null = null;

console.log(`[html-decompressor] 静态导入pako类型: ${typeof pakoLib}`);

// 初始化静态导入的pako
if (pakoLib) {
  console.log('[html-decompressor] ✅ 静态导入pako成功');
} else {
  console.log('[html-decompressor] ❌ 静态导入pako失败');
}

// 初始化pako库
const initPako = (): PakoLib | null => {
  if (pakoInstance) {
    return pakoInstance;
  }

  try {
    // 方法1: 使用静态导入的pako（优先级最高）
    if (pakoLib && typeof pakoLib.ungzip === 'function') {
      pakoInstance = pakoLib;
      console.log('[html-decompressor] ✅ 使用静态导入的pako');
      return pakoInstance;
    }

    // 方法2: 检查全局变量 (适用于CDN引入)
    if (typeof window !== 'undefined' && (window as any).pako) {
      pakoInstance = (window as any).pako;
      console.log('[html-decompressor] ✅ 使用全局变量加载pako成功');
      return pakoInstance;
    }

    // 方法3: 检查全局对象 (适用于小程序环境)
    if (typeof global !== 'undefined' && (global as any).pako) {
      pakoInstance = (global as any).pako;
      console.log('[html-decompressor] ✅ 使用global变量加载pako成功');
      return pakoInstance;
    }

    console.log('[html-decompressor] ❌ 所有pako加载方式都失败');
    console.log(`[html-decompressor] pakoLib类型: ${typeof pakoLib}, ungzip: ${typeof pakoLib?.ungzip}`);
    return null;
  } catch (error) {
    console.log('[html-decompressor] ❌ pako初始化失败:', error);
    return null;
  }
};

// 获取pako实例
const getPako = (): PakoLib | null => {
  return initPako();
};

// 内部解压缩函数
const ungzip = (data: Uint8Array, options?: any): string | null => {
  const pako = getPako();
  if (!pako || typeof pako.ungzip !== 'function') {
    console.log('[html-decompressor] ❌ pako库不可用');
    return null;
  }

  try {
    const result = pako.ungzip(data, options);
    return typeof result === 'string' ? result : new TextDecoder().decode(result);
  } catch (error) {
    console.log('[html-decompressor] ❌ 解压缩失败:', error);
    return null;
  }
};

// 检查pako是否可用
const isPakoAvailable = (): boolean => {
  const pako = getPako();
  return !!(pako && typeof pako.ungzip === 'function' && typeof pako.gzip === 'function');
};

/**
 * 检查是否是压缩内容
 * @param data - 响应数据
 * @returns 是否是压缩内容
 */
export function isCompressedContent(data: any): boolean {
  console.log(`[html-decompressor] 🔍 检查是否为压缩内容，数据类型: ${typeof data}，长度: ${data?.length || 0}`);

  // 检查是否是Base64编码的字符串
  if (typeof data !== 'string') {
    console.log(`[html-decompressor] ❌ 数据不是字符串类型`);
    return false;
  }

  // Base64字符串的特征：只包含A-Z, a-z, 0-9, +, /, =
  const base64Regex = /^[A-Za-z0-9+/]+=*$/;
  const isBase64 = base64Regex.test(data);
  const isLongEnough = data.length > 100;

  console.log(`[html-decompressor] 📊 检查结果: Base64格式=${isBase64}, 长度足够=${isLongEnough} (${data.length})`);

  // 压缩内容通常比较长，且符合Base64格式
  const result = isBase64 && isLongEnough;
  console.log(`[html-decompressor] ${result ? '✅' : '❌'} 是否为压缩内容: ${result}`);

  return result;
}

/**
 * 解压缩内容
 * @param encodedData - Base64编码的压缩数据
 * @returns 解压后的HTML内容，失败时返回null
 */
export function decompressContent(encodedData: string): string | null {
  console.log(`[html-decompressor] 🗜️ 开始解压缩，编码数据长度: ${encodedData.length}`);
  console.log(`[html-decompressor] 📦 当前pako状态: ${isPakoAvailable() ? '可用' : '不可用'}`);

  try {
    // 1. Base64解码
    const binaryString = atob(encodedData);
    console.log(`[html-decompressor] ✅ Base64解码成功，二进制长度: ${binaryString.length}`);

    const bytes = new Uint8Array(binaryString.length);

    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    console.log(`[html-decompressor] 📦 字节数组创建完成，长度: ${bytes.length}`);

    // 2. 检查pako库是否可用
    if (!isPakoAvailable()) {
      console.log('[html-decompressor] ❌ pako库未正确加载或ungzip方法不可用');
      return null;
    }
    console.log(`[html-decompressor] ✅ pako库已加载`);

    // 3. Gzip解压缩
    console.log(`[html-decompressor] 🗜️ 开始Gzip解压缩...`);
    const decompressed = ungzip(bytes, { to: 'string' });
    if (!decompressed) {
      console.log('[html-decompressor] ❌ 解压缩返回null');
      return null;
    }
    console.log(`[html-decompressor] ✅ 解压缩成功，解压后长度: ${decompressed.length}`);
    console.log(`[html-decompressor] 📄 解压后内容预览: ${decompressed.substring(0, 100)}...`);

    return decompressed;
  } catch (error) {
    const errorMsg = error instanceof Error ? error.message : String(error);
    console.log(
      `[html-decompressor] ❌ 解压缩失败: ${errorMsg}, 数据长度: ${encodedData.length}, 预览: ${encodedData.substring(0, 100)}`
    );
    return null;
  }
}
