/**
 * 通用工具函数
 */

/**
 * 计算字符串的字节大小（兼容小程序）
 */
export function getStringByteSize(str: string): number {
  let size = 0;
  for (let i = 0; i < str.length; i++) {
    const code = str.charCodeAt(i);
    if (code <= 0x7f) {
      size += 1; // ASCII字符
    } else if (code <= 0x7ff) {
      size += 2; // 2字节字符
    } else if (code <= 0xffff) {
      size += 3; // 3字节字符（大部分中文）
    } else {
      size += 4; // 4字节字符
    }
  }
  return size;
}

/**
 * 计算字符串的字节大小（兼容小程序）
 */
export function getStringSize(str: string): string {
  let size = 0;
  for (let i = 0; i < str.length; i++) {
    const code = str.charCodeAt(i);
    if (code <= 0x7f) {
      size += 1; // ASCII字符
    } else if (code <= 0x7ff) {
      size += 2; // 2字节字符
    } else if (code <= 0xffff) {
      size += 3; // 3字节字符（大部分中文）
    } else {
      size += 4; // 4字节字符
    }
  }
  return `${Math.round(size / 1024)}KB`;
}
