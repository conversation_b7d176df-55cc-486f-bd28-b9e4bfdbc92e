/**
 * 卦名配图游戏题目生成器
 */

import { getAllHexagrams } from '@/utils/hexagram';
import type { Hexagram } from '@/utils/hexagram';
import type { GameQuestion, GameOption } from './types';
import { BaseQuestionGenerator } from '@/games/shared/base/base-question-generator';
import type { GameConfig } from '@/games/shared/types';

export class HexagramNameToImageQuestionGenerator extends BaseQuestionGenerator<GameQuestion, GameOption> {
  private allHexagrams: Hexagram[];

  constructor(config: GameConfig) {
    super(config);
    this.allHexagrams = getAllHexagrams();
  }

  /**
   * 实现基类的抽象方法：生成单个题目
   */
  generateQuestion(questionNumber: number): GameQuestion | null {
    // 随机选择一个未使用的卦象
    const availableHexagrams = this.allHexagrams.filter((h) => !this.usedItems.has(h.id));
    if (availableHexagrams.length === 0) {
      return null;
    }

    const targetHexagram = availableHexagrams[Math.floor(Math.random() * availableHexagrams.length)];
    this.usedItems.add(targetHexagram.id);

    // 生成选项
    const options = this.generateImageOptions(targetHexagram);
    const optionCount = this.getOptionCount();
    if (options.length < optionCount) {
      return null;
    }

    return {
      id: `q${questionNumber}`,
      question: '📖 请选择卦名对应的卦象图形',
      correctAnswer: targetHexagram.name,
      options: options.slice(0, optionCount),
    };
  }

  /**
   * 生成卦象图选项
   */
  private generateImageOptions(correctHexagram: Hexagram): GameOption[] {
    const options: GameOption[] = [];

    // 添加正确答案
    options.push({
      id: `option_${correctHexagram.id}`,
      hexagram: correctHexagram,
      isCorrect: true,
    });

    // 添加错误选项
    const otherHexagrams = this.allHexagrams.filter((h) => h.id !== correctHexagram.id && !this.usedItems.has(h.id));

    // 随机选择错误选项
    const shuffledOthers = this.shuffleArray([...otherHexagrams]);
    const wrongOptionsCount = this.getOptionCount() - 1;

    for (let i = 0; i < wrongOptionsCount && i < shuffledOthers.length; i++) {
      const hexagram = shuffledOthers[i];
      options.push({
        id: `option_${hexagram.id}`,
        hexagram: hexagram,
        isCorrect: false,
      });
    }

    // 打乱选项顺序
    return this.shuffleArray(options);
  }
}
