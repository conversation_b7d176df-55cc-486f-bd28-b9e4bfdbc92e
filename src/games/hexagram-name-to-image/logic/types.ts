/**
 * 卦名配图游戏类型定义
 * 基于共享基础类型，定义游戏特定的类型
 */

import type { Hexagram } from '@/utils/hexagram';
import type { BaseGameOption, BaseGameQuestion } from '@/games/shared/types';

// 卦名配图的游戏选项 - 扩展基础选项
export interface GameOption extends BaseGameOption {
  hexagram: Hexagram; // 卦象图形
}

// 卦名配图的游戏题目 - 扩展基础题目
export interface GameQuestion extends BaseGameQuestion<GameOption> {
  // 卦名配图游戏的题目结构与基础题目一致
  // correctAnswer 是卦名字符串
}

// 重新导出共享类型，使游戏逻辑可以从单一来源导入所有类型
// 这样做的好处是：
// 1. 如果共享类型路径变化，只需修改这里
// 2. 游戏逻辑文件可以统一从 './types' 导入所有类型
// 3. 保持类型导入的一致性和简洁性
export type { AnswerResult, GameDifficulty } from '@/games/shared/types';
export type { GameState } from '@/games/shared/types';
