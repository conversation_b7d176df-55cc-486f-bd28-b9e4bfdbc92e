<template>
  <PageWrapper :show-back="true" customTitle="📖 卦名配图">
    <GameController
      :game-logic-class="HexagramNameToImageGameLogic"
      :config="HEXAGRAM_NAME_TO_IMAGE_CONFIG.timer!"
    >
      <!-- 游戏内容插槽 -->
      <template #game-content="{
        question,
        disabled,
        gameActive,
        selectedOptionId,
        isAnswered,
        correctOptionId,
        buttonStates,
        currentQuestionIndex,
        totalQuestions,
        onSelect,
        onConfirm,
        onNext,
        onReset
      }">
        <GameContent
          :question="question"
          :disabled="disabled"
          :game-active="gameActive"
          :selected-option-id="selectedOptionId"
          :is-answered="isAnswered"
          :correct-option-id="correctOptionId"
          :button-states="buttonStates"
          :current-question-index="currentQuestionIndex"
          :total-questions="totalQuestions"
          @select="onSelect"
          @confirm="onConfirm"
          @next="onNext"
          @reset="onReset"
        >
          <!-- 题目内容插槽 -->
          <template #question-content="{ question }">
            <view class="hexagram-name">
              {{ question.correctAnswer }}
            </view>
          </template>

          <!-- 选项内容插槽 -->
          <template #option-content="{ option }">
            <HexagramDisplay
              v-if="option.hexagram"
              :hexagram="option.hexagram"
              :binary="option.hexagram.binary"
              size="medium"
            />
          </template>
        </GameContent>
      </template>
    </GameController>
  </PageWrapper>
</template>

<script setup lang="ts">

  import PageWrapper from '@/components/PageWrapper.vue';
  import HexagramDisplay from '@/components/HexagramDisplay.vue';
  import GameController from '@/games/shared/components/GameController.vue';
  import GameContent from '@/games/shared/components/GameContent.vue';
  import { BaseGameLogic } from '@/games/shared/base/base-game-logic';
  import { HexagramNameToImageQuestionGenerator } from './logic/question-generator';
  import { HEXAGRAM_NAME_TO_IMAGE_CONFIG } from './config';
  import type { GameQuestion, GameOption } from './logic/types';

  // 直接在页面中定义游戏逻辑类
  class HexagramNameToImageGameLogic extends BaseGameLogic<
    GameQuestion,
    GameOption,
    HexagramNameToImageQuestionGenerator
  > {
    constructor() {
      super(
        HEXAGRAM_NAME_TO_IMAGE_CONFIG,
        HexagramNameToImageQuestionGenerator,
        'hexagram-name-to-image'
      );
    }
  }


</script>

<style scoped>

  /* 卦名特殊样式 - 渐变文字效果 */
  .hexagram-name {
    background: linear-gradient(135deg, #8b4513, #d4af37);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 52rpx;
    font-weight: 800;
    letter-spacing: 4rpx;
    margin-top: 16rpx;
    text-shadow: 0 2rpx 4rpx rgba(139, 69, 19, 0.2);
  }

</style>
