/**
 * 游戏注册中心
 * 统一管理所有游戏的注册、配置和路由
 */

import type { GameRegistry, GameCategory } from '@/games/shared/types';
import { HEXAGRAM_IMAGE_TO_NAME_CONFIG } from './hexagram-image-to-name/config';
import { HEXAGRAM_NAME_TO_IMAGE_CONFIG } from './hexagram-name-to-image/config';

// 游戏分类定义
export const GAME_CATEGORIES: GameCategory[] = [
  { id: 'all', name: '全部' },
  { id: 'quiz', name: '问答' },
  { id: 'puzzle', name: '益智' },
  { id: 'hexagram', name: '卦象' },
];

/**
 * 从游戏配置生成注册信息
 */
function createGameFromConfig(config: any, route: string, isNew: boolean = true): GameRegistry {
  return {
    id: config.id!,
    name: config.name!,
    description: config.description!,
    category: config.category!,
    difficulty: config.difficulty!,
    icon: config.icon!,
    route,
    isNew,
  };
}

// 游戏注册表 - 从配置文件自动生成
export const REGISTERED_GAMES: GameRegistry[] = [
  createGameFromConfig(HEXAGRAM_IMAGE_TO_NAME_CONFIG, '/games/hexagram-image-to-name/index', true),
  createGameFromConfig(HEXAGRAM_NAME_TO_IMAGE_CONFIG, '/games/hexagram-name-to-image/index', true),

  // 以下为演示用的占位游戏，实际开发时可以删除
  {
    id: 'coming-soon-1',
    name: '五行相克',
    description: '学习五行相生相克的奥秘',
    category: 'quiz',
    difficulty: 'easy',
    icon: '⚡',
    route: '/pages/game/coming-soon',
    isNew: true,
  },
  {
    id: 'coming-soon-2',
    name: '八卦推演',
    description: '体验古代占卜的智慧',
    category: 'puzzle',
    difficulty: 'hard',
    icon: '🔮',
    route: '/pages/game/coming-soon',
    isNew: true,
  },
];

/**
 * 获取所有游戏
 */
export function getAllGames(): GameRegistry[] {
  return REGISTERED_GAMES;
}

/**
 * 根据分类获取游戏
 */
export function getGamesByCategory(categoryId: string): GameRegistry[] {
  if (categoryId === 'all') {
    return REGISTERED_GAMES;
  }
  return REGISTERED_GAMES.filter((game) => game.category === categoryId);
}

/**
 * 根据ID获取游戏
 */
export function getGameById(gameId: string): GameRegistry | undefined {
  return REGISTERED_GAMES.find((game) => game.id === gameId);
}

/**
 * 注册新游戏
 * @param game 游戏配置
 */
export function registerGame(game: GameRegistry): void {
  const existingIndex = REGISTERED_GAMES.findIndex((g) => g.id === game.id);
  if (existingIndex >= 0) {
    // 更新现有游戏
    REGISTERED_GAMES[existingIndex] = game;
  } else {
    // 添加新游戏
    REGISTERED_GAMES.push(game);
  }
}

/**
 * 注销游戏
 * @param gameId 游戏ID
 */
export function unregisterGame(gameId: string): boolean {
  const index = REGISTERED_GAMES.findIndex((game) => game.id === gameId);
  if (index >= 0) {
    REGISTERED_GAMES.splice(index, 1);
    return true;
  }
  return false;
}

/**
 * 获取游戏统计信息
 */
export function getGameStats() {
  const stats = {
    total: REGISTERED_GAMES.length,
    byCategory: {} as Record<string, number>,
    byDifficulty: {} as Record<string, number>,
  };

  REGISTERED_GAMES.forEach((game) => {
    // 按分类统计
    stats.byCategory[game.category] = (stats.byCategory[game.category] || 0) + 1;
    // 按难度统计
    stats.byDifficulty[game.difficulty] = (stats.byDifficulty[game.difficulty] || 0) + 1;
  });

  return stats;
}
