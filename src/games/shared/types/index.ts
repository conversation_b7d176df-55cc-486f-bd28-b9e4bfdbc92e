/**
 * 游戏类型统一导出
 * 提供所有游戏相关类型的便捷导入入口
 */

// ==================== 游戏核心类型 ====================
// 游戏开发和逻辑相关的类型
export type {
  // 基础类型
  GameDifficulty,
  GameResult,

  // 游戏开发基础类型
  BaseGameOption,
  BaseGameQuestion,
  AnswerResult,
  GameState,

  // 配置类型
  GameTimerConfig,
  GameConfig,
} from './core';

// ==================== 游戏管理类型 ====================
// 游戏注册、展示、统计相关的类型
export type { GameCategory, GameRegistry, GameStats, GlobalStats } from './management';
