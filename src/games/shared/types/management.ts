/**
 * 游戏管理相关类型定义
 * 用于游戏注册、列表展示、统计等管理功能
 */

// 导入游戏核心类型
import type { GameDifficulty } from './core';

// 游戏分类
export interface GameCategory {
  id: string;
  name: string;
}

// 游戏注册信息 - 用于游戏列表和注册管理
export interface GameRegistry {
  id: string;
  name: string;
  description: string;
  category: string;
  difficulty: GameDifficulty;
  icon: string;
  route: string;
  isNew?: boolean;
  bestScore?: number;
  playCount?: number;
}

// 游戏统计
export interface GameStats {
  totalGames: number;
  totalScore: number;
  bestScores: Record<string, number>;
  playCounts: Record<string, number>;
}

// 全网积分统计
export interface GlobalStats {
  totalScore: number;
  level: number;
  experience: number; // 当前等级经验值
  nextLevelExp: number; // 升级所需经验值
  achievements: string[]; // 成就ID列表，具体成就信息请使用 achievement-engine 获取
  gamesPlayed: Record<string, number>;
  bestScores: Record<string, number>;
  totalPlayTime: number; // 总游戏时间（秒）
  averageAccuracy: number;
  longestStreak: number; // 最长连胜
  currentStreak: number; // 当前连胜
}
