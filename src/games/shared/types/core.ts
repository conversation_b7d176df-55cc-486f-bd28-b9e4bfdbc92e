/**
 * 游戏核心类型定义
 * 包含游戏开发和逻辑相关的通用类型
 */

// ==================== 游戏核心类型 ====================

// 游戏难度等级
export type GameDifficulty = 'easy' | 'medium' | 'hard';

// 游戏结果
export interface GameResult {
  gameId: string;
  score: number;
  baseScore: number; // 基础分数（不含加成）
  bonusScore: number; // 奖励分数
  finalScore: number; // 最终积分（含所有加成）
  duration: number;
  accuracy?: number;
  difficulty: GameDifficulty;
  correctAnswers: number;
  totalQuestions: number;
  maxCombo: number; // 最大连击数
  timeBonus: number; // 时间奖励
  accuracyBonus: number; // 准确率奖励
  comboBonus: number; // 连击奖励
  difficultyMultiplier: number; // 难度倍数
  timestamp: number;
  completedAt?: number; // 完成时间戳
  isNewRecord?: boolean; // 是否创造新纪录
  details?: Record<string, unknown>; // 游戏特定的详细信息
  metadata?: Record<string, unknown>; // 元数据
}

// ==================== 通用游戏基础类型 ====================

// 基础游戏选项接口 - 所有游戏选项的基础
export interface BaseGameOption {
  id: string;
  isCorrect: boolean;
}

// 基础游戏题目接口 - 所有游戏题目的基础
export interface BaseGameQuestion<TOption = BaseGameOption> {
  id: string;
  question: string;
  correctAnswer: string;
  options: TOption[];
  timeLimit?: number;
  metadata?: {
    // 题目元数据（可选）
    difficulty?: string;
    strategy?: string;
    generatedAt?: string;
    [key: string]: any;
  };
}

// 答题结果 - 通用的答题结果结构
export interface AnswerResult {
  questionId: string;
  selectedOption: string;
  isCorrect: boolean;
  timeSpent: number;
  score: number;
}

// 游戏状态 - 通用的游戏状态结构
export interface GameState<TQuestion = BaseGameQuestion> {
  currentQuestionIndex: number;
  questions: TQuestion[];
  answers: AnswerResult[];
  score: number;
  timeLeft: number;
  isGameActive: boolean;
  startTime: number;
  endTime?: number; // 游戏结束时间（可选）
}

// ==================== 游戏配置类型 ====================

// 游戏计时器配置 - 统一配置类型，用于所有计时器相关组件和配置文件
export interface GameTimerConfig {
  totalTime: number; // 总时间（秒）
  warningThreshold: number; // 警告阈值（秒）
  dangerThreshold: number; // 危险阈值（秒）
  autoStart?: boolean; // 是否自动开始
  paused?: boolean; // 是否暂停
}

// 完整游戏配置 - 用于游戏注册和管理
export interface GameConfig {
  id?: string;
  name?: string;
  description?: string;
  icon?: string;
  difficulty?: GameDifficulty;
  category?: string;
  timeLimit?: number;
  maxQuestions?: number;
  minScore?: number;
  maxScore?: number;
  scorePerCorrect?: number;
  penaltyPerWrong?: number;
  timer?: GameTimerConfig; // 计时器配置
  gameSpecific?: Record<string, unknown>;
  scoring?: Record<string, unknown>; // 使用通用类型，具体类型由各 service 定义
  achievements?: Record<string, unknown>[]; // 使用通用类型，具体类型由 achievement service 定义
  leaderboard?: Record<string, unknown>; // 使用通用类型，具体类型由 ranking service 定义
  ui?: {
    showProgress: boolean;
    showTimer: boolean;
    showScore: boolean;
    showCombo: boolean;
    theme: string;
  };
}
