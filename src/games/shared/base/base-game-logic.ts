/**
 * 基础游戏逻辑类
 *
 * 提供所有游戏共享的核心功能
 * 只包含真正需要的公用代码
 */

import type { GameState, AnswerResult, BaseGameQuestion, BaseGameOption, GameConfig, GameResult } from '../types';
import { BaseQuestionGenerator } from './base-question-generator';
import { gameService } from '../services/game-service';

/**
 * 基础游戏逻辑类
 */
/**
 * 答题会话状态接口
 */
export interface AnswerSession {
  selectedOptionId: string | null;
  isAnswered: boolean;
  correctOptionId: string | null;
}

/**
 * 游戏控制器接口（简化版）
 */
export interface GameControllerInterface {
  onGameStarted?(state: any): void;
  onGameEnded?(state: any): void;
  onGameStateUpdated?(state: any): void;
  onError?(error: Error): void;
}

export class BaseGameLogic<
  Q extends BaseGameQuestion<O>,
  O extends BaseGameOption,
  QG extends BaseQuestionGenerator<Q, O> = BaseQuestionGenerator<Q, O>,
> {
  protected state: GameState;
  protected config: GameConfig;
  protected questionGenerator: QG;
  protected gameType: string;
  private lastResult: GameResult | null = null;

  /** 控制器接口 */
  private gameController?: GameControllerInterface;

  /** 当前答题会话状态 */
  protected currentSession: AnswerSession = {
    selectedOptionId: null,
    isAnswered: false,
    correctOptionId: null,
  };

  constructor(config: GameConfig, QuestionGeneratorClass: new (config: GameConfig) => QG, gameType: string) {
    this.config = config;
    this.gameType = gameType;
    this.questionGenerator = new QuestionGeneratorClass(config);
    this.state = this.createInitialState();
  }

  /**
   * 创建初始游戏状态
   */
  protected createInitialState(): GameState {
    return {
      currentQuestionIndex: 0,
      questions: [],
      answers: [],
      score: 0,
      timeLeft: this.config.timer?.totalTime || 300,
      isGameActive: false,
      startTime: 0,
    };
  }

  /**
   * 设置游戏配置
   */
  setConfig(config: Partial<GameConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 获取题目数量
   */
  protected getQuestionCount(): number {
    return this.config.maxQuestions || 10;
  }

  /**
   * 生成游戏题目 - 现在直接使用题目生成器
   */
  protected generateQuestions(count: number): Q[] {
    return this.questionGenerator.generateQuestions(count);
  }

  // ==================== 接口设置方法 ====================

  /**
   * 设置控制器接口
   */
  setGameController(controller: GameControllerInterface): void {
    this.gameController = controller;
  }

  // ==================== 生命周期管理 ====================

  /**
   * 初始化游戏
   */
  async initGame(): Promise<GameState> {
    try {
      // 重置状态
      this.state = this.createInitialState();
      this.currentSession = {
        selectedOptionId: null,
        isAnswered: false,
        correctOptionId: null,
      };

      // 通知控制器
      this.gameController?.onGameStateUpdated?.(this.getGameState());

      return this.getGameState();
    } catch (error) {
      this.handleError(error as Error);
      throw error;
    }
  }

  /**
   * 开始游戏
   */
  async startGame(): Promise<GameState> {
    try {
      const questions = this.generateQuestions(this.getQuestionCount());

      this.state = {
        ...this.createInitialState(),
        questions,
        isGameActive: true,
        startTime: Date.now(),
      };

      // 重置答题会话状态
      this.currentSession = {
        selectedOptionId: null,
        isAnswered: false,
        correctOptionId: null,
      };

      // 通知控制器
      this.gameController?.onGameStarted?.(this.getGameState());

      return this.getGameState();
    } catch (error) {
      this.handleError(error as Error);
      throw error;
    }
  }

  /**
   * 获取当前游戏状态的副本
   */
  getGameState(): GameState {
    return { ...this.state };
  }

  /**
   * 获取当前题目
   */
  getCurrentQuestion(): Q | null {
    if (!this.state.isGameActive || this.state.currentQuestionIndex >= this.state.questions.length) {
      return null;
    }
    return this.state.questions[this.state.currentQuestionIndex] as Q;
  }

  /**
   * 回答当前题目
   */
  answerQuestion(optionId: string): AnswerResult {
    if (!this.state.isGameActive) {
      throw new Error('游戏未激活');
    }

    const currentQuestion = this.getCurrentQuestion();
    if (!currentQuestion) {
      throw new Error('没有当前题目');
    }

    // 找到选择的选项
    const selectedOption = currentQuestion.options.find((option) => option.id === optionId);

    if (!selectedOption) {
      throw new Error('无效的选项ID');
    }

    // 计算结果
    const isCorrect = selectedOption.isCorrect;
    const scoreChange = this.calculateScore(isCorrect);

    // 记录答题结果
    const answerResult: AnswerResult = {
      questionId: currentQuestion.id,
      selectedOption: optionId,
      isCorrect,
      score: scoreChange,
      timeSpent: this.calculateTimeSpent(),
    };

    // 更新状态
    this.state.score += scoreChange;
    this.state.answers.push(answerResult);
    this.state.currentQuestionIndex++;

    // 检查游戏是否结束
    if (this.shouldEndGame()) {
      this.state.isGameActive = false;
      this.state.endTime = Date.now();
    }

    return answerResult;
  }

  /**
   * 计算答题得分 - 子类可以覆盖
   */
  protected calculateScore(isCorrect: boolean): number {
    return isCorrect ? 10 : 0;
  }

  /**
   * 计算当前题目已用时间
   */
  protected calculateTimeSpent(): number {
    const currentTime = Date.now();
    const questionStartTime = this.getQuestionStartTime();
    return Math.round((currentTime - questionStartTime) / 1000);
  }

  /**
   * 获取当前题目开始时间
   */
  protected getQuestionStartTime(): number {
    // 简化：每个题目的开始时间就是游戏开始时间
    // 如果需要更精确的时间计算，可以在答题时记录时间戳
    return this.state.startTime;
  }

  /**
   * 结束游戏
   */
  async endGame(): Promise<GameState> {
    try {
      this.state.isGameActive = false;
      this.state.endTime = Date.now();

      // 保存游戏结果
      await this.saveGameResult();

      // 通知控制器
      this.gameController?.onGameEnded?.(this.getGameState());

      return this.getGameState();
    } catch (error) {
      this.handleError(error as Error);
      throw error;
    }
  }

  /**
   * 重启游戏
   */
  async restartGame(): Promise<GameState> {
    try {
      // 重新开始游戏
      return await this.startGame();
    } catch (error) {
      this.handleError(error as Error);
      throw error;
    }
  }

  /**
   * 检查游戏是否应该结束
   */
  protected shouldEndGame(): boolean {
    return this.state.currentQuestionIndex >= this.state.questions.length;
  }

  /**
   * 保存游戏结果
   */
  private async saveGameResult(): Promise<void> {
    if (!this.state.endTime) {
      this.state.endTime = Date.now();
    }

    const correctAnswers = this.state.answers.filter((a) => a.isCorrect).length;
    const totalQuestions = this.state.questions.length;
    const accuracy = totalQuestions > 0 ? (correctAnswers / totalQuestions) * 100 : 0;

    const result: GameResult = {
      gameId: this.gameType,
      score: this.state.score,
      baseScore: this.state.score,
      bonusScore: 0,
      finalScore: this.state.score,
      duration: Math.round((this.state.endTime - this.state.startTime) / 1000),
      accuracy: Math.round(accuracy),
      difficulty: 'medium' as const,
      correctAnswers,
      totalQuestions,
      maxCombo: 0, // TODO: 从游戏状态中获取
      timeBonus: 0,
      accuracyBonus: 0,
      comboBonus: 0,
      difficultyMultiplier: 1,
      timestamp: this.state.endTime,
    };

    this.lastResult = result;

    // 保存结果到服务
    try {
      await gameService.endGame(result);
    } catch (error) {
      console.error('保存游戏结果失败:', error);
    }
  }

  /**
   * 获取最后一次游戏结果
   */
  getLastResult(): GameResult | null {
    return this.lastResult;
  }

  // ==================== 内部辅助方法 ====================

  /**
   * 统一错误处理
   */
  private handleError(error: Error): void {
    console.error('游戏逻辑错误:', error);
    this.gameController?.onError?.(error);
  }

  // ==================== 答题会话管理 ====================

  /**
   * 选择选项
   */
  selectOption(optionId: string): void {
    // 验证游戏状态
    if (!this.state.isGameActive) {
      throw new Error('游戏未激活');
    }

    if (this.currentSession.isAnswered) {
      throw new Error('已经回答过此题');
    }

    const currentQuestion = this.getCurrentQuestion();
    if (!currentQuestion) {
      throw new Error('没有当前题目');
    }

    // 验证选项是否存在
    const optionExists = currentQuestion.options.some((opt) => opt.id === optionId);
    if (!optionExists) {
      throw new Error(`无效的选项ID: ${optionId}`);
    }

    // 设置选中状态
    this.currentSession.selectedOptionId = optionId;
  }

  /**
   * 确认答题
   */
  confirmAnswer(): AnswerResult {
    // 验证状态
    if (!this.state.isGameActive) {
      throw new Error('游戏未激活');
    }

    if (!this.currentSession.selectedOptionId) {
      throw new Error('未选择任何选项');
    }

    if (this.currentSession.isAnswered) {
      throw new Error('已经回答过此题');
    }

    // 处理答题
    const result = this.answerQuestion(this.currentSession.selectedOptionId);

    // 更新会话状态
    this.currentSession.isAnswered = true;

    // 找到正确答案的选项ID
    const currentQuestion = this.getCurrentQuestion();
    if (currentQuestion) {
      const correctOption = currentQuestion.options.find((opt) => opt.isCorrect);
      this.currentSession.correctOptionId = correctOption?.id || null;
    }

    return result;
  }

  /**
   * 重置当前题目
   */
  resetCurrentQuestion(): void {
    if (this.currentSession.isAnswered) {
      throw new Error('已经回答过此题，无法重置');
    }

    this.currentSession.selectedOptionId = null;
    this.currentSession.correctOptionId = null;
  }

  /**
   * 进入下一题
   */
  moveToNextQuestion(): void {
    if (!this.currentSession.isAnswered) {
      throw new Error('尚未回答当前题目');
    }

    // 重置会话状态
    this.currentSession = {
      selectedOptionId: null,
      isAnswered: false,
      correctOptionId: null,
    };

    // 检查游戏是否结束
    if (this.shouldEndGame()) {
      this.state.isGameActive = false;
      this.state.endTime = Date.now();
    }
  }

  /**
   * 获取当前答题会话状态
   */
  getCurrentSession(): AnswerSession {
    return { ...this.currentSession };
  }

  /**
   * 获取按钮控制状态
   */
  getControlStates(): {
    canReset: boolean;
    canConfirm: boolean;
    canNext: boolean;
  } {
    return {
      canReset: this.state.isGameActive && !!this.currentSession.selectedOptionId && !this.currentSession.isAnswered,
      canConfirm: this.state.isGameActive && !!this.currentSession.selectedOptionId && !this.currentSession.isAnswered,
      canNext: this.state.isGameActive && this.currentSession.isAnswered,
    };
  }
}
