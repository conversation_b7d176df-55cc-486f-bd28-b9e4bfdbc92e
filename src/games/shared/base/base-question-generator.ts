/**
 * 基础题目生成器
 * 
 * 只包含真正共用的基础功能
 */

import type { BaseGameQuestion, BaseGameOption, GameConfig } from '../types';

/**
 * 基础题目生成器类
 */
export abstract class BaseQuestionGenerator<
  Q extends BaseGameQuestion<O>,
  O extends BaseGameOption
> {
  protected gameConfig: GameConfig;
  protected usedItems: Set<string | number> = new Set();
  
  constructor(config: GameConfig) {
    this.gameConfig = config;
  }
  
  /**
   * 获取选项数量
   */
  protected getOptionCount(): number {
    return (this.gameConfig.gameSpecific?.optionCount as number) || 4;
  }
  
  /**
   * 生成题目 - 子类必须实现
   */
  abstract generateQuestion(index: number): Q | null;
  
  /**
   * 批量生成题目
   */
  generateQuestions(count: number): Q[] {
    const questions: Q[] = [];
    for (let i = 1; i <= count; i++) {
      const question = this.generateQuestion(i);
      if (question) {
        questions.push(question);
      }
    }
    return questions;
  }
  
  /**
   * 随机打乱数组
   */
  protected shuffleArray<T>(array: T[]): T[] {
    const result = [...array];
    for (let i = result.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [result[i], result[j]] = [result[j], result[i]];
    }
    return result;
  }
  
  /**
   * 生成唯一ID
   */
  protected generateId(prefix: string = 'item'): string {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * 检查项目是否已使用
   */
  protected isItemUsed(itemId: string | number): boolean {
    return this.usedItems.has(itemId);
  }
  
  /**
   * 标记项目为已使用
   */
  protected markItemAsUsed(itemId: string | number): void {
    this.usedItems.add(itemId);
  }
}
