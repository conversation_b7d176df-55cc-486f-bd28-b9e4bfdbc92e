<template>
  <view class="game-progress">
    <view class="progress-text">
      {{ currentProgress }}/{{ totalProgress }}
    </view>
    <view class="progress-bar">
      <view 
        class="progress-fill" 
        :style="{ width: progressPercentage + '%' }"
      ></view>
    </view>
  </view>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import type { GameState } from '@/games/shared/types';

  interface Props {
    // 可以直接传入游戏状态，自动计算进度
    gameState?: Pick<GameState, 'currentQuestionIndex' | 'questions'> | null;
    // 或者手动指定进度（向后兼容）
    current?: number;
    total?: number;
  }

  const props = withDefaults(defineProps<Props>(), {
    gameState: null,
    current: 0,
    total: 10,
  });

  // 计算当前进度
  const currentProgress = computed(() => {
    if (props.gameState) {
      return Math.min(props.gameState.currentQuestionIndex + 1, props.gameState.questions.length);
    }
    return props.current || 0;
  });

  // 计算总进度
  const totalProgress = computed(() => {
    if (props.gameState) {
      return props.gameState.questions.length;
    }
    return props.total || 10;
  });

  // 计算进度百分比
  const progressPercentage = computed(() => {
    if (totalProgress.value === 0) return 0;
    return Math.min((currentProgress.value / totalProgress.value) * 100, 100);
  });
</script>

<style lang="scss" scoped>
  .game-progress {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 16rpx 24rpx;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 244, 233, 0.95) 100%);
    border-radius: 24rpx;
    box-shadow: 0 8rpx 24rpx rgba(139, 69, 19, 0.15);
    border: 2rpx solid rgba(139, 69, 19, 0.2);
    width: 220rpx; /* 适配小程序屏幕宽度 */
    height: 88rpx; /* 增加高度以容纳进度条 */
    gap: 12rpx;
  }

  .progress-text {
    font-size: 28rpx;
    font-weight: 600;
    color: #8b4513;
    line-height: 1;
    white-space: nowrap;
  }

  .progress-bar {
    width: 100%;
    height: 12rpx;
    background: rgba(139, 69, 19, 0.2);
    border-radius: 6rpx;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #8b4513, #d4af37);
    border-radius: 6rpx;
    transition: width 0.3s ease;
  }
</style>
