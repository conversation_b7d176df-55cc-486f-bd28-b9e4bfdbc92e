<template>
  <view class="game-timer" :class="{ warning: isWarning, danger: isDanger }">
    <view class="timer-emoji">
      {{ timerEmoji }}
    </view>
    <view class="timer-text">
      {{ formatTime(timeLeft) }}
    </view>
  </view>
</template>

<script setup lang="ts">
  import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
  import type { GameTimerConfig } from '@/games/shared/types';

  interface Props {
    // 可以直接传入游戏配置，自动获取时间设置
    gameConfig?: GameTimerConfig | null;
    // 或者手动指定时间配置（向后兼容）
    totalTime?: number; // 总时间（秒）
    warningThreshold?: number; // 警告阈值（秒）
    dangerThreshold?: number; // 危险阈值（秒）
    autoStart?: boolean; // 是否自动开始
    paused?: boolean; // 是否暂停
    // 游戏集成配置
    gameLogic?: any; // 游戏逻辑实例，用于同步时间
    autoEndOnTimeUp?: boolean; // 时间到时是否自动触发游戏结束
  }

  interface Emits {
    (e: 'timeUpdate', timeLeft: number): void;
    (e: 'timeUp'): void;
    (e: 'warning', timeLeft: number): void;
    (e: 'danger', timeLeft: number): void;
    (e: 'gameEnd'): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    gameConfig: null,
    totalTime: 60,
    warningThreshold: 20,
    dangerThreshold: 10,
    autoStart: true,
    paused: false,
    autoEndOnTimeUp: true,
  });

  const emit = defineEmits<Emits>();

  // 计算当前配置
  const currentTotalTime = computed(() => {
    if (props.gameConfig) {
      return props.gameConfig.totalTime;
    }
    return props.totalTime || 60;
  });

  const currentWarningThreshold = computed(() => {
    if (props.gameConfig) {
      return props.gameConfig.warningThreshold;
    }
    return props.warningThreshold || 20;
  });

  const currentDangerThreshold = computed(() => {
    if (props.gameConfig) {
      return props.gameConfig.dangerThreshold;
    }
    return props.dangerThreshold || 10;
  });

  // 内部状态
  const timeLeft = ref(currentTotalTime.value);
  const isRunning = ref(false);
  const hasWarned = ref(false);
  const hasDangered = ref(false);
  let timer: number | null = null;

  // 是否处于警告状态
  const isWarning = computed(() => {
    return timeLeft.value <= currentWarningThreshold.value && timeLeft.value > currentDangerThreshold.value;
  });

  // 是否处于危险状态
  const isDanger = computed(() => {
    return timeLeft.value <= currentDangerThreshold.value;
  });

  // 计时器 emoji 图标
  const timerEmoji = computed(() => {
    if (isDanger.value) return '⏰'; // 危险状态：闹钟
    if (isWarning.value) return '⏳'; // 警告状态：沙漏
    return '⏱️'; // 正常状态：秒表
  });

  // 格式化时间显示
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // 启动计时器
  const start = () => {
    if (isRunning.value) return;

    isRunning.value = true;
    timer = setInterval(() => {
      if (timeLeft.value > 0) {
        timeLeft.value--;

        // 同步时间到游戏逻辑
        syncTimeToGameLogic();

        emit('timeUpdate', timeLeft.value);

        // 检查警告状态
        if (isWarning.value && !hasWarned.value) {
          hasWarned.value = true;
          emit('warning', timeLeft.value);
        }

        // 检查危险状态
        if (isDanger.value && !hasDangered.value) {
          hasDangered.value = true;
          emit('danger', timeLeft.value);
        }

        // 检查时间到
        if (timeLeft.value === 0) {
          handleTimeUp();
        }
      }
    }, 1000) as unknown as number;
  };

  // 停止计时器
  const stop = () => {
    if (timer) {
      clearInterval(timer);
      timer = null;
    }
    isRunning.value = false;
  };

  // 暂停计时器
  const pause = () => {
    stop();
  };

  // 恢复计时器
  const resume = () => {
    if (timeLeft.value > 0) {
      start();
    }
  };

  // 重置计时器
  const reset = () => {
    stop();
    timeLeft.value = currentTotalTime.value;
    hasWarned.value = false;
    hasDangered.value = false;
    syncTimeToGameLogic();
    emit('timeUpdate', timeLeft.value);
  };

  // 设置时间
  const setTime = (seconds: number) => {
    timeLeft.value = Math.max(0, seconds);
    syncTimeToGameLogic();
    emit('timeUpdate', timeLeft.value);
  };

  // 新增：游戏集成方法
  const syncTimeToGameLogic = () => {
    if (props.gameLogic && props.gameLogic.getGameState) {
      props.gameLogic.getGameState().timeLeft = timeLeft.value;
    }
  };

  const handleTimeUp = () => {
    stop();
    emit('timeUp');

    // 如果启用了自动游戏结束，触发游戏结束事件
    if (props.autoEndOnTimeUp) {
      emit('gameEnd');
    }
  };

  // 新增：游戏生命周期管理方法
  const startGame = () => {
    reset();
    start();
  };

  const endGame = () => {
    stop();
  };

  // 监听暂停状态
  watch(
    () => props.paused,
    (newPaused) => {
      if (newPaused) {
        pause();
      } else {
        resume();
      }
    }
  );

  // 监听配置变化，自动重置时间
  watch(
    () => currentTotalTime.value,
    (newTotalTime) => {
      if (!isRunning.value) {
        timeLeft.value = newTotalTime;
        syncTimeToGameLogic();
        emit('timeUpdate', timeLeft.value);
      }
    }
  );

  // 暴露方法给父组件
  defineExpose({
    // 基础计时器方法
    start,
    stop,
    pause,
    resume,
    reset,
    setTime,

    // 游戏集成方法
    startGame,
    endGame,

    // 状态访问
    timeLeft: computed(() => timeLeft.value),
    isRunning: computed(() => isRunning.value),
  });

  // 生命周期
  onMounted(() => {
    if (props.autoStart) {
      start();
    }
  });

  onUnmounted(() => {
    stop();
  });
</script>

<style lang="scss" scoped>
  .game-timer {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16rpx;
    padding: 16rpx 24rpx;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 244, 233, 0.95) 100%);
    border-radius: 24rpx;
    box-shadow: 0 8rpx 24rpx rgba(139, 69, 19, 0.15);
    border: 2rpx solid rgba(139, 69, 19, 0.2);
    transition: all 0.3s ease;
    width: 210rpx; /* 适配小程序屏幕宽度 */
    height: 88rpx; /* 与其他组件保持一致 */

    &.warning {
      background: linear-gradient(135deg, rgba(255, 152, 0, 0.15) 0%, rgba(255, 193, 7, 0.1) 100%);
      border-color: #ff9800;
      box-shadow: 0 8rpx 32rpx rgba(255, 152, 0, 0.3);

      .timer-text {
        color: #ff9800;
      }

      .timer-emoji {
        animation: shake 0.5s ease-in-out infinite alternate;
      }
    }

    &.danger {
      background: linear-gradient(135deg, rgba(244, 67, 54, 0.2) 0%, rgba(255, 87, 34, 0.15) 100%);
      border-color: #f44336;
      box-shadow: 0 8rpx 40rpx rgba(244, 67, 54, 0.4);
      animation: pulse 1s infinite;

      .timer-text {
        color: #f44336;
        font-weight: bold;
      }

      .timer-emoji {
        animation: bounce 0.6s ease-in-out infinite alternate;
      }
    }
  }

  .timer-emoji {
    font-size: 40rpx;
    line-height: 1;
    transition: all 0.3s ease;
  }

  .timer-text {
    font-size: 32rpx;
    font-weight: bold;
    color: #8b4513;
    font-family: 'Courier New', monospace;
    line-height: 1;
  }

  @keyframes pulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes shake {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(4rpx);
    }
  }

  @keyframes bounce {
    0% {
      transform: translateY(0);
    }
    100% {
      transform: translateY(-6rpx);
    }
  }
</style>
