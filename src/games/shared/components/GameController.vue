<template>
  <view class="game-controller">
    <!-- 顶部状态栏 -->
    <view class="game-status-bar">
      <GameScore :score="gameStats.score" :combo="combo" />
      
      <GameProgress
        :current="Math.min(gameState.currentQuestionIndex + 1, gameState.questions.length)"
        :total="gameState.questions.length"
      />
      
      <GameTimer
        ref="gameTimer"
        :game-config="config"
        :game-logic="gameLogic"
        :auto-end-on-time-up="true"
        :paused="!gameState.isGameActive"
        @time-up="handleTimeUp"
      />
    </view>

    <!-- 游戏内容 -->
    <view v-if="currentQuestion">
      <!-- 使用统一答题区域组件 -->
      <slot
        name="game-content"
        :question="currentQuestion"
        :disabled="isGameLocked"
        :game-active="gameState.isGameActive"
        :selected-option-id="selectedOptionId"
        :is-answered="isAnswered"
        :correct-option-id="correctOptionId"
        :button-states="buttonStates"
        :current-question-index="gameState.currentQuestionIndex"
        :total-questions="gameState.questions.length"
        :on-select="handleSelect"
        :on-confirm="handleConfirm"
        :on-next="handleNext"
        :on-reset="handleReset"
      ></slot>
    </view>

    <!-- 游戏结果弹窗 -->
    <GameResult
      :visible="showGameResult"
      :game-result="finalResult"
      :game-service-result="gameServiceResult"
      :show-score="true"
      :show-stats="true"
      :show-score-breakdown="true"
      :new-achievements="[]"
      :allow-backdrop-close="false"
      @action="handleGameResultAction"
    />
  </view>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, onUnmounted } from 'vue';
  import GameTimer from './GameTimer.vue';
  import GameScore from './GameScore.vue';
  import GameProgress from './GameProgress.vue';
  import GameResult from './GameResult.vue';

  import type {
    GameResult as GameResultType,
    GameState,
    GameTimerConfig
  } from '@/games/shared/types';
  import type { GameServiceResult } from '@/games/shared/services/game-service';

  interface Props {
    gameLogicClass: any; // 游戏逻辑类
    config: GameTimerConfig;
  }

  const props = defineProps<Props>();

  // 游戏逻辑实例
  const gameLogic = ref<any>(null);

  // 游戏状态
  const gameState = ref<GameState>({
    currentQuestionIndex: 0,
    questions: [],
    answers: [],
    score: 0,
    timeLeft: 60,
    isGameActive: false,
    startTime: 0,
  });

  // 当前题目
  const currentQuestion = ref<any>(null);

  // 游戏结果
  const finalResult = ref<GameResultType | null>(null);
  const gameServiceResult = ref<GameServiceResult | null>(null);
  const showGameResult = ref(false);

  // 其他状态
  const combo = ref(0);
  const isGameLocked = ref(false);

  // GameTimer 组件引用
  const gameTimer = ref<{
    startGame: () => void;
    endGame: () => void;
  } | null>(null);

  // 计算属性
  const gameStats = computed(() => ({
    score: gameState.value.score,
    accuracy:
      gameState.value.answers.length > 0
        ? Math.round((gameState.value.answers.filter((a: any) => a.isCorrect).length / gameState.value.answers.length) * 100)
        : 0,
  }));

  // 从 game-logic 同步状态
  const currentSession = computed(() => gameLogic.value?.getCurrentSession() || {
    selectedOptionId: null,
    isAnswered: false,
    correctOptionId: null,
  });

  const selectedOptionId = computed(() => currentSession.value.selectedOptionId || '');
  const isAnswered = computed(() => currentSession.value.isAnswered);
  const correctOptionId = computed(() => currentSession.value.correctOptionId || '');

  // 从 game-logic 获取按钮状态
  const buttonStates = computed(() => {
    if (!gameLogic.value || isGameLocked.value) {
      return {
        canReset: false,
        canConfirm: false,
        canNext: false,
      };
    }
    return gameLogic.value.getControlStates();
  });







  // 处理选择选项
  function handleSelect(optionId: string) {
    if (!gameLogic.value || isGameLocked.value) {
      return;
    }

    try {
      gameLogic.value.selectOption(optionId);
    } catch (error) {
      console.warn('选择选项失败:', error);
    }
  }

  // 处理确认答题
  function handleConfirm() {
    if (!gameLogic.value || isGameLocked.value) {
      return;
    }

    try {
      // 临时锁定游戏，防止重复操作
      isGameLocked.value = true;

      const answerResult = gameLogic.value.confirmAnswer();

      // 更新连击数
      if (answerResult.isCorrect) {
        combo.value++;
      } else {
        combo.value = 0;
      }

      // 同步游戏状态
      gameState.value = gameLogic.value.getGameState();

      // 答题完成后立即解锁，允许点击下一题按钮
      isGameLocked.value = false;

    } catch (error) {
      console.error('答题失败:', error);

      // 恢复状态
      isGameLocked.value = false;
    }
  }



  // 处理下一题
  async function handleNext() {
    if (!gameLogic.value) {
      return;
    }

    try {
      // 检查是否是最后一题
      const isLastQuestion = gameState.value.currentQuestionIndex >= gameState.value.questions.length - 1;

      if (isLastQuestion && isAnswered.value) {
        // 最后一题已答完，直接结束游戏
        await gameLogic.value.endGame();
      } else {
        // 不是最后一题，进入下一题
        gameLogic.value.moveToNextQuestion();

        // 解锁游戏
        isGameLocked.value = false;

        // 同步游戏状态
        gameState.value = gameLogic.value.getGameState();
        currentQuestion.value = gameLogic.value.getCurrentQuestion();
      }
    } catch (error) {
      console.error('处理下一题失败:', error);

      // 确保游戏解锁
      isGameLocked.value = false;
    }
  }

  // 处理重置
  function handleReset() {
    if (!gameLogic.value || isGameLocked.value) {
      return;
    }

    try {
      gameLogic.value.resetCurrentQuestion();
    } catch (error) {
      console.warn('重置失败:', error);
    }
  }





  // UI 事件处理 - 重启游戏
  async function handleRestart() {
    if (!gameLogic.value) return;

    try {
      showGameResult.value = false;
      finalResult.value = null;
      combo.value = 0;

      // 直接调用 BaseGameLogic 的重启方法
      await gameLogic.value.restartGame();
    } catch (error) {
      console.error('重启游戏失败:', error);
    }
  }

  // UI 事件处理 - 返回游戏大厅
  async function handleBack() {
    if (gameLogic.value) {
      try {
        // BaseGameLogic 会自动处理所有清理工作
        await gameLogic.value.endGame();
      } catch (error) {
        console.warn('结束游戏时出错:', error);
      }
    }

    // 直接返回游戏大厅
    uni.switchTab({
      url: '/pages/game/index',
    });
  }

  // 处理时间结束（计时器事件转发）
  async function handleTimeUp() {
    if (gameLogic.value) {
      try {
        // 时间结束时，直接调用 BaseGameLogic 的结束方法
        await gameLogic.value.endGame();
      } catch (error) {
        console.error('时间结束处理失败:', error);
      }
    }
  }

  // 处理游戏结果操作（UI 事件转发）
  async function handleGameResultAction(actionKey: string) {
    switch (actionKey) {
      case 'restart':
        await handleRestart();
        break;
      case 'back':
        await handleBack();
        break;
    }
  }



  // 暴露方法给父组件（只暴露 UI 事件处理方法）
  defineExpose({
    handleRestart,
    handleBack,
  });

  // Vue 组件生命周期 - 直接调用 BaseGameLogic
  onMounted(async () => {
    try {
      console.log('组件挂载，开始游戏...');

      // 创建游戏逻辑实例
      gameLogic.value = new props.gameLogicClass();

      // 设置控制器接口
      gameLogic.value.setGameController({
        onGameStarted: (state: any) => {
          gameState.value = state;
          currentQuestion.value = gameLogic.value?.getCurrentQuestion() || null;
          console.log('游戏开始', state);
        },
        onGameEnded: (state: any) => {
          gameState.value = state;
          // 显示游戏结果
          const gameResult = gameLogic.value?.getLastResult();
          if (gameResult) {
            finalResult.value = gameResult;
            showGameResult.value = true;
          }
          console.log('游戏结束', state);
        },
        onGameStateUpdated: (state: any) => {
          gameState.value = state;
          currentQuestion.value = gameLogic.value?.getCurrentQuestion() || null;
        },
        onError: (error: Error) => {
          console.error('游戏错误:', error);
        }
      });

      // 直接调用 BaseGameLogic 的生命周期方法
      await gameLogic.value.initGame();
      await gameLogic.value.startGame();

      console.log('游戏启动完成');
    } catch (error) {
      console.error('游戏启动失败:', error);
    }
  });

  onUnmounted(async () => {
    try {
      console.log('组件卸载，结束游戏...');

      if (gameLogic.value) {
        // 直接调用 BaseGameLogic 的结束方法
        await gameLogic.value.endGame();
      }
    } catch (error) {
      console.warn('组件卸载时结束游戏出错:', error);
    }
  });
</script>

<style scoped>
/* 浮动状态栏样式 */
.game-status-bar {
  position: sticky;
  top: 36rpx;
  z-index: 100;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 30rpx;
  /* padding: 40rpx 20rpx; */
}

/* 游戏控制器基础样式 */
.game-controller {
  min-height: 100vh;
  background: #f8f4e9;
  padding: 36rpx;
}

</style>
