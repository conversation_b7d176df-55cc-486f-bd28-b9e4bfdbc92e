<template>
  <view class="game-content-wrapper">
    <!-- 题目展示区 - 保持原有样式 -->
    <view class="question-wrapper">
      <view class="question-title-container">
        <text class="question-title">{{ question.question }}</text>
      </view>
      <view class="question-content-container">
        <slot name="question-content" :question="question"></slot>
      </view>
    </view>

    <!-- 选项区域 - 保持原有样式 -->
    <view class="options-wrapper">
      <view class="options-title-container">
        <view class="options-title-with-emoji">
          <text class="options-title">🎯 请作答</text>
          <!-- 答题结果表情反馈 -->
          <view v-if="isAnswered" class="answer-emoji">
            <text class="emoji-text">{{ resultType === 'correct' ? '😊' : '😢' }}</text>
          </view>
        </view>
      </view>
      
      <view class="options-grid">
        <button
          v-for="(option, index) in question.options"
          :key="option.id"
          class="game-option-btn"
          :class="[
            `option-${index + 1}`,
            {
              'selected': selectedOptionId === option.id,
              'disabled': isAnswered || disabled
            }
          ]"
          :disabled="isAnswered || disabled"
          @tap="selectOption(option.id)"
        >
          <view class="option-content">
            <slot name="option-content" :option="option" :index="index">
              <text class="option-text">{{ option.text }}</text>
            </slot>
          </view>
          
          <!-- 结果图标 -->
          <view v-if="isAnswered" class="result-icon">
            <text v-if="option.isCorrect" class="icon correct">✓</text>
            <text v-else-if="selectedOptionId === option.id && !option.isCorrect" class="icon incorrect">✗</text>
          </view>

          <!-- 光晕效果 -->
          <view class="option-glow"></view>
        </button>
      </view>
    </view>

    <!-- 答题控制区域 -->
    <view class="answer-control-wrapper">
      <view class="answer-control-buttons">
        <!-- 重置按钮 -->
        <button 
          class="answer-control-btn reset-btn"
          :class="{ 'disabled': !canReset }"
          :disabled="!canReset"
          @tap="handleReset"
        >
          <text class="btn-text">重置</text>
        </button>

        <!-- 确定按钮 -->
        <button 
          class="answer-control-btn confirm-btn"
          :class="{ 'disabled': !canConfirm }"
          :disabled="!canConfirm"
          @tap="handleConfirm"
        >
          <text class="btn-text">确定</text>
        </button>

        <!-- 下一题/结束按钮 -->
        <button
          class="answer-control-btn next-btn"
          :class="{ 'disabled': !canNext }"
          :disabled="!canNext"
          @tap="handleNext"
        >
          <text class="btn-text">{{ nextButtonText }}</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';

  interface GameOption {
    id: string;
    text: string;
    isCorrect: boolean;
    [key: string]: any;
  }

  interface GameQuestion {
    id: string;
    question: string;
    options: GameOption[];
    [key: string]: any;
  }

  interface Props {
    question: GameQuestion;
    disabled?: boolean;
    gameActive?: boolean;
    // 新增：外部传入的答题状态
    selectedOptionId?: string;
    isAnswered?: boolean;
    correctOptionId?: string;
    // 新增：按钮状态控制
    buttonStates?: {
      canReset: boolean;
      canConfirm: boolean;
      canNext: boolean;
    };
    // 新增：游戏状态信息
    currentQuestionIndex?: number;
    totalQuestions?: number;

  }

  interface Emits {
    (e: 'select', optionId: string): void;  // 简化：只发送选择事件
    (e: 'confirm'): void;                   // 简化：只发送确认事件
    (e: 'next'): void;
    (e: 'reset'): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    disabled: false,
    gameActive: true,
    selectedOptionId: '',
    isAnswered: false,
    correctOptionId: '',
    buttonStates: () => ({
      canReset: false,
      canConfirm: false,
      canNext: false,
    }),
    currentQuestionIndex: 0,
    totalQuestions: 10,
  });

  const emit = defineEmits<Emits>();

  // 计算属性 - 使用外部传入的状态
  const resultType = computed(() => {
    if (!props.isAnswered) return '';

    // 如果已回答，根据选择的选项是否正确来确定结果类型
    const selectedOption = props.question.options.find(opt => opt.id === props.selectedOptionId);
    if (!selectedOption) return '';

    return selectedOption.isCorrect ? 'correct' : 'incorrect';
  });

  // 使用外部传入的按钮状态
  const canReset = computed(() => props.buttonStates?.canReset ?? false);
  const canConfirm = computed(() => props.buttonStates?.canConfirm ?? false);
  const canNext = computed(() => props.buttonStates?.canNext ?? false);

  // 动态按钮文本
  const nextButtonText = computed(() => {
    // 如果已答题且是最后一题，显示"结束"
    if (props.isAnswered && props.currentQuestionIndex >= props.totalQuestions - 1) {
      return '结束';
    }
    return '下一题';
  });

  // 方法 - 简化为纯事件发送
  function selectOption(optionId: string) {
    if (props.isAnswered || props.disabled || !props.gameActive) return;
    emit('select', optionId);
  }

  function handleReset() {
    if (!canReset.value) return;
    emit('reset');
  }

  function handleConfirm() {
    if (!canConfirm.value) return;
    emit('confirm');
  }

  function handleNext() {
    if (!canNext.value) return;
    emit('next');
  }

  // 暴露方法给父组件
  defineExpose({
    reset: () => {
      // 现在重置逻辑由父组件处理，这里只是保持接口兼容性
      emit('reset');
    }
  });
</script>

<style lang="scss" scoped>
  /* 游戏区域布局 */
  .game-content-wrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 32rpx;
  }

  /* 题目展示区通用样式 */
  .question-wrapper {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95)) !important;
    border-radius: 24rpx;
    padding: 32rpx;
    box-shadow:
      0 16rpx 48rpx rgba(139, 69, 19, 0.15),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
    border: 2rpx solid rgba(139, 69, 19, 0.2) !important;
    text-align: center;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    gap: 30rpx;
  }

  /* 题目展示区装饰效果 */
  .question-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 49%, rgba(139, 69, 19, 0.02) 50%, transparent 51%);
    pointer-events: none;
  }

  /* 问题标题基础样式 */
  .question-title {
    font-size: 30rpx;
    font-weight: 400;
    text-align: left;
    padding: 16rpx 20rpx;
    color: #8b4513;
    line-height: 1.4;
    letter-spacing: 1rpx;
    margin-bottom: 0;
    position: relative;
    display: block;
    width: 100%;
    background: linear-gradient(135deg, rgba(139, 69, 19, 0.08), rgba(160, 82, 45, 0.08));
    box-shadow: 0 2rpx 8rpx rgba(139, 69, 19, 0.1);
    border-radius: 16rpx;
    border: 2rpx solid rgba(139, 69, 19, 0.2);;
    transition: all 0.3s ease;
  }

  .question-content-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20rpx;
  }

  /* 选项区域容器通用样式 */
  .options-wrapper {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.8));
    border-radius: 24rpx;
    padding: 32rpx 24rpx;
    box-shadow:
      0 12rpx 36rpx rgba(139, 69, 19, 0.12),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
    border: 2rpx solid rgba(139, 69, 19, 0.1);
  }

  /* 标题和跳过按钮区域布局 */
  .options-title-container {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    justify-content: flex-start !important;
    gap: 16rpx;
    width: 100%;
    box-sizing: border-box;
    min-height: 60rpx;
  }

  /* 标题和表情组合容器 */
  .options-title-with-emoji {
    display: flex;
    align-items: center;
    gap: 8rpx;
  }

  /* 答题结果表情反馈 */
  .answer-emoji {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: emoji-appear 0.5s ease-out;
    margin-left: 4rpx;
  }

  .emoji-text {
    font-size: 48rpx;
    line-height: 1;
    animation: emoji-bounce 0.6s ease-out;
  }

  @keyframes emoji-appear {
    0% {
      opacity: 0;
      transform: scale(0);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes emoji-bounce {
    0% {
      transform: scale(0);
    }
    50% {
      transform: scale(1.2);
    }
    100% {
      transform: scale(1);
    }
  }


  /* 选项标题基础样式 */
  .options-title {
    flex: 1;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    margin: 0;
    font-size: 32rpx;
    font-weight: 400;
    color: #8b4513;
    text-align: left;
    line-height: 1.4;
    letter-spacing: 1rpx;
    position: relative;
    width: auto;
    padding-left:32rpx;
  }

  /* 选项网格布局覆盖 */
  .options-grid {
    gap: 32rpx;
    /* 确保跨平台一致性 */
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    padding: 20rpx;
  }

    /* 选项内容容器 */
  .option-container {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }

  /* 选项文字样式 */
  /* 选项内容容器 */
  .option-content {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    text-shadow: 0 1rpx 2rpx rgba(212, 175, 55, 0.3);
    letter-spacing: 0.5rpx;
    color: #6b3410;
    opacity: 0.6;
    font-size: 36rpx;
    font-weight: 500;
  }

  /* 光晕效果 */
  .option-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 16rpx;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }

  /* 选中状态样式 */
  .game-option-btn.selected {
    border-color: #8b4513 !important;
    box-shadow: 0 0 0 3rpx rgba(139, 69, 19, 0.3) !important;
    background: linear-gradient(135deg, rgba(139, 69, 19, 0.12), rgba(139, 69, 19, 0.06)) !important;

    .option-glow {
      opacity: 0.5;
      background: radial-gradient(circle, rgba(139, 69, 19, 0.15), transparent);
    }
  }

  .game-option-btn {
    /* 基础布局 */
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;

    /* 尺寸 */
    min-height: 100rpx;
    width: 100%;
    min-width: 0;
    padding: 24rpx;

    /* 古典文雅样式 */
    background: linear-gradient(135deg, rgba(139, 69, 19, 0.08), rgba(160, 82, 45, 0.08));
    box-shadow: 0 2rpx 8rpx rgba(139, 69, 19, 0.1);
    border-radius: 16rpx;
    border: 2rpx solid rgba(139, 69, 19, 0.2);

    /* 阴影效果 */
    box-shadow:
      inset 0 2rpx 4rpx rgba(212, 175, 55, 0.2),
      0 4rpx 16rpx rgba(139, 69, 19, 0.1);

    /* 交互效果 */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
  }

  /* 结果图标 */
  .result-icon {
    position: absolute;
    top: 8rpx;
    right: 8rpx;
    width: 48rpx;
    height: 48rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: result-appear 0.3s ease-out;

    .icon {
      font-size: 46rpx;
      font-weight: bold;
      line-height: 1;

      &.correct {
        color: #4caf50;
      }

      &.incorrect {
        color: #f44336;
      }
    }
  }

  /* 答题控制区域 */
  .answer-control-wrapper {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20rpx;
    padding: 32rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
    border-top: 2rpx solid rgba(0, 0, 0, 0.1);
  }

  .answer-control-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 24rpx;
  }

  .answer-control-btn {
    flex: 1;
    // height: 88rpx;
    border-radius: 20rpx;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 28rpx;
    font-weight: 600;


    background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
    padding: 0.6rem;
    text-align: center;
    box-shadow: 0 0.25rem 1rem rgba(139, 69, 19, 0.25), inset 0 0.0625rem 0 rgba(255, 255, 255, 0.2);
    transition: all 0.2s ease;

    &:active {
      transform: scale(0.95);
    }

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
      pointer-events: none;
    }
  
  }

  .btn-text {
    color: white;
  }



  @keyframes result-appear {
    0% {
      opacity: 0;
      transform: translateY(20rpx);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style>
