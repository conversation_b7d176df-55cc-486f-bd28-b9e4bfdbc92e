<template>
  <view v-if="visible" class="game-result-modal" @tap="handleBackdropClick">
    <view class="modal-content" @tap.stop>
      <view class="result-header">
        <view class="result-icon" :class="computedResultType">
          <view v-if="computedResultType === 'success'" class="success-icon">✓</view>
          <view v-else-if="computedResultType === 'failure'" class="failure-icon">✗</view>
          <view v-else class="info-icon">!</view>
        </view>
        <view class="result-title">{{ computedTitle }}</view>
      </view>

      <view class="result-body">
        <view v-if="computedMessage" class="result-message">{{ computedMessage }}</view>

        <view v-if="showScore" class="score-section">
          <view class="score-item main-score">
            <view class="score-label">本次得分</view>
            <view class="score-value">{{ computedFinalScore || computedScore }}</view>
            <view v-if="computedIsNewRecord" class="new-record">🏆 新纪录!</view>
          </view>

          <!-- 积分详细分解 -->
          <view v-if="showScoreBreakdown && computedScoreBreakdown" class="score-breakdown">
            <view class="breakdown-title">积分详情</view>
            <view class="breakdown-item">
              <text class="breakdown-label">基础分数</text>
              <text class="breakdown-value">{{ computedScoreBreakdown.baseScore }}</text>
            </view>
            <view v-if="computedScoreBreakdown.timeBonus > 0" class="breakdown-item bonus">
              <text class="breakdown-label">时间奖励</text>
              <text class="breakdown-value">+{{ computedScoreBreakdown.timeBonus }}</text>
            </view>
            <view v-if="computedScoreBreakdown.accuracyBonus > 0" class="breakdown-item bonus">
              <text class="breakdown-label">准确率奖励</text>
              <text class="breakdown-value">+{{ computedScoreBreakdown.accuracyBonus }}</text>
            </view>
            <view v-if="computedScoreBreakdown.comboBonus > 0" class="breakdown-item bonus">
              <text class="breakdown-label">连击奖励</text>
              <text class="breakdown-value">+{{ computedScoreBreakdown.comboBonus }}</text>
            </view>
            <view v-if="computedScoreBreakdown.skillBonus > 0" class="breakdown-item bonus">
              <text class="breakdown-label">技能奖励</text>
              <text class="breakdown-value">+{{ computedScoreBreakdown.skillBonus }}</text>
            </view>
            <view v-if="computedScoreBreakdown.activityBonus > 0" class="breakdown-item bonus special">
              <text class="breakdown-label">活跃度奖励</text>
              <text class="breakdown-value">+{{ computedScoreBreakdown.activityBonus }}</text>
            </view>
            <view v-if="computedScoreBreakdown.difficultyMultiplier !== 1" class="breakdown-item multiplier">
              <text class="breakdown-label">难度倍数</text>
              <text class="breakdown-value">×{{ computedScoreBreakdown.difficultyMultiplier }}</text>
            </view>
            <view class="breakdown-item total">
              <text class="breakdown-label">总获得积分</text>
              <text class="breakdown-value final">{{ computedScoreBreakdown.finalScore }}</text>
            </view>
          </view>

          <view v-if="bestScore > 0" class="score-item">
            <view class="score-label">历史最高分</view>
            <view class="score-value best">{{ bestScore }}</view>
          </view>
        </view>

        <!-- 成就解锁 -->
        <view v-if="newAchievements && newAchievements.length > 0" class="achievements-section">
          <view class="achievements-title">🎉 解锁新成就!</view>
          <view class="achievement-list">
            <view v-for="achievement in newAchievements" :key="achievement.id" class="achievement-item">
              <text class="achievement-icon">{{ achievement.icon }}</text>
              <view class="achievement-info">
                <text class="achievement-name">{{ achievement.name }}</text>
                <text class="achievement-desc">{{ achievement.description }}</text>
                <text class="achievement-reward">+{{ achievement.points }}分</text>
              </view>
            </view>
          </view>
        </view>

        <view v-if="showStats" class="stats-section">
          <view class="stat-item">
            <view class="stat-label">正确率</view>
            <view class="stat-value">{{ computedAccuracy }}%</view>
          </view>

          <view class="stat-item">
            <view class="stat-label">用时</view>
            <view class="stat-value">{{ formatTime(computedDuration) }}</view>
          </view>
        </view>

        <view v-if="extraContent" class="extra-content">
          <slot name="extra"></slot>
        </view>
      </view>

      <view class="result-actions">
        <button
          v-for="action in actions"
          :key="action.key"
          :class="['action-btn', action.type || 'default']"
          @tap="handleAction(action)"
        >
          {{ action.text }}
        </button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import type { GameResult as GameResultType } from '@/games/shared/types';
  import type { Achievement } from '@/games/shared/services/achievement/achievement-engine';
  import type { ScoreBreakdown } from '@/games/shared/services/scoring/score-calculator';
  import type { GameServiceResult } from '@/games/shared/services/game-service';

  interface GameAction {
    key: string;
    text: string;
    type?: 'primary' | 'secondary' | 'default';
  }

  interface Props {
    visible: boolean;
    // 可以传入完整的游戏结果对象，组件内部自动计算 title、message、resultType
    gameResult?: GameResultType | null;
    gameServiceResult?: GameServiceResult | null;
    // 或者手动指定这些属性（向后兼容）
    title?: string;
    message?: string;
    resultType?: 'success' | 'failure' | 'info';
    score?: number;
    finalScore?: number;
    bestScore?: number;
    accuracy?: number;
    duration?: number;
    showScore?: boolean;
    showStats?: boolean;
    showScoreBreakdown?: boolean;
    scoreBreakdown?: ScoreBreakdown | null;
    isNewRecord?: boolean;
    newAchievements?: Achievement[];
    extraContent?: boolean;
    actions?: GameAction[];
    allowBackdropClose?: boolean;
  }

  interface Emits {
    (e: 'close'): void;
    (e: 'action', actionKey: string): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    gameResult: null,
    gameServiceResult: null,
    title: '',
    message: '',
    resultType: 'info',
    score: 0,
    finalScore: 0,
    bestScore: 0,
    accuracy: 0,
    duration: 0,
    showScore: false,
    showStats: false,
    showScoreBreakdown: false,
    scoreBreakdown: null,
    isNewRecord: false,
    newAchievements: () => [],
    extraContent: false,
    actions: () => [{ key: 'restart', text: '再玩一次', type: 'primary' as const }, { key: 'back', text: '返回', type: 'secondary' as const }],
    allowBackdropClose: true,
  });

  const emit = defineEmits<Emits>();

  // 计算属性：自动生成标题
  const computedTitle = computed(() => {
    if (props.title) return props.title; // 如果手动指定了标题，使用手动指定的
    if (!props.gameResult) return '';

    const accuracy = props.gameResult.accuracy || 0;
    if (accuracy >= 90) return '完美表现！';
    if (accuracy >= 70) return '表现不错！';
    if (accuracy >= 50) return '继续努力！';
    return '再接再厉！';
  });

  // 计算属性：自动生成消息
  const computedMessage = computed(() => {
    if (props.message) return props.message; // 如果手动指定了消息，使用手动指定的
    if (!props.gameResult) return '';

    return `你答对了 ${props.gameResult.correctAnswers || 0} 题，准确率 ${props.gameResult.accuracy || 0}%`;
  });

  // 计算属性：自动生成结果类型
  const computedResultType = computed(() => {
    if (props.resultType !== 'info') return props.resultType; // 如果手动指定了类型，使用手动指定的
    if (!props.gameResult) return 'info';

    const accuracy = props.gameResult.accuracy || 0;
    if (accuracy >= 80) return 'success';
    if (accuracy >= 60) return 'info';
    return 'failure';
  });

  // 计算属性：自动获取分数相关数据
  const computedScore = computed(() => props.score || props.gameResult?.score || 0);
  const computedFinalScore = computed(() =>
    props.finalScore ||
    props.gameServiceResult?.scoreBreakdown?.finalScore ||
    props.gameResult?.score ||
    0
  );
  const computedAccuracy = computed(() => props.accuracy || props.gameResult?.accuracy || 0);
  const computedDuration = computed(() => props.duration || props.gameResult?.duration || 0);
  const computedScoreBreakdown = computed(() =>
    props.scoreBreakdown ||
    props.gameServiceResult?.scoreBreakdown ||
    null
  );
  const computedIsNewRecord = computed(() =>
    props.isNewRecord ||
    props.gameServiceResult?.isNewRecord ||
    false
  );

  // 处理背景点击
  const handleBackdropClick = () => {
    if (props.allowBackdropClose) {
      emit('close');
    }
  };

  // 处理按钮点击
  const handleAction = (action: GameAction) => {
    emit('action', action.key);
  };

  // 格式化时间
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}分${secs}秒`;
  };
</script>

<style lang="scss" scoped>
  .game-result-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease-out;
  }

  .modal-content {
    background: white;
    border-radius: 40rpx;
    padding: 60rpx;
    margin: 40rpx;
    max-width: 800rpx;
    width: 100%;
    box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
    animation: slideUp 0.3s ease-out;
  }

  .result-header {
    text-align: center;
    margin-bottom: 48rpx;

    .result-icon {
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 32rpx;
      font-size: 60rpx;
      font-weight: bold;

      &.success {
        background: linear-gradient(135deg, #4caf50, #66bb6a);
        color: white;
      }

      &.failure {
        background: linear-gradient(135deg, #f44336, #ef5350);
        color: white;
      }

      &.info {
        background: linear-gradient(135deg, #2196f3, #42a5f5);
        color: white;
      }
    }

    .result-title {
      font-size: 40rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .result-body {
    margin-bottom: 48rpx;

    .result-message {
      font-size: 32rpx;
      color: #666;
      text-align: center;
      line-height: 1.5;
      margin-bottom: 40rpx;
    }
  }

  .score-section {
    margin-bottom: 40rpx;

    .score-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24rpx 32rpx;
      margin-bottom: 16rpx;
      background: #f8f4e9;
      border-radius: 16rpx;

      &.main-score {
        position: relative;
        background: linear-gradient(135deg, #8b4513, #a0522d);
        color: white;
        padding: 32rpx;
        border-radius: 24rpx;

        .score-label {
          color: rgba(255, 255, 255, 0.9);
        }

        .score-value {
          color: white;
          font-size: 56rpx;
        }

        .new-record {
          position: absolute;
          top: -16rpx;
          right: 16rpx;
          background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
          color: white;
          font-size: 24rpx;
          padding: 8rpx 16rpx;
          border-radius: 24rpx;
          font-weight: bold;
        }
      }

      .score-label {
        font-size: 28rpx;
        color: #666;
      }

      .score-value {
        font-size: 36rpx;
        font-weight: bold;
        color: #8b4513;

        &.best {
          color: #ff9800;
        }
      }
    }
  }

  .stats-section {
    display: flex;
    justify-content: space-around;
    margin-bottom: 40rpx;

    .stat-item {
      text-align: center;

      .stat-label {
        font-size: 28rpx;
        color: #666;
        margin-bottom: 8rpx;
      }

      .stat-value {
        font-size: 36rpx;
        font-weight: bold;
        color: #333;
      }
    }
  }

  .extra-content {
    margin-bottom: 40rpx;
  }

  .result-actions {
    display: flex;
    gap: 24rpx;

    .action-btn {
      flex: 1;
      padding: 24rpx 40rpx;
      border-radius: 24rpx;
      font-size: 32rpx;
      font-weight: bold;
      border: none;
      transition: all 0.2s ease;

      &.primary {
        background: linear-gradient(135deg, #8b4513, #a0522d);
        color: white;
        box-shadow: 0 8rpx 24rpx rgba(139, 69, 19, 0.3);
      }

      &.secondary {
        background: white;
        color: #8b4513;
        border: 4rpx solid #8b4513;
      }

      &.default {
        background: #f5f5f5;
        color: #666;
      }

      &:active {
        transform: translateY(2rpx);
      }
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(100rpx);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  // 积分分解样式
  .score-breakdown {
    background: #f9f9f9;
    border-radius: 16rpx;
    padding: 24rpx;
    margin: 24rpx 0;

    .breakdown-title {
      font-size: 28rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 16rpx;
      text-align: center;
    }

    .breakdown-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12rpx 0;
      font-size: 26rpx;

      .breakdown-label {
        color: #666;
      }

      .breakdown-value {
        font-weight: bold;
        color: #333;
      }

      &.bonus {
        .breakdown-value {
          color: #4caf50;
        }

        &.special {
          background: linear-gradient(90deg, rgba(76, 175, 80, 0.1), rgba(76, 175, 80, 0.05));
          padding: 16rpx 24rpx;
          border-radius: 12rpx;
          margin: 8rpx 0;

          .breakdown-label {
            font-weight: 600;
            color: #2e7d32;
          }

          .breakdown-value {
            color: #2e7d32;
            font-weight: 700;
          }
        }
      }

      &.multiplier .breakdown-value {
        color: #2196f3;
      }

      &.total {
        background: linear-gradient(90deg, rgba(139, 69, 19, 0.1), rgba(139, 69, 19, 0.05));
        padding: 20rpx 24rpx;
        border-radius: 12rpx;
        margin-top: 16rpx;
        border: 2rpx solid rgba(139, 69, 19, 0.2);

        .breakdown-label {
          font-weight: 700;
          color: #8b4513;
        }

        .breakdown-value.final {
          color: #8b4513;
          font-weight: 700;
          font-size: 32rpx;
        }
      }
    }
  }

  // 成就解锁样式
  .achievements-section {
    background: linear-gradient(135deg, #fff3e0, #ffe0b2);
    border-radius: 24rpx;
    padding: 32rpx;
    margin-bottom: 40rpx;

    .achievements-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #e65100;
      text-align: center;
      margin-bottom: 24rpx;
    }

    .achievement-list {
      .achievement-item {
        display: flex;
        align-items: center;
        padding: 16rpx 0;
        border-bottom: 2rpx solid rgba(230, 81, 0, 0.1);

        &:last-child {
          border-bottom: none;
        }

        .achievement-icon {
          font-size: 48rpx;
          margin-right: 24rpx;
        }

        .achievement-info {
          flex: 1;

          .achievement-name {
            display: block;
            font-size: 28rpx;
            font-weight: bold;
            color: #333;
            margin-bottom: 4rpx;
          }

          .achievement-desc {
            display: block;
            font-size: 24rpx;
            color: #666;
            margin-bottom: 4rpx;
          }

          .achievement-reward {
            display: block;
            font-size: 24rpx;
            color: #4caf50;
            font-weight: bold;
          }
        }
      }
    }
  }
</style>
