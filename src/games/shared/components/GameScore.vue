<template>
  <view class="game-score">
    <view class="score-item">
      <view class="score-label">分数</view>
      <view class="score-value" :class="{ animated: scoreChanged }">
        {{ animatedScore }}
      </view>
    </view>

    <view v-if="currentCombo > 1" class="combo-item">
      <view class="combo-value" :class="{ active: currentCombo > 1 }"> {{ currentCombo }}x </view>
    </view>
  </view>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue';
  import type { GameState } from '@/games/shared/types';

  interface Props {
    // 可以直接传入游戏状态，自动获取分数
    gameState?: Pick<GameState, 'score'> | null;
    // 或者手动指定分数和连击（向后兼容）
    score?: number;
    combo?: number;
  }

  const props = withDefaults(defineProps<Props>(), {
    gameState: null,
    score: 0,
    combo: 0,
  });

  // 计算当前分数
  const currentScore = computed(() => {
    if (props.gameState) {
      return props.gameState.score;
    }
    return props.score || 0;
  });

  // 计算当前连击
  const currentCombo = computed(() => {
    return props.combo || 0;
  });

  const animatedScore = ref(0);
  const scoreChanged = ref(false);

  // 监听分数变化，添加动画效果
  watch(
    () => currentScore.value,
    (newScore, oldScore) => {
      // 初始化时直接设置分数，不做动画
      if (oldScore === undefined || oldScore === null) {
        animatedScore.value = newScore;
        return;
      }

      if (newScore !== oldScore) {
        scoreChanged.value = true;

        // 数字递增动画
        const duration = 500; // 动画持续时间
        const steps = 20; // 动画步数
        const increment = (newScore - oldScore) / steps;
        let currentStep = 0;

        const timer = setInterval(() => {
          currentStep++;
          animatedScore.value = Math.round(oldScore + increment * currentStep);

          if (currentStep >= steps) {
            clearInterval(timer);
            animatedScore.value = newScore;

            // 移除动画类
            setTimeout(() => {
              scoreChanged.value = false;
            }, 200);
          }
        }, duration / steps);
      }
    },
    { immediate: true }
  );
</script>

<style lang="scss" scoped>
  .game-score {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16rpx 24rpx;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 244, 233, 0.95) 100%);
    border-radius: 24rpx;
    box-shadow: 0 8rpx 24rpx rgba(139, 69, 19, 0.15);
    border: 2rpx solid rgba(139, 69, 19, 0.2);
    width: 210rpx; /* 适配小程序屏幕宽度 */
    height: 88rpx; /* 与其他组件保持一致 */
  }

  .score-item {
    display: flex;
    align-items: center;
    gap: 16rpx;
  }

  .combo-item {
    position: absolute;
    top: -16rpx;
    right: -16rpx;
    display: flex;
    align-items: center;
    z-index: 10;
  }

  .score-label {
    font-size: 28rpx !important;
    color: #8b4513;
    font-weight: 600;
    opacity: 0.8;
    line-height: 1;
    white-space: nowrap;
  }

  .score-value {
    font-size: 40rpx !important;
    font-weight: bold !important;
    color: #8b4513;
    font-family: 'Arial', sans-serif;
    transition: all 0.3s ease;
    line-height: 1;
    white-space: nowrap;

    &.animated {
      transform: scale(1.1);
      color: #d4af37;
      text-shadow: 0 0 16rpx rgba(212, 175, 55, 0.4);
    }
  }

  .combo-value {
    font-size: 24rpx !important;
    font-weight: bold !important;
    font-family: 'Arial', sans-serif;
    color: white;
    background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);
    padding: 4rpx 12rpx;
    border-radius: 20rpx;
    border: 4rpx solid white;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    box-shadow: 0 4rpx 16rpx rgba(255, 107, 53, 0.4);
    animation: combo-appear 0.3s ease-out;

    &.active {
      animation: combo-pulse 0.6s ease-in-out infinite alternate;
    }
  }

  @keyframes combo-appear {
    0% {
      transform: scale(0) rotate(-180deg);
      opacity: 0;
    }
    100% {
      transform: scale(1) rotate(0deg);
      opacity: 1;
    }
  }

  @keyframes combo-pulse {
    0% {
      transform: scale(1);
      box-shadow: 0 4rpx 16rpx rgba(255, 107, 53, 0.4);
    }
    100% {
      transform: scale(1.1);
      box-shadow: 0 8rpx 24rpx rgba(255, 107, 53, 0.6);
    }
  }
</style>
