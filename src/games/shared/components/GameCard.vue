<template>
  <view class="game-card" @tap="handlePlay">
    <!-- 难度标识 - 右上角 -->
    <view class="difficulty" :class="game.difficulty">
      {{ difficultyText }}
    </view>

    <!-- 游戏图标 -->
    <view class="game-icon">
      <text class="emoji-icon">{{ game.icon }}</text>
    </view>

    <!-- 游戏信息 -->
    <view class="game-info">
      <view class="game-title">
        {{ game.name }}
        <view v-if="game.isNew" class="new-badge">NEW</view>
      </view>
      <view class="game-description">{{ game.description }}</view>
    </view>

    <!-- 游戏统计 -->
    <view v-if="hasStats" class="game-stats">
      <view v-if="game.bestScore" class="stat-item">
        <text class="stat-label">最高分</text>
        <text class="stat-value">{{ game.bestScore }}</text>
      </view>
      <view v-if="game.playCount" class="stat-item">
        <text class="stat-label">已玩</text>
        <text class="stat-value">{{ game.playCount }}次</text>
      </view>
    </view>

    <!-- 无统计数据时的占位 -->
    <view v-if="!hasStats" class="no-stats">
      <text class="no-stats-text">开始你的第一局游戏吧！</text>
    </view>

    <!-- 开始按钮 -->
    <view class="play-button">
      <text class="play-text">开始游戏</text>
    </view>
  </view>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import type { GameRegistry } from '@/games/shared/types';

  interface Props {
    game: GameRegistry;
  }

  interface Emits {
    (e: 'play', game: GameRegistry): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  const difficultyText = computed(() => {
    const map = {
      easy: '简单',
      medium: '中等',
      hard: '困难',
    };
    return map[props.game.difficulty];
  });

  const hasStats = computed(() => {
    return !!(props.game.bestScore || props.game.playCount);
  });

  const handlePlay = () => {
    emit('play', props.game);
  };
</script>

<style lang="scss" scoped>
  .game-card {
    background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
    border-radius: 40rpx;
    padding: 30rpx;
    box-shadow:
      0 16rpx 64rpx rgba(0, 0, 0, 0.08),
      0 4rpx 16rpx rgba(0, 0, 0, 0.04);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    border: 2rpx solid rgba(139, 69, 19, 0.08);

    // 添加微妙的背景纹理
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 20% 20%, rgba(139, 69, 19, 0.02) 0%, transparent 50%);
      pointer-events: none;
    }

    &:active {
      transform: translateY(4rpx) scale(0.98);
      box-shadow:
        0 8rpx 32rpx rgba(0, 0, 0, 0.12),
        0 2rpx 8rpx rgba(0, 0, 0, 0.08);
    }
  }

  .difficulty {
    position: absolute;
    top: 18rpx;
    right: 18rpx;
    font-size: 20rpx;
    padding: 2rpx 10rpx;
    border-radius: 18rpx;
    font-weight: 600;
    z-index: 2;
    backdrop-filter: blur(20rpx);

    &.easy {
      background: rgba(76, 175, 80, 0.15);
      color: #2e7d32;
      border: 2rpx solid rgba(76, 175, 80, 0.2);
    }
    &.medium {
      background: rgba(255, 152, 0, 0.15);
      color: #ef6c00;
      border: 2rpx solid rgba(255, 152, 0, 0.2);
    }
    &.hard {
      background: rgba(244, 67, 54, 0.15);
      color: #c62828;
      border: 2rpx solid rgba(244, 67, 54, 0.2);
    }
  }

  .game-icon {
    width: 100rpx;
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8f4e9 0%, #e8dcc0 100%);
    border-radius: 36rpx;
    margin: 0 auto 32rpx;
    box-shadow:
      0 8rpx 32rpx rgba(139, 69, 19, 0.15),
      inset 0 2rpx 0 rgba(255, 255, 255, 0.5);
    border: 4rpx solid rgba(139, 69, 19, 0.1);

    .emoji-icon {
      font-size: 48rpx;
      line-height: 1;
    }
  }

  .game-info {
    text-align: center;
    margin-bottom: 32rpx;
  }

  .game-title {
    font-size: 32rpx;
    font-weight: 700;
    color: #2c1810;
    margin-bottom: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
    line-height: 1.2;
  }

  .new-badge {
    background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
    color: white;
    font-size: 16rpx;
    padding: 4rpx 12rpx;
    border-radius: 20rpx;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1rpx;
    box-shadow: 0 4rpx 8rpx rgba(255, 107, 107, 0.3);
  }

  .game-description {
    font-size: 24rpx;
    color: #666;
    line-height: 1.4;
    margin-bottom: 0;
  }

  .game-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 32rpx;
    padding: 24rpx 0;
    background: rgba(139, 69, 19, 0.03);
    border-radius: 24rpx;
    border: 2rpx solid rgba(139, 69, 19, 0.08);

    .stat-item {
      text-align: center;
      flex: 1;

      .stat-label {
        display: block;
        font-size: 20rpx;
        color: #999;
        margin-bottom: 4rpx;
        font-weight: 500;
      }

      .stat-value {
        display: block;
        font-size: 28rpx;
        color: #8b4513;
        font-weight: 700;
      }
    }
  }

  .no-stats {
    padding: 32rpx 0;
    text-align: center;
    margin-bottom: 32rpx;

    .no-stats-text {
      font-size: 24rpx;
      color: #999;
      font-style: italic;
    }
  }

  .play-button {
    background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
    border-radius: 32rpx;
    padding: 24rpx;
    text-align: center;
    box-shadow:
      0 8rpx 32rpx rgba(139, 69, 19, 0.25),
      inset 0 2rpx 0 rgba(255, 255, 255, 0.2);
    border: none;
    transition: all 0.2s ease;

    &:active {
      transform: translateY(2rpx);
      box-shadow:
        0 4rpx 16rpx rgba(139, 69, 19, 0.3),
        inset 0 2rpx 0 rgba(255, 255, 255, 0.2);
    }

    .play-text {
      color: white;
      font-size: 28rpx;
      font-weight: 600;
      letter-spacing: 1rpx;
    }
  }
</style>
