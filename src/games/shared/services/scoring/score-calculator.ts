/**
 * 积分计算引擎
 * 提供统一的积分计算逻辑，支持多种积分类型和动态权重
 *
 * 导出的专业类型：
 * - ScoreBreakdown: 积分明细
 * - ScoreRule: 完整的积分规则配置
 * - ScoreConfig: 积分计算器配置
 *
 * 游戏如需使用积分相关类型，请从此模块导入
 */

import type { GameResult, GameDifficulty } from '@/games/shared/types';

// 积分类型定义
export interface ScoreBreakdown {
  baseScore: number; // 基础积分
  timeBonus: number; // 时间奖励
  accuracyBonus: number; // 准确率奖励
  comboBonus: number; // 连击奖励
  difficultyMultiplier: number; // 难度倍数
  skillBonus: number; // 技能奖励
  activityBonus: number; // 活跃度奖励
  finalScore: number; // 最终积分
}

// 统一的积分配置接口（整合了原 ScoreRule）
export interface ScoreConfig {
  baseScoreWeight: number;
  timeWeight: number;
  accuracyWeight: number;
  comboWeight: number;
  difficultyMultipliers: Record<string, number>;
  maxTimeBonus: number;
  maxAccuracyBonus: number;
  maxComboBonus: number;
}

// 积分计算规则（从 types/game.ts 移入）
export interface ScoreRule {
  baseScore: number; // 基础分数
  difficultyMultiplier: Record<GameDifficulty, number>; // 难度倍数
  timeBonus: {
    enabled: boolean;
    maxBonus: number; // 最大时间奖励
    thresholdRatio: number; // 时间阈值比例（如0.5表示用时少于50%获得奖励）
  };
  accuracyBonus: {
    enabled: boolean;
    perfectBonus: number; // 100%准确率奖励
    highAccuracyThreshold: number; // 高准确率阈值（如90%）
    highAccuracyBonus: number; // 高准确率奖励
  };
  comboBonus: {
    enabled: boolean;
    multiplier: number; // 连击倍数
    maxCombo: number; // 最大连击数
  };
}

// 默认积分配置
const DEFAULT_SCORE_CONFIG: ScoreConfig = {
  baseScoreWeight: 1.0,
  timeWeight: 0.3,
  accuracyWeight: 0.4,
  comboWeight: 0.2,
  difficultyMultipliers: {
    easy: 0.8,
    medium: 1.0,
    hard: 1.3,
    expert: 1.6,
  },
  maxTimeBonus: 500,
  maxAccuracyBonus: 300,
  maxComboBonus: 200,
};

// 积分计算器
export class ScoreCalculator {
  private config: ScoreConfig;

  constructor(config: ScoreConfig = DEFAULT_SCORE_CONFIG) {
    this.config = config;
  }

  /**
   * 计算游戏积分
   */
  calculateScore(gameResult: GameResult, userContext?: Record<string, unknown>): ScoreBreakdown {
    const breakdown: ScoreBreakdown = {
      baseScore: 0,
      timeBonus: 0,
      accuracyBonus: 0,
      comboBonus: 0,
      difficultyMultiplier: 1,
      skillBonus: 0,
      activityBonus: 0,
      finalScore: 0,
    };

    // 计算基础积分
    breakdown.baseScore = this.calculateBaseScore(gameResult);

    // 计算时间奖励
    breakdown.timeBonus = this.calculateTimeBonus(gameResult);

    // 计算准确率奖励
    breakdown.accuracyBonus = this.calculateAccuracyBonus(gameResult);

    // 计算连击奖励
    breakdown.comboBonus = this.calculateComboBonus(gameResult);

    // 获取难度倍数
    breakdown.difficultyMultiplier = this.getDifficultyMultiplier(gameResult.difficulty);

    // 计算技能奖励
    breakdown.skillBonus = this.calculateSkillBonus(gameResult, userContext);

    // 计算活跃度奖励
    breakdown.activityBonus = this.calculateActivityBonus(userContext);

    // 计算最终积分
    breakdown.finalScore = this.calculateFinalScore(breakdown);

    return breakdown;
  }

  /**
   * 计算基础积分
   */
  private calculateBaseScore(gameResult: GameResult): number {
    return Math.round(gameResult.score * this.config.baseScoreWeight);
  }

  /**
   * 计算时间奖励
   */
  private calculateTimeBonus(gameResult: GameResult): number {
    // 基于游戏时长和表现计算时间奖励
    const efficiency = gameResult.score / Math.max(gameResult.duration, 1);
    const timeBonus = Math.min(efficiency * this.config.timeWeight * 100, this.config.maxTimeBonus);
    return Math.round(timeBonus);
  }

  /**
   * 计算准确率奖励
   */
  private calculateAccuracyBonus(gameResult: GameResult): number {
    if (!gameResult.accuracy) return 0;

    const accuracyBonus = Math.min(
      (gameResult.accuracy / 100) * this.config.maxAccuracyBonus * this.config.accuracyWeight,
      this.config.maxAccuracyBonus
    );
    return Math.round(accuracyBonus);
  }

  /**
   * 计算连击奖励
   */
  private calculateComboBonus(gameResult: GameResult): number {
    const maxCombo = gameResult.maxCombo || 0;
    if (maxCombo <= 1) return 0;

    const comboBonus = Math.min(Math.log2(maxCombo) * this.config.comboWeight * 50, this.config.maxComboBonus);
    return Math.round(comboBonus);
  }

  /**
   * 获取难度倍数
   */
  private getDifficultyMultiplier(difficulty: string): number {
    return this.config.difficultyMultipliers[difficulty] || 1.0;
  }

  /**
   * 计算技能奖励
   */
  private calculateSkillBonus(gameResult: GameResult, userContext?: Record<string, unknown>): number {
    if (!userContext) return 0;

    let skillBonus = 0;

    // 基于历史表现的技能奖励
    const gameHistory = ((userContext.gameHistory as Record<string, number[]>) || {})[gameResult.gameId] || [];
    if (gameHistory.length > 0) {
      const averageScore = gameHistory.reduce((sum: number, score: number) => sum + score, 0) / gameHistory.length;
      if (gameResult.score > averageScore * 1.2) {
        skillBonus += 100; // 超越个人平均表现20%
      }
    }

    // 连续游戏奖励
    const currentStreak = userContext.currentStreak as number;
    if (currentStreak > 5) {
      skillBonus += Math.min(currentStreak * 5, 100);
    }

    return Math.round(skillBonus);
  }

  /**
   * 计算活跃度奖励
   */
  private calculateActivityBonus(userContext?: Record<string, unknown>): number {
    if (!userContext) return 0;

    let activityBonus = 0;

    // 每日首次游戏奖励
    if (userContext.isFirstGameToday as boolean) {
      activityBonus += 50;
    }

    // 连续登录奖励
    const consecutiveDays = userContext.consecutiveDays as number;
    if (consecutiveDays > 1) {
      activityBonus += Math.min(consecutiveDays * 10, 100);
    }

    // 完成每日目标奖励
    if (userContext.dailyGoalCompleted as boolean) {
      activityBonus += 100;
    }

    return Math.round(activityBonus);
  }

  /**
   * 计算最终积分
   */
  private calculateFinalScore(breakdown: ScoreBreakdown): number {
    const subtotal =
      breakdown.baseScore +
      breakdown.timeBonus +
      breakdown.accuracyBonus +
      breakdown.comboBonus +
      breakdown.skillBonus +
      breakdown.activityBonus;

    return Math.round(subtotal * breakdown.difficultyMultiplier);
  }

  /**
   * 更新积分配置
   */
  updateConfig(newConfig: Partial<ScoreConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 获取当前配置
   */
  getConfig(): ScoreConfig {
    return { ...this.config };
  }

  /**
   * 验证积分计算结果
   */
  validateScore(breakdown: ScoreBreakdown): boolean {
    // 检查积分是否在合理范围内
    if (breakdown.finalScore < 0 || breakdown.finalScore > 100000) {
      return false;
    }

    // 检查各项奖励是否在限制范围内
    if (
      breakdown.timeBonus > this.config.maxTimeBonus ||
      breakdown.accuracyBonus > this.config.maxAccuracyBonus ||
      breakdown.comboBonus > this.config.maxComboBonus
    ) {
      return false;
    }

    return true;
  }

  /**
   * 获取积分说明
   */
  getScoreExplanation(breakdown: ScoreBreakdown): string[] {
    const explanations: string[] = [];

    explanations.push(`基础积分: ${breakdown.baseScore}`);

    if (breakdown.timeBonus > 0) {
      explanations.push(`时间奖励: +${breakdown.timeBonus}`);
    }

    if (breakdown.accuracyBonus > 0) {
      explanations.push(`准确率奖励: +${breakdown.accuracyBonus}`);
    }

    if (breakdown.comboBonus > 0) {
      explanations.push(`连击奖励: +${breakdown.comboBonus}`);
    }

    if (breakdown.skillBonus > 0) {
      explanations.push(`技能奖励: +${breakdown.skillBonus}`);
    }

    if (breakdown.activityBonus > 0) {
      explanations.push(`活跃奖励: +${breakdown.activityBonus}`);
    }

    if (breakdown.difficultyMultiplier !== 1) {
      explanations.push(`难度倍数: ×${breakdown.difficultyMultiplier}`);
    }

    explanations.push(`最终积分: ${breakdown.finalScore}`);

    return explanations;
  }
}

// 导出默认实例
export const scoreCalculator = new ScoreCalculator();
