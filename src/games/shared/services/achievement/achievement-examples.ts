/**
 * 成就系统使用示例
 * 展示如何在游戏中使用成就系统
 */

import { gameService } from '../game-service';
import { eventBus } from '../user/user-events';
import type { GameResult } from '@/types/game';
import type { Achievement, UserAchievement } from './achievement-engine';

/**
 * 游戏结束后检查成就的示例
 */
export async function handleGameEndAchievements(gameResult: GameResult): Promise<void> {
  try {
    // 检查游戏相关成就
    const newAchievements = await gameService.checkAchievements(gameResult);

    if (newAchievements.length > 0) {
      console.log('🎉 解锁新成就:', newAchievements);

      // 可以在这里添加UI提示逻辑
      for (const achievementId of newAchievements) {
        const achievement = gameService.getAllAchievements().find((a) => a.id === achievementId);
        if (achievement) {
          console.log(`✨ ${achievement.name}: ${achievement.description}`);
        }
      }
    }
  } catch (error) {
    console.error('检查成就时出错:', error);
  }
}

/**
 * 获取用户成就统计的示例
 */
export function displayAchievementStats(): void {
  const stats = gameService.getAchievementStats();

  console.log('📊 成就统计:');
  console.log(`总成就数: ${stats.total}`);
  console.log(`已完成: ${stats.completed}`);
  console.log(`进行中: ${stats.inProgress}`);
  console.log(`总积分: ${stats.totalPoints}`);

  console.log('按稀有度分布:');
  Object.entries(stats.byRarity).forEach(([rarity, count]) => {
    console.log(`  ${rarity}: ${count}`);
  });
}

/**
 * 获取用户成就列表的示例
 */
export function displayUserAchievements(): void {
  const userAchievements = gameService.getUserAchievements();
  const allAchievements = gameService.getAllAchievements();

  console.log('🏆 用户成就列表:');

  userAchievements.forEach((userAchievement) => {
    const achievement = allAchievements.find((a) => a.id === userAchievement.achievementId);
    if (achievement) {
      const status = userAchievement.isCompleted ? '✅' : '🔄';
      const progress = userAchievement.isCompleted ? '100%' : `${userAchievement.progress.toFixed(1)}%`;

      console.log(`${status} ${achievement.name} (${progress})`);
      console.log(`   ${achievement.description}`);

      if (!userAchievement.isCompleted) {
        console.log(`   进度: ${userAchievement.currentValue}/${userAchievement.targetValue}`);
      }
    }
  });
}

/**
 * 监听成就事件的示例
 */
export function setupAchievementEventListeners(): void {
  // 监听成就解锁事件
  eventBus.on('ACHIEVEMENT_UNLOCK', async (event) => {
    console.log('🎉 成就解锁!');
    console.log(`成就名称: ${event.data.achievementName}`);
    console.log(`成就ID: ${event.data.achievementId}`);

    if (event.data.points) {
      console.log(`获得积分: ${event.data.points}`);
    }

    if (event.data.rewards && event.data.rewards.length > 0) {
      console.log('奖励:');
      event.data.rewards.forEach((reward: { description: string }) => {
        console.log(`  - ${reward.description}`);
      });
    }

    // 这里可以添加UI通知逻辑
    // 例如显示成就解锁动画、播放音效等
  });
}

/**
 * 成就系统初始化示例
 */
export async function initializeAchievementSystem(): Promise<void> {
  try {
    // 初始化用户
    await gameService.initializeUser();

    // 设置事件监听器
    setupAchievementEventListeners();

    // 显示当前成就状态
    displayAchievementStats();

    console.log('✅ 成就系统初始化完成');
  } catch (error) {
    console.error('❌ 成就系统初始化失败:', error);
  }
}

/**
 * 模拟游戏结果测试成就系统
 */
export async function testAchievementSystem(): Promise<void> {
  console.log('🧪 开始测试成就系统...');

  // 模拟不同类型的游戏结果
  const testGameResults: GameResult[] = [
    // 测试分数成就
    {
      gameId: 'hexagram-match',
      score: 150,
      baseScore: 150,
      bonusScore: 0,
      finalScore: 150,
      duration: 45,
      accuracy: 95,
      difficulty: 'medium',
      correctAnswers: 8,
      totalQuestions: 10,
      maxCombo: 5,
      timeBonus: 0,
      accuracyBonus: 0,
      comboBonus: 0,
      difficultyMultiplier: 1.5,
      timestamp: Date.now(),
    },
    // 测试高分成就
    {
      gameId: 'hexagram-match',
      score: 600,
      baseScore: 400,
      bonusScore: 200,
      finalScore: 600,
      duration: 25,
      accuracy: 100,
      difficulty: 'hard',
      correctAnswers: 10,
      totalQuestions: 10,
      maxCombo: 10,
      timeBonus: 50,
      accuracyBonus: 100,
      comboBonus: 50,
      difficultyMultiplier: 2.0,
      timestamp: Date.now(),
    },
    // 测试速度成就
    {
      gameId: 'hexagram-match',
      score: 300,
      baseScore: 200,
      bonusScore: 100,
      finalScore: 300,
      duration: 20, // 快速完成
      accuracy: 90,
      difficulty: 'medium',
      correctAnswers: 9,
      totalQuestions: 10,
      maxCombo: 8,
      timeBonus: 80,
      accuracyBonus: 20,
      comboBonus: 0,
      difficultyMultiplier: 1.5,
      timestamp: Date.now(),
    },
  ];

  // 逐个测试游戏结果
  for (let i = 0; i < testGameResults.length; i++) {
    const gameResult = testGameResults[i];
    console.log(`\n🎮 测试游戏 ${i + 1}:`);
    console.log(`分数: ${gameResult.score}, 时长: ${gameResult.duration}s, 准确率: ${gameResult.accuracy}%`);

    // 检查成就
    const newAchievements = await gameService.checkAchievements(gameResult);

    if (newAchievements.length > 0) {
      console.log(`🎉 解锁 ${newAchievements.length} 个新成就!`);
    } else {
      console.log('📝 没有解锁新成就');
    }

    // 显示当前统计
    const stats = gameService.getAchievementStats();
    console.log(`当前已完成成就: ${stats.completed}/${stats.total}`);
  }

  console.log('\n📊 最终成就统计:');
  displayAchievementStats();

  console.log('\n🏆 用户成就详情:');
  displayUserAchievements();
}

/**
 * 成就系统完整使用示例
 */
export class AchievementSystemExample {
  private initialized = false;

  /**
   * 初始化成就系统
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    await initializeAchievementSystem();
    this.initialized = true;
  }

  /**
   * 处理游戏结束
   */
  async handleGameEnd(gameResult: GameResult): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }

    await handleGameEndAchievements(gameResult);
  }

  /**
   * 获取成就页面数据
   */
  getAchievementPageData(): {
    stats: Record<string, unknown>;
    userAchievements: UserAchievement[];
    allAchievements: Achievement[];
  } {
    return {
      stats: gameService.getAchievementStats(),
      userAchievements: gameService.getUserAchievements(),
      allAchievements: gameService.getAllAchievements(),
    };
  }

  /**
   * 运行测试
   */
  async runTest(): Promise<void> {
    await this.initialize();
    await testAchievementSystem();
  }
}

// 导出默认实例
export const achievementExample = new AchievementSystemExample();
