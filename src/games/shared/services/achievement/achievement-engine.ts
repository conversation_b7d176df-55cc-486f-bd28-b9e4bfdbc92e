/**
 * 成就判定引擎
 * 根据文档设计实现完整的成就系统
 */

import { eventBus } from '../user/user-events';
import type { GameResult } from '@/types/game';

// 成就类型定义
export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  type: 'score' | 'games_played' | 'accuracy' | 'speed' | 'combo' | 'special';
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  points: number; // 成就积分
  condition: AchievementCondition;
  rewards?: AchievementReward[];
  isHidden?: boolean; // 隐藏成就
  category?: string; // 成就分类
}

export interface AchievementCondition {
  type: 'single' | 'cumulative' | 'streak' | 'complex';
  target: number;
  gameId?: string; // 特定游戏成就
  difficulty?: string; // 特定难度成就
  timeLimit?: number; // 时间限制（秒）
  customCheck?: (userStats: Record<string, unknown>, gameResult?: GameResult) => boolean;
}

export interface AchievementReward {
  type: 'experience' | 'title' | 'badge' | 'multiplier';
  value: string | number;
  description: string;
}

export interface UserAchievement {
  achievementId: string;
  unlockedAt: number;
  progress: number; // 0-100
  isCompleted: boolean;
  currentValue: number;
  targetValue: number;
}

// 成就配置
export interface AchievementConfig {
  enabledCategories: string[];
  progressUpdateInterval: number;
  maxDailyAchievements: number;
}

// 默认成就配置
const DEFAULT_ACHIEVEMENT_CONFIG: AchievementConfig = {
  enabledCategories: ['score', 'games_played', 'accuracy', 'speed', 'combo', 'special'],
  progressUpdateInterval: 1000, // 1秒
  maxDailyAchievements: 10,
};

// 预定义成就列表
const PREDEFINED_ACHIEVEMENTS: Achievement[] = [
  // 积分成就
  {
    id: 'score_100',
    name: '百分达人',
    description: '单局游戏获得100分',
    icon: '💯',
    type: 'score',
    rarity: 'common',
    points: 10,
    condition: { type: 'single', target: 100 },
  },
  {
    id: 'score_500',
    name: '五百强者',
    description: '单局游戏获得500分',
    icon: '🌟',
    type: 'score',
    rarity: 'rare',
    points: 25,
    condition: { type: 'single', target: 500 },
  },
  {
    id: 'score_1000',
    name: '千分大师',
    description: '单局游戏获得1000分',
    icon: '👑',
    type: 'score',
    rarity: 'epic',
    points: 50,
    condition: { type: 'single', target: 1000 },
  },
  {
    id: 'total_score_10000',
    name: '万分传说',
    description: '累计获得10000分',
    icon: '🏆',
    type: 'score',
    rarity: 'legendary',
    points: 100,
    condition: { type: 'cumulative', target: 10000 },
  },

  // 游戏次数成就
  {
    id: 'first_game',
    name: '初次体验',
    description: '完成第一个游戏',
    icon: '🎮',
    type: 'games_played',
    rarity: 'common',
    points: 5,
    condition: { type: 'cumulative', target: 1 },
  },
  {
    id: 'games_10',
    name: '勤学者',
    description: '完成10局游戏',
    icon: '📚',
    type: 'games_played',
    rarity: 'common',
    points: 15,
    condition: { type: 'cumulative', target: 10 },
  },
  {
    id: 'games_100',
    name: '游戏达人',
    description: '完成100局游戏',
    icon: '🎯',
    type: 'games_played',
    rarity: 'rare',
    points: 50,
    condition: { type: 'cumulative', target: 100 },
  },

  // 准确率成就
  {
    id: 'accuracy_90',
    name: '精准射手',
    description: '单局准确率达到90%',
    icon: '🎯',
    type: 'accuracy',
    rarity: 'rare',
    points: 30,
    condition: { type: 'single', target: 90 },
  },
  {
    id: 'accuracy_100',
    name: '完美主义',
    description: '单局准确率达到100%',
    icon: '💎',
    type: 'accuracy',
    rarity: 'epic',
    points: 75,
    condition: { type: 'single', target: 100 },
  },

  // 速度成就
  {
    id: 'speed_30',
    name: '闪电侠',
    description: '30秒内完成游戏',
    icon: '⚡',
    type: 'speed',
    rarity: 'rare',
    points: 40,
    condition: { type: 'single', target: 30 },
  },
  {
    id: 'speed_15',
    name: '光速传说',
    description: '15秒内完成游戏',
    icon: '🌟',
    type: 'speed',
    rarity: 'epic',
    points: 80,
    condition: { type: 'single', target: 15 },
  },

  // 连击成就
  {
    id: 'combo_5',
    name: '连击新手',
    description: '达成5连击',
    icon: '🔥',
    type: 'combo',
    rarity: 'common',
    points: 20,
    condition: { type: 'single', target: 5 },
  },
  {
    id: 'combo_10',
    name: '连击大师',
    description: '达成10连击',
    icon: '💥',
    type: 'combo',
    rarity: 'rare',
    points: 45,
    condition: { type: 'single', target: 10 },
  },

  // 特殊成就
  {
    id: 'daily_streak_7',
    name: '七日坚持',
    description: '连续7天游戏',
    icon: '📅',
    type: 'special',
    rarity: 'rare',
    points: 60,
    condition: { type: 'streak', target: 7 },
  },
  {
    id: 'all_games_played',
    name: '全能玩家',
    description: '尝试所有游戏类型',
    icon: '🌈',
    type: 'special',
    rarity: 'epic',
    points: 100,
    condition: {
      type: 'complex',
      target: 1,
      customCheck: (userStats) => {
        const playedGames = Object.keys(userStats.gamesPlayed || {});
        return playedGames.length >= 2; // 当前有2个游戏
      },
    },
  },
];

// 成就判定引擎
export class AchievementEngine {
  private config: AchievementConfig;
  private achievements: Map<string, Achievement> = new Map();
  private userAchievements: Map<string, UserAchievement> = new Map();
  private dailyUnlocked: Set<string> = new Set();

  constructor(config: AchievementConfig = DEFAULT_ACHIEVEMENT_CONFIG) {
    this.config = config;
    this.initializeAchievements();
  }

  /**
   * 检查游戏结果触发的成就
   */
  async checkGameAchievements(gameResult: GameResult, userStats: Record<string, unknown>): Promise<string[]> {
    const newAchievements: string[] = [];

    // 检查每日成就限制
    if (this.dailyUnlocked.size >= this.config.maxDailyAchievements) {
      return newAchievements;
    }

    for (const achievement of this.achievements.values()) {
      // 跳过已完成的成就
      if (this.isAchievementCompleted(achievement.id)) {
        continue;
      }

      // 检查成就条件
      if (await this.checkAchievementCondition(achievement, gameResult, userStats)) {
        await this.unlockAchievement(achievement.id, userStats.userId as string);
        newAchievements.push(achievement.id);

        // 检查每日限制
        if (this.dailyUnlocked.size >= this.config.maxDailyAchievements) {
          break;
        }
      } else {
        // 更新进度
        await this.updateAchievementProgress(achievement, gameResult, userStats);
      }
    }

    return newAchievements;
  }

  /**
   * 检查用户统计触发的成就
   */
  async checkUserStatsAchievements(userStats: Record<string, unknown>): Promise<string[]> {
    const newAchievements: string[] = [];

    for (const achievement of this.achievements.values()) {
      if (this.isAchievementCompleted(achievement.id)) {
        continue;
      }

      if (achievement.condition.type === 'cumulative' || achievement.condition.type === 'complex') {
        if (await this.checkAchievementCondition(achievement, undefined, userStats)) {
          await this.unlockAchievement(achievement.id, userStats.userId as string);
          newAchievements.push(achievement.id);
        }
      }
    }

    return newAchievements;
  }

  /**
   * 获取用户成就列表
   */
  getUserAchievements(): UserAchievement[] {
    return Array.from(this.userAchievements.values());
  }

  /**
   * 获取成就详情
   */
  getAchievement(achievementId: string): Achievement | null {
    return this.achievements.get(achievementId) || null;
  }

  /**
   * 获取所有成就
   */
  getAllAchievements(): Achievement[] {
    return Array.from(this.achievements.values());
  }

  /**
   * 获取成就统计
   */
  getAchievementStats(): {
    total: number;
    completed: number;
    inProgress: number;
    byRarity: Record<string, number>;
    totalPoints: number;
  } {
    const total = this.achievements.size;
    const completed = Array.from(this.userAchievements.values()).filter((ua) => ua.isCompleted).length;
    const inProgress = Array.from(this.userAchievements.values()).filter(
      (ua) => !ua.isCompleted && ua.progress > 0
    ).length;

    const byRarity: Record<string, number> = {};
    let totalPoints = 0;

    for (const userAchievement of this.userAchievements.values()) {
      if (userAchievement.isCompleted) {
        const achievement = this.achievements.get(userAchievement.achievementId);
        if (achievement) {
          byRarity[achievement.rarity] = (byRarity[achievement.rarity] || 0) + 1;
          totalPoints += achievement.points;
        }
      }
    }

    return { total, completed, inProgress, byRarity, totalPoints };
  }

  /**
   * 私有方法：初始化成就
   */
  private initializeAchievements(): void {
    for (const achievement of PREDEFINED_ACHIEVEMENTS) {
      this.achievements.set(achievement.id, achievement);
    }
  }

  /**
   * 私有方法：检查成就条件
   */
  private async checkAchievementCondition(
    achievement: Achievement,
    gameResult?: GameResult,
    userStats?: Record<string, unknown>
  ): Promise<boolean> {
    const condition = achievement.condition;

    switch (condition.type) {
      case 'single':
        return this.checkSingleCondition(achievement, gameResult);
      case 'cumulative':
        return this.checkCumulativeCondition(achievement, userStats);
      case 'streak':
        return this.checkStreakCondition(achievement, userStats);
      case 'complex':
        return condition.customCheck && userStats ? condition.customCheck(userStats, gameResult) : false;
      default:
        return false;
    }
  }

  /**
   * 私有方法：检查单次条件
   */
  private checkSingleCondition(achievement: Achievement, gameResult?: GameResult): boolean {
    if (!gameResult) return false;

    const condition = achievement.condition;

    // 检查游戏ID限制
    if (condition.gameId && gameResult.gameId !== condition.gameId) {
      return false;
    }

    // 检查难度限制
    if (condition.difficulty && gameResult.difficulty !== condition.difficulty) {
      return false;
    }

    switch (achievement.type) {
      case 'score':
        return gameResult.score >= condition.target;
      case 'accuracy':
        return (gameResult.accuracy || 0) >= condition.target;
      case 'speed':
        return gameResult.duration <= condition.target;
      case 'combo':
        return (gameResult.maxCombo || 0) >= condition.target;
      default:
        return false;
    }
  }

  /**
   * 私有方法：检查累计条件
   */
  private checkCumulativeCondition(achievement: Achievement, userStats?: Record<string, unknown>): boolean {
    if (!userStats) return false;

    switch (achievement.type) {
      case 'score':
        return (userStats.totalScore as number) >= achievement.condition.target;
      case 'games_played': {
        const totalGames = Object.values((userStats.gamesPlayed as Record<string, number>) || {}).reduce(
          (sum: number, count: number) => sum + count,
          0
        );
        return totalGames >= achievement.condition.target;
      }
      default:
        return false;
    }
  }

  /**
   * 私有方法：检查连续条件
   */
  private checkStreakCondition(achievement: Achievement, userStats?: Record<string, unknown>): boolean {
    if (!userStats) return false;

    // 这里需要根据具体的连续条件实现
    // 例如连续登录天数等
    return ((userStats.statistics as Record<string, unknown>)?.currentStreak as number) >= achievement.condition.target;
  }

  /**
   * 私有方法：解锁成就
   */
  private async unlockAchievement(achievementId: string, userId: string): Promise<void> {
    const achievement = this.achievements.get(achievementId);
    if (!achievement) return;

    const userAchievement: UserAchievement = {
      achievementId,
      unlockedAt: Date.now(),
      progress: 100,
      isCompleted: true,
      currentValue: achievement.condition.target,
      targetValue: achievement.condition.target,
    };

    this.userAchievements.set(achievementId, userAchievement);
    this.dailyUnlocked.add(achievementId);

    // 发布成就解锁事件
    const achievementEvent = eventBus.createAchievementUnlockEvent(
      userId,
      achievementId,
      achievement.name,
      achievement.rewards || []
    );
    await eventBus.emit(achievementEvent);
  }

  /**
   * 私有方法：更新成就进度
   */
  private async updateAchievementProgress(
    achievement: Achievement,
    _gameResult?: GameResult,
    userStats?: Record<string, unknown>
  ): Promise<void> {
    // 只有累计类型的成就需要更新进度
    if (achievement.condition.type !== 'cumulative') return;

    let currentValue = 0;
    switch (achievement.type) {
      case 'score':
        currentValue = (userStats?.totalScore as number) || 0;
        break;
      case 'games_played':
        currentValue = Object.values((userStats?.gamesPlayed as Record<string, number>) || {}).reduce(
          (sum: number, count: number) => sum + count,
          0
        );
        break;
    }

    const progress = Math.min((currentValue / achievement.condition.target) * 100, 100);

    const userAchievement: UserAchievement = {
      achievementId: achievement.id,
      unlockedAt: 0,
      progress,
      isCompleted: false,
      currentValue,
      targetValue: achievement.condition.target,
    };

    this.userAchievements.set(achievement.id, userAchievement);
  }

  /**
   * 私有方法：检查成就是否已完成
   */
  private isAchievementCompleted(achievementId: string): boolean {
    const userAchievement = this.userAchievements.get(achievementId);
    return userAchievement?.isCompleted || false;
  }

  /**
   * 加载用户成就数据
   */
  loadUserAchievements(achievements: UserAchievement[]): void {
    this.userAchievements.clear();
    for (const achievement of achievements) {
      this.userAchievements.set(achievement.achievementId, achievement);
    }
  }

  /**
   * 保存用户成就数据
   */
  saveUserAchievements(): UserAchievement[] {
    return Array.from(this.userAchievements.values());
  }

  /**
   * 重置每日限制
   */
  resetDailyLimit(): void {
    this.dailyUnlocked.clear();
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<AchievementConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

// 导出默认实例
export const achievementEngine = new AchievementEngine();
