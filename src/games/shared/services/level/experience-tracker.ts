/**
 * 经验值追踪器
 * 管理用户经验值的获得、计算和历史记录
 */

import { eventBus } from '../user/user-events';
import type { GameResult } from '@/types/game';

// 经验值来源类型
export interface ExperienceSource {
  type:
    | 'game_completion'
    | 'score_achievement'
    | 'accuracy_bonus'
    | 'speed_bonus'
    | 'combo_bonus'
    | 'daily_bonus'
    | 'achievement_unlock'
    | 'special_event';
  gameId?: string;
  amount: number;
  description: string;
  timestamp: number;
  multiplier?: number;
}

// 经验值记录
export interface ExperienceRecord {
  id: string;
  userId: string;
  source: ExperienceSource;
  totalExperienceBefore: number;
  totalExperienceAfter: number;
  levelBefore: number;
  levelAfter: number;
  isLevelUp: boolean;
}

// 经验值配置
export interface ExperienceConfig {
  baseGameCompletion: number;
  scoreMultiplier: number;
  accuracyBonusThreshold: number;
  accuracyBonusAmount: number;
  speedBonusThreshold: number; // 秒
  speedBonusAmount: number;
  comboBonusMultiplier: number;
  dailyBonusAmount: number;
  achievementBonusAmount: number;
  maxDailyExperience: number;
}

// 默认经验值配置
const DEFAULT_EXPERIENCE_CONFIG: ExperienceConfig = {
  baseGameCompletion: 50,
  scoreMultiplier: 0.1, // 每100分获得10经验
  accuracyBonusThreshold: 90, // 90%以上准确率
  accuracyBonusAmount: 30,
  speedBonusThreshold: 30, // 30秒内完成
  speedBonusAmount: 20,
  comboBonusMultiplier: 2, // 连击数 * 2
  dailyBonusAmount: 100,
  achievementBonusAmount: 50,
  maxDailyExperience: 1000,
};

// 经验值追踪器
export class ExperienceTracker {
  private config: ExperienceConfig;
  private experienceHistory: ExperienceRecord[] = [];
  private dailyExperience: Map<string, number> = new Map(); // 日期 -> 经验值

  constructor(config: ExperienceConfig = DEFAULT_EXPERIENCE_CONFIG) {
    this.config = config;
  }

  /**
   * 计算游戏结果的经验值
   */
  calculateGameExperience(gameResult: GameResult, userContext?: Record<string, unknown>): ExperienceSource[] {
    const sources: ExperienceSource[] = [];
    const timestamp = Date.now();

    // 基础游戏完成经验
    sources.push({
      type: 'game_completion',
      gameId: gameResult.gameId,
      amount: this.config.baseGameCompletion,
      description: '完成游戏',
      timestamp,
    });

    // 分数经验
    const scoreExp = Math.floor(gameResult.score * this.config.scoreMultiplier);
    if (scoreExp > 0) {
      sources.push({
        type: 'score_achievement',
        gameId: gameResult.gameId,
        amount: scoreExp,
        description: `获得 ${gameResult.score} 分`,
        timestamp,
      });
    }

    // 准确率奖励
    if (gameResult.accuracy && gameResult.accuracy >= this.config.accuracyBonusThreshold) {
      sources.push({
        type: 'accuracy_bonus',
        gameId: gameResult.gameId,
        amount: this.config.accuracyBonusAmount,
        description: `高准确率奖励 (${gameResult.accuracy}%)`,
        timestamp,
      });
    }

    // 速度奖励
    if (gameResult.duration <= this.config.speedBonusThreshold) {
      sources.push({
        type: 'speed_bonus',
        gameId: gameResult.gameId,
        amount: this.config.speedBonusAmount,
        description: `快速完成奖励 (${gameResult.duration}秒)`,
        timestamp,
      });
    }

    // 连击奖励
    if (gameResult.maxCombo && gameResult.maxCombo > 3) {
      const comboExp = gameResult.maxCombo * this.config.comboBonusMultiplier;
      sources.push({
        type: 'combo_bonus',
        gameId: gameResult.gameId,
        amount: comboExp,
        description: `连击奖励 (${gameResult.maxCombo} 连击)`,
        timestamp,
      });
    }

    // 每日首次游戏奖励
    if (userContext?.isFirstGameToday) {
      sources.push({
        type: 'daily_bonus',
        gameId: gameResult.gameId,
        amount: this.config.dailyBonusAmount,
        description: '每日首次游戏奖励',
        timestamp,
      });
    }

    return sources;
  }

  /**
   * 添加经验值
   */
  async addExperience(
    userId: string,
    sources: ExperienceSource[],
    currentExperience: number,
    currentLevel: number,
    levelCalculator: { calculateLevel: (exp: number) => number }
  ): Promise<{
    totalExperienceGained: number;
    newTotalExperience: number;
    levelUp: boolean;
    newLevel: number;
    records: ExperienceRecord[];
  }> {
    const records: ExperienceRecord[] = [];
    let totalExperienceGained = 0;
    let runningTotal = currentExperience;
    let runningLevel = currentLevel;

    // 检查每日经验限制
    const today = new Date().toDateString();
    const todayExp = this.dailyExperience.get(today) || 0;

    for (const source of sources) {
      // 应用倍数
      let expAmount = source.amount;
      if (source.multiplier) {
        expAmount = Math.floor(expAmount * source.multiplier);
      }

      // 检查每日限制
      if (todayExp + totalExperienceGained + expAmount > this.config.maxDailyExperience) {
        expAmount = Math.max(0, this.config.maxDailyExperience - todayExp - totalExperienceGained);
        if (expAmount === 0) break; // 达到每日上限
      }

      const newTotal = runningTotal + expAmount;
      const newLevel = levelCalculator.calculateLevel(newTotal);
      const isLevelUp = newLevel.level > runningLevel;

      const record: ExperienceRecord = {
        id: this.generateRecordId(),
        userId,
        source: { ...source, amount: expAmount },
        totalExperienceBefore: runningTotal,
        totalExperienceAfter: newTotal,
        levelBefore: runningLevel,
        levelAfter: newLevel.level,
        isLevelUp,
      };

      records.push(record);
      this.experienceHistory.push(record);

      totalExperienceGained += expAmount;
      runningTotal = newTotal;
      runningLevel = newLevel.level;

      // 发布经验获得事件
      await this.publishExperienceEvent(userId, record);

      if (isLevelUp) {
        // 发布等级提升事件
        const levelUpEvent = eventBus.createLevelUpEvent(
          userId,
          record.levelBefore,
          record.levelAfter,
          newTotal,
          [] // 奖励将在等级系统中处理
        );
        await eventBus.emit(levelUpEvent);
      }
    }

    // 更新每日经验记录
    this.dailyExperience.set(today, todayExp + totalExperienceGained);

    return {
      totalExperienceGained,
      newTotalExperience: runningTotal,
      levelUp: runningLevel > currentLevel,
      newLevel: runningLevel,
      records,
    };
  }

  /**
   * 获取经验值历史
   */
  getExperienceHistory(userId: string, limit: number = 50): ExperienceRecord[] {
    return this.experienceHistory
      .filter((record) => record.userId === userId)
      .sort((a, b) => b.source.timestamp - a.source.timestamp)
      .slice(0, limit);
  }

  /**
   * 获取今日经验值统计
   */
  getTodayExperienceStats(userId: string): {
    totalGained: number;
    remaining: number;
    sources: { type: string; amount: number; count: number }[];
  } {
    const today = new Date().toDateString();
    const todayRecords = this.experienceHistory.filter(
      (record) => record.userId === userId && new Date(record.source.timestamp).toDateString() === today
    );

    const totalGained = todayRecords.reduce((sum, record) => sum + record.source.amount, 0);
    const remaining = Math.max(0, this.config.maxDailyExperience - totalGained);

    // 按来源统计
    const sourceStats = new Map<string, { amount: number; count: number }>();
    todayRecords.forEach((record) => {
      const type = record.source.type;
      const current = sourceStats.get(type) || { amount: 0, count: 0 };
      sourceStats.set(type, {
        amount: current.amount + record.source.amount,
        count: current.count + 1,
      });
    });

    const sources = Array.from(sourceStats.entries()).map(([type, stats]) => ({
      type,
      amount: stats.amount,
      count: stats.count,
    }));

    return { totalGained, remaining, sources };
  }

  /**
   * 获取经验值来源统计
   */
  getExperienceSourceStats(
    userId: string,
    days: number = 7
  ): {
    totalExperience: number;
    averageDaily: number;
    sourceBreakdown: { type: string; amount: number; percentage: number }[];
  } {
    const cutoffTime = Date.now() - days * 24 * 60 * 60 * 1000;
    const recentRecords = this.experienceHistory.filter(
      (record) => record.userId === userId && record.source.timestamp >= cutoffTime
    );

    const totalExperience = recentRecords.reduce((sum, record) => sum + record.source.amount, 0);
    const averageDaily = totalExperience / days;

    // 按来源分组
    const sourceMap = new Map<string, number>();
    recentRecords.forEach((record) => {
      const type = record.source.type;
      sourceMap.set(type, (sourceMap.get(type) || 0) + record.source.amount);
    });

    const sourceBreakdown = Array.from(sourceMap.entries()).map(([type, amount]) => ({
      type,
      amount,
      percentage: totalExperience > 0 ? (amount / totalExperience) * 100 : 0,
    }));

    return { totalExperience, averageDaily, sourceBreakdown };
  }

  /**
   * 检查是否可以获得每日奖励
   */
  canGetDailyBonus(userId: string): boolean {
    const today = new Date().toDateString();
    const todayRecords = this.experienceHistory.filter(
      (record) =>
        record.userId === userId &&
        new Date(record.source.timestamp).toDateString() === today &&
        record.source.type === 'daily_bonus'
    );
    return todayRecords.length === 0;
  }

  /**
   * 添加成就解锁经验
   */
  async addAchievementExperience(userId: string, achievementId: string, currentExperience: number): Promise<number> {
    const source: ExperienceSource = {
      type: 'achievement_unlock',
      amount: this.config.achievementBonusAmount,
      description: `解锁成就: ${achievementId}`,
      timestamp: Date.now(),
    };

    const record: ExperienceRecord = {
      id: this.generateRecordId(),
      userId,
      source,
      totalExperienceBefore: currentExperience,
      totalExperienceAfter: currentExperience + source.amount,
      levelBefore: 0, // 将在调用方计算
      levelAfter: 0, // 将在调用方计算
      isLevelUp: false,
    };

    this.experienceHistory.push(record);
    await this.publishExperienceEvent(userId, record);

    return source.amount;
  }

  /**
   * 私有方法：生成记录ID
   */
  private generateRecordId(): string {
    return 'exp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 私有方法：发布经验获得事件
   */
  private async publishExperienceEvent(userId: string, record: ExperienceRecord): Promise<void> {
    const experienceEvent = {
      type: 'EXPERIENCE_GAINED',
      timestamp: Date.now(),
      userId,
      data: {
        recordId: record.id,
        source: record.source,
        experienceGained: record.source.amount,
        totalExperience: record.totalExperienceAfter,
        isLevelUp: record.isLevelUp,
      },
    };
    await eventBus.emit(experienceEvent as Record<string, unknown>);
  }

  /**
   * 清理旧的经验记录
   */
  cleanupOldRecords(maxAge: number = 30 * 24 * 60 * 60 * 1000): void {
    const cutoffTime = Date.now() - maxAge;
    this.experienceHistory = this.experienceHistory.filter((record) => record.source.timestamp > cutoffTime);
  }

  /**
   * 加载经验历史数据
   */
  loadExperienceHistory(history: ExperienceRecord[]): void {
    this.experienceHistory = history;
  }

  /**
   * 保存经验历史数据
   */
  saveExperienceHistory(): ExperienceRecord[] {
    return [...this.experienceHistory];
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<ExperienceConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 获取配置
   */
  getConfig(): ExperienceConfig {
    return { ...this.config };
  }
}

// 导出默认实例
export const experienceTracker = new ExperienceTracker();
