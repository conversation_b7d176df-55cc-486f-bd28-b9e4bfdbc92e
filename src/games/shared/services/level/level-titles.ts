/**
 * 统一的等级系统配置
 * 包含每个等级的称号和图标，避免在多个文件中重复定义
 */

// 等级信息定义
export interface LevelInfo {
  title: string;
  icon: string;
}

// 等级称号和图标映射表
export const LEVEL_CONFIG: Record<number, LevelInfo> = {
  1: { title: '初学者', icon: '🌱' }, // 1级默认
  5: { title: '易学新手', icon: '🌿' }, // 5级解锁
  10: { title: '勤学者', icon: '🍃' }, // 10级解锁
  15: { title: '小有所成', icon: '🌾' }, // 15级解锁
  20: { title: '颇有心得', icon: '🌳' }, // 20级解锁
  25: { title: '融会贯通', icon: '🎋' }, // 25级解锁
  30: { title: '登堂入室', icon: '🎍' }, // 30级解锁
  40: { title: '炉火纯青', icon: '⭐' }, // 40级解锁
  50: { title: '易学大师', icon: '🎖️' }, // 50级解锁
  70: { title: '易学宗师', icon: '🏆' }, // 70级解锁
  100: { title: '易学泰斗', icon: '👑' }, // 100级解锁
};

// 兼容性：保留原有的称号映射（向后兼容）
export const LEVEL_TITLES: Record<number, string> = Object.fromEntries(
  Object.entries(LEVEL_CONFIG).map(([level, info]) => [level, info.title])
);

// 称号解锁等级列表（按等级排序）
export const TITLE_UNLOCK_LEVELS = Object.keys(LEVEL_TITLES)
  .map(Number)
  .sort((a, b) => a - b);

/**
 * 根据等级获取对应的称号
 * @param level 用户等级
 * @returns 对应的称号
 */
export function getLevelTitle(level: number): string {
  // 查找最高的已解锁称号
  const availableLevels = TITLE_UNLOCK_LEVELS.filter((titleLevel) => titleLevel <= level);

  if (availableLevels.length > 0) {
    const highestLevel = availableLevels[availableLevels.length - 1];
    return LEVEL_CONFIG[highestLevel].title;
  }

  return '初学者'; // 默认称号
}

/**
 * 根据等级获取对应的图标
 * @param level 用户等级
 * @returns 对应的图标
 */
export function getLevelIcon(level: number): string {
  // 查找最高的已解锁等级
  const availableLevels = TITLE_UNLOCK_LEVELS.filter((titleLevel) => titleLevel <= level);

  if (availableLevels.length > 0) {
    const highestLevel = availableLevels[availableLevels.length - 1];
    return LEVEL_CONFIG[highestLevel].icon;
  }

  return '🌱'; // 默认图标
}

/**
 * 根据等级获取完整的等级信息
 * @param level 用户等级
 * @returns 等级信息对象
 */
export function getLevelInfo(level: number): LevelInfo {
  // 查找最高的已解锁等级
  const availableLevels = TITLE_UNLOCK_LEVELS.filter((titleLevel) => titleLevel <= level);

  if (availableLevels.length > 0) {
    const highestLevel = availableLevels[availableLevels.length - 1];
    return LEVEL_CONFIG[highestLevel];
  }

  return { title: '初学者', icon: '🌱' }; // 默认信息
}

/**
 * 检查指定等级是否有新称号解锁
 * @param level 等级
 * @returns 是否有新称号解锁
 */
export function hasNewTitleAtLevel(level: number): boolean {
  return Object.prototype.hasOwnProperty.call(LEVEL_TITLES, level);
}

/**
 * 获取下一个称号解锁等级
 * @param currentLevel 当前等级
 * @returns 下一个称号解锁等级，如果已是最高等级则返回null
 */
export function getNextTitleLevel(currentLevel: number): number | null {
  const nextLevels = TITLE_UNLOCK_LEVELS.filter((level) => level > currentLevel);
  return nextLevels.length > 0 ? nextLevels[0] : null;
}

/**
 * 获取所有称号信息
 * @returns 称号信息数组
 */
export function getAllTitles(): Array<{ level: number; title: string; unlocked: boolean }> {
  return TITLE_UNLOCK_LEVELS.map((level) => ({
    level,
    title: LEVEL_TITLES[level],
    unlocked: false, // 需要根据用户等级动态设置
  }));
}

/**
 * 获取用户已解锁的称号列表
 * @param userLevel 用户等级
 * @returns 已解锁的称号列表
 */
export function getUnlockedTitles(userLevel: number): Array<{ level: number; title: string }> {
  return TITLE_UNLOCK_LEVELS.filter((level) => level <= userLevel).map((level) => ({
    level,
    title: LEVEL_TITLES[level],
  }));
}

/**
 * 转换为奖励系统格式
 * @returns 奖励系统所需的格式
 */
export function getTitleRewardsConfig(): Record<number, string[]> {
  const config: Record<number, string[]> = {};

  // 跳过1级的默认称号，只包含需要解锁的称号
  TITLE_UNLOCK_LEVELS.forEach((level) => {
    if (level > 1) {
      // 跳过默认称号
      config[level] = [LEVEL_TITLES[level]];
    }
  });

  return config;
}
