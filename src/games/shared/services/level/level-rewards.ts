/**
 * 等级奖励系统
 * 管理等级提升时的奖励发放和特权解锁
 */

import { eventBus } from '../user/user-events';
import { getTitleRewardsConfig } from './level-titles';

// 奖励类型定义
export interface RewardItem {
  id: string;
  type: 'title' | 'badge' | 'feature' | 'multiplier' | 'currency' | 'unlock';
  name: string;
  description: string;
  icon: string;
  value?: string | number;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  isActive: boolean;
  unlockedAt: number;
}

export interface UserRewards {
  titles: RewardItem[];
  badges: RewardItem[];
  features: RewardItem[];
  multipliers: RewardItem[];
  activeTitleId?: string;
  activeBadgeId?: string;
}

// 奖励配置
export interface RewardConfig {
  titleRewards: Record<number, string[]>;
  badgeRewards: Record<number, string[]>;
  featureRewards: Record<number, string[]>;
  multiplierRewards: Record<number, number>;
}

// 默认奖励配置
const DEFAULT_REWARD_CONFIG: RewardConfig = {
  titleRewards: getTitleRewardsConfig(),
  badgeRewards: {
    10: ['青铜徽章'],
    25: ['白银徽章'],
    50: ['黄金徽章'],
    75: ['铂金徽章'],
    100: ['钻石徽章'],
  },
  featureRewards: {
    5: ['自定义头像'],
    10: ['游戏统计详情'],
    15: ['成就进度追踪'],
    20: ['高级排行榜'],
    30: ['专属游戏模式'],
    50: ['大师挑战赛'],
  },
  multiplierRewards: {
    20: 1.1,
    40: 1.2,
    60: 1.3,
    80: 1.5,
    100: 2.0,
  },
};

// 等级奖励管理器
export class LevelRewardManager {
  private config: RewardConfig;
  private userRewards: UserRewards;

  constructor(config: RewardConfig = DEFAULT_REWARD_CONFIG) {
    this.config = config;
    this.userRewards = this.initializeUserRewards();
  }

  /**
   * 处理等级提升奖励
   */
  async processLevelUpRewards(oldLevel: number, newLevel: number, userId: string): Promise<RewardItem[]> {
    const newRewards: RewardItem[] = [];

    // 检查每个等级的奖励
    for (let level = oldLevel + 1; level <= newLevel; level++) {
      const levelRewards = await this.getLevelRewards(level);
      newRewards.push(...levelRewards);
    }

    // 添加奖励到用户奖励列表
    for (const reward of newRewards) {
      this.addRewardToUser(reward);
    }

    // 发布奖励获得事件
    if (newRewards.length > 0) {
      await this.publishRewardEvents(userId, newRewards);
    }

    return newRewards;
  }

  /**
   * 获取指定等级的奖励
   */
  async getLevelRewards(level: number): Promise<RewardItem[]> {
    const rewards: RewardItem[] = [];
    const timestamp = Date.now();

    // 称号奖励
    if (this.config.titleRewards[level]) {
      for (const titleName of this.config.titleRewards[level]) {
        rewards.push({
          id: `title_${level}_${titleName}`,
          type: 'title',
          name: titleName,
          description: `等级 ${level} 解锁专属称号`,
          icon: this.getTitleIcon(level),
          rarity: this.getTitleRarity(level),
          isActive: false,
          unlockedAt: timestamp,
        });
      }
    }

    // 徽章奖励
    if (this.config.badgeRewards[level]) {
      for (const badgeName of this.config.badgeRewards[level]) {
        rewards.push({
          id: `badge_${level}_${badgeName}`,
          type: 'badge',
          name: badgeName,
          description: `等级 ${level} 解锁专属徽章`,
          icon: this.getBadgeIcon(level),
          rarity: this.getBadgeRarity(level),
          isActive: false,
          unlockedAt: timestamp,
        });
      }
    }

    // 功能奖励
    if (this.config.featureRewards[level]) {
      for (const featureName of this.config.featureRewards[level]) {
        rewards.push({
          id: `feature_${level}_${featureName}`,
          type: 'feature',
          name: featureName,
          description: `等级 ${level} 解锁新功能`,
          icon: this.getFeatureIcon(featureName),
          rarity: 'rare',
          isActive: true, // 功能奖励自动激活
          unlockedAt: timestamp,
        });
      }
    }

    // 倍数奖励
    if (this.config.multiplierRewards[level]) {
      const multiplier = this.config.multiplierRewards[level];
      rewards.push({
        id: `multiplier_${level}`,
        type: 'multiplier',
        name: `积分倍数 x${multiplier}`,
        description: `积分获得倍数提升至 ${multiplier} 倍`,
        icon: '⚡',
        value: multiplier,
        rarity: 'epic',
        isActive: true, // 倍数奖励自动激活
        unlockedAt: timestamp,
      });
    }

    return rewards;
  }

  /**
   * 获取用户当前奖励
   */
  getUserRewards(): UserRewards {
    return { ...this.userRewards };
  }

  /**
   * 激活称号
   */
  activateTitle(titleId: string): boolean {
    const title = this.userRewards.titles.find((t) => t.id === titleId);
    if (title) {
      // 取消之前激活的称号
      if (this.userRewards.activeTitleId) {
        const oldTitle = this.userRewards.titles.find((t) => t.id === this.userRewards.activeTitleId);
        if (oldTitle) oldTitle.isActive = false;
      }

      // 激活新称号
      title.isActive = true;
      this.userRewards.activeTitleId = titleId;
      return true;
    }
    return false;
  }

  /**
   * 激活徽章
   */
  activateBadge(badgeId: string): boolean {
    const badge = this.userRewards.badges.find((b) => b.id === badgeId);
    if (badge) {
      // 取消之前激活的徽章
      if (this.userRewards.activeBadgeId) {
        const oldBadge = this.userRewards.badges.find((b) => b.id === this.userRewards.activeBadgeId);
        if (oldBadge) oldBadge.isActive = false;
      }

      // 激活新徽章
      badge.isActive = true;
      this.userRewards.activeBadgeId = badgeId;
      return true;
    }
    return false;
  }

  /**
   * 获取当前积分倍数
   */
  getCurrentMultiplier(): number {
    const activeMultipliers = this.userRewards.multipliers.filter((m) => m.isActive);
    if (activeMultipliers.length === 0) return 1.0;

    // 返回最高的倍数
    return Math.max(...activeMultipliers.map((m) => (typeof m.value === 'number' ? m.value : 1.0)));
  }

  /**
   * 检查功能是否解锁
   */
  isFeatureUnlocked(featureName: string): boolean {
    return this.userRewards.features.some((f) => f.name === featureName && f.isActive);
  }

  /**
   * 获取奖励统计
   */
  getRewardStats(): {
    totalTitles: number;
    totalBadges: number;
    totalFeatures: number;
    currentMultiplier: number;
    activeTitle?: string;
    activeBadge?: string;
  } {
    return {
      totalTitles: this.userRewards.titles.length,
      totalBadges: this.userRewards.badges.length,
      totalFeatures: this.userRewards.features.length,
      currentMultiplier: this.getCurrentMultiplier(),
      activeTitle: this.userRewards.titles.find((t) => t.isActive)?.name,
      activeBadge: this.userRewards.badges.find((b) => b.isActive)?.name,
    };
  }

  /**
   * 私有方法：初始化用户奖励
   */
  private initializeUserRewards(): UserRewards {
    return {
      titles: [],
      badges: [],
      features: [],
      multipliers: [],
    };
  }

  /**
   * 私有方法：添加奖励到用户
   */
  private addRewardToUser(reward: RewardItem): void {
    switch (reward.type) {
      case 'title':
        this.userRewards.titles.push(reward);
        break;
      case 'badge':
        this.userRewards.badges.push(reward);
        break;
      case 'feature':
        this.userRewards.features.push(reward);
        break;
      case 'multiplier':
        this.userRewards.multipliers.push(reward);
        break;
    }
  }

  /**
   * 私有方法：发布奖励事件
   */
  private async publishRewardEvents(userId: string, rewards: RewardItem[]): Promise<void> {
    for (const reward of rewards) {
      // 发布通用奖励事件
      const rewardEvent = {
        type: 'REWARD_UNLOCKED',
        timestamp: Date.now(),
        userId,
        data: {
          rewardId: reward.id,
          rewardType: reward.type,
          rewardName: reward.name,
          rewardDescription: reward.description,
          rarity: reward.rarity,
        },
      };
      await eventBus.emit(rewardEvent);

      // 发布特定类型的事件
      if (reward.type === 'title') {
        const titleEvent = {
          type: 'TITLE_UNLOCKED',
          timestamp: Date.now(),
          userId,
          data: {
            titleId: reward.id,
            titleName: reward.name,
          },
        };
        await eventBus.emit(titleEvent);
      }
    }
  }

  /**
   * 私有方法：获取称号图标
   */
  private getTitleIcon(level: number): string {
    if (level >= 70) return '👑';
    if (level >= 50) return '🎖️';
    if (level >= 30) return '⭐';
    if (level >= 15) return '🌟';
    return '🏅';
  }

  /**
   * 私有方法：获取称号稀有度
   */
  private getTitleRarity(level: number): 'common' | 'rare' | 'epic' | 'legendary' {
    if (level >= 70) return 'legendary';
    if (level >= 50) return 'epic';
    if (level >= 25) return 'rare';
    return 'common';
  }

  /**
   * 私有方法：获取徽章图标
   */
  private getBadgeIcon(level: number): string {
    if (level >= 100) return '💎';
    if (level >= 75) return '🏆';
    if (level >= 50) return '🥇';
    if (level >= 25) return '🥈';
    return '🥉';
  }

  /**
   * 私有方法：获取徽章稀有度
   */
  private getBadgeRarity(level: number): 'common' | 'rare' | 'epic' | 'legendary' {
    if (level >= 100) return 'legendary';
    if (level >= 75) return 'epic';
    if (level >= 50) return 'rare';
    return 'common';
  }

  /**
   * 私有方法：获取功能图标
   */
  private getFeatureIcon(featureName: string): string {
    const iconMap: Record<string, string> = {
      自定义头像: '🎨',
      游戏统计详情: '📊',
      成就进度追踪: '📈',
      高级排行榜: '🏆',
      专属游戏模式: '🎮',
      大师挑战赛: '⚔️',
    };
    return iconMap[featureName] || '🔓';
  }

  /**
   * 加载用户奖励数据
   */
  loadUserRewards(rewardsData: UserRewards): void {
    this.userRewards = rewardsData;
  }

  /**
   * 保存用户奖励数据
   */
  saveUserRewards(): UserRewards {
    return { ...this.userRewards };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<RewardConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

// 导出默认实例
export const levelRewardManager = new LevelRewardManager();
