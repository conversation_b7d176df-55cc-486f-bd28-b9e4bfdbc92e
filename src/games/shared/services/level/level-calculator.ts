/**
 * 等级计算器
 * 提供多维度等级体系和等级奖励机制
 */

// import { eventBus } from '../user/user-events';
import { getLevelTitle, getLevelIcon } from './level-titles';

// 等级类型定义
export interface UserLevel {
  level: number;
  title: string;
  description: string;
  minExperience: number;
  maxExperience: number;
  color: string;
  icon: string;
  rewards: LevelReward[];
}

export interface LevelReward {
  type: 'title' | 'badge' | 'feature' | 'multiplier';
  name: string;
  description: string;
  value?: string | number;
}

export interface GameSpecificLevel {
  gameId: string;
  level: number;
  experience: number;
  gamesPlayed: number;
  bestScore: number;
  averageScore: number;
}

export interface SkillLevel {
  skillType: 'speed' | 'accuracy' | 'consistency' | 'endurance';
  level: number;
  experience: number;
  description: string;
}

// 等级配置
export interface LevelConfig {
  baseExperienceRequired: number;
  experienceGrowthRate: number;
  maxLevel: number;
  levelTitles: string[];
  skillMultipliers: Record<string, number>;
}

// 默认等级配置
const DEFAULT_LEVEL_CONFIG: LevelConfig = {
  baseExperienceRequired: 1000,
  experienceGrowthRate: 1.2,
  maxLevel: 100,
  levelTitles: [], // 不再使用，改用LEVEL_TITLE_MAP
  skillMultipliers: {
    speed: 1.0,
    accuracy: 1.2,
    consistency: 1.1,
    endurance: 0.9,
  },
};

// 等级计算器
export class LevelCalculator {
  private config: LevelConfig;
  private levelCache: Map<number, UserLevel> = new Map();

  constructor(config: LevelConfig = DEFAULT_LEVEL_CONFIG) {
    this.config = config;
    this.initializeLevelCache();
  }

  /**
   * 根据经验值计算等级
   */
  calculateLevel(experience: number): UserLevel {
    let level = 1;
    let totalExpRequired = 0;

    // 计算当前等级
    while (level < this.config.maxLevel) {
      const expForThisLevel = this.getExperienceRequiredForLevel(level);
      if (totalExpRequired + expForThisLevel > experience) {
        break;
      }
      totalExpRequired += expForThisLevel;
      level++;
    }

    return this.getLevelInfo(level);
  }

  /**
   * 获取等级信息
   */
  getLevelInfo(level: number): UserLevel {
    // 检查缓存
    const cacheKey = level;
    if (this.levelCache.has(cacheKey)) {
      const cachedLevel = this.levelCache.get(cacheKey)!;
      return {
        ...cachedLevel,
        minExperience: this.getTotalExperienceForLevel(level),
        maxExperience: level === 1 ? this.getExperienceRequiredForLevel(1) : this.getTotalExperienceForLevel(level + 1),
      };
    }

    const title = getLevelTitle(level);
    const icon = getLevelIcon(level);
    const rewards = this.getLevelRewards(level);

    const levelInfo: UserLevel = {
      level,
      title,
      description: `${title} - 等级 ${level}`,
      minExperience: this.getTotalExperienceForLevel(level),
      maxExperience: level === 1 ? this.getExperienceRequiredForLevel(1) : this.getTotalExperienceForLevel(level + 1),
      color: '#8b4513', // 统一使用主题色
      icon,
      rewards,
    };

    this.levelCache.set(cacheKey, levelInfo);
    return levelInfo;
  }

  /**
   * 计算升级所需经验
   */
  getExperienceToNextLevel(currentExperience: number): number {
    const currentLevel = this.calculateLevel(currentExperience);
    if (currentLevel.level >= this.config.maxLevel) {
      return 0; // 已达到最高等级
    }

    return currentLevel.maxExperience - currentExperience;
  }

  /**
   * 计算等级进度百分比
   */
  getLevelProgress(currentExperience: number): number {
    const currentLevel = this.calculateLevel(currentExperience);
    const levelExp = currentExperience - currentLevel.minExperience;
    const levelExpRequired = currentLevel.maxExperience - currentLevel.minExperience;

    return levelExpRequired > 0 ? (levelExp / levelExpRequired) * 100 : 100;
  }

  /**
   * 检查是否升级
   */
  checkLevelUp(oldExperience: number, newExperience: number): UserLevel | null {
    const oldLevel = this.calculateLevel(oldExperience);
    const newLevel = this.calculateLevel(newExperience);

    if (newLevel.level > oldLevel.level) {
      return newLevel;
    }

    return null;
  }

  /**
   * 计算游戏专项等级
   */
  calculateGameLevel(
    gameId: string,
    gameStats: {
      gamesPlayed: number;
      totalScore: number;
      bestScore: number;
      averageAccuracy: number;
    }
  ): GameSpecificLevel {
    // 基于游戏次数和表现计算专项等级
    const baseExp = gameStats.gamesPlayed * 50;
    const scoreExp = Math.floor(gameStats.totalScore / 100);
    const accuracyBonus = Math.floor(gameStats.averageAccuracy * 10);

    const totalExp = baseExp + scoreExp + accuracyBonus;
    const level = Math.floor(totalExp / 500) + 1;

    return {
      gameId,
      level: Math.min(level, 50), // 游戏专项等级最高50级
      experience: totalExp,
      gamesPlayed: gameStats.gamesPlayed,
      bestScore: gameStats.bestScore,
      averageScore: gameStats.gamesPlayed > 0 ? gameStats.totalScore / gameStats.gamesPlayed : 0,
    };
  }

  /**
   * 计算技能等级
   */
  calculateSkillLevel(
    skillType: 'speed' | 'accuracy' | 'consistency' | 'endurance',
    skillData: {
      totalGames: number;
      skillValue: number; // 技能相关的数值（如平均速度、准确率等）
    }
  ): SkillLevel {
    const multiplier = this.config.skillMultipliers[skillType] || 1.0;
    const baseExp = skillData.totalGames * 20;
    const skillExp = Math.floor(skillData.skillValue * multiplier * 10);

    const totalExp = baseExp + skillExp;
    const level = Math.floor(totalExp / 300) + 1;

    const descriptions = {
      speed: '反应速度和操作效率',
      accuracy: '答题准确率和精确度',
      consistency: '稳定性和持续表现',
      endurance: '持久力和专注度',
    };

    return {
      skillType,
      level: Math.min(level, 20), // 技能等级最高20级
      experience: totalExp,
      description: descriptions[skillType],
    };
  }

  /**
   * 获取等级奖励
   */
  getLevelRewards(level: number): LevelReward[] {
    const rewards: LevelReward[] = [];

    // 每5级获得称号奖励
    if (level % 5 === 0) {
      const title = getLevelTitle(level);
      rewards.push({
        type: 'title',
        name: `${title}称号`,
        description: `解锁专属称号：${title}`,
      });
    }

    // 每10级获得功能奖励
    if (level % 10 === 0) {
      rewards.push({
        type: 'feature',
        name: '新功能解锁',
        description: '解锁高级游戏功能或特殊模式',
      });
    }

    // 每20级获得积分倍数奖励
    if (level % 20 === 0) {
      rewards.push({
        type: 'multiplier',
        name: '积分倍数提升',
        description: '游戏积分获得倍数永久提升',
        value: 1 + (level / 20) * 0.1,
      });
    }

    // 特殊等级奖励
    if (level === 50) {
      rewards.push({
        type: 'badge',
        name: '大师徽章',
        description: '获得易学大师专属徽章',
      });
    }

    if (level === 100) {
      rewards.push({
        type: 'badge',
        name: '宗师徽章',
        description: '获得易学宗师至高荣誉',
      });
    }

    return rewards;
  }

  /**
   * 私有方法：获取指定等级所需的经验值
   */
  private getExperienceRequiredForLevel(level: number): number {
    return Math.floor(this.config.baseExperienceRequired * Math.pow(this.config.experienceGrowthRate, level - 1));
  }

  /**
   * 私有方法：获取到达指定等级的总经验值
   */
  private getTotalExperienceForLevel(level: number): number {
    if (level <= 1) return 0;

    let total = 0;
    for (let i = 1; i < level; i++) {
      total += this.getExperienceRequiredForLevel(i);
    }
    return total;
  }

  /**
   * 获取等级的最大经验值（修复等级1的问题）
   */
  private getMaxExperienceForLevel(level: number): number {
    if (level === 1) {
      // 等级1的最大经验值应该是升到等级2所需的经验
      return this.getExperienceRequiredForLevel(1);
    }
    return this.getTotalExperienceForLevel(level);
  }

  /**
   * 私有方法：初始化等级缓存
   */
  private initializeLevelCache(): void {
    // 预计算前50级的信息以提高性能
    for (let level = 1; level <= 50; level++) {
      const title = getLevelTitle(level);
      const icon = getLevelIcon(level);
      const rewards = this.getLevelRewards(level);

      this.levelCache.set(level, {
        level,
        title,
        description: `${title} - 等级 ${level}`,
        minExperience: 0, // 将在getLevelInfo中计算
        maxExperience: 0, // 将在getLevelInfo中计算
        color: '#8b4513', // 统一使用主题色
        icon,
        rewards,
      });
    }
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<LevelConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.levelCache.clear();
    this.initializeLevelCache();
  }

  /**
   * 获取当前配置
   */
  getConfig(): LevelConfig {
    return { ...this.config };
  }
}

// 导出默认实例
export const levelCalculator = new LevelCalculator();
