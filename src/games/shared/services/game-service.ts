/**
 * 统一游戏服务接口
 * 为所有游戏提供统一的服务入口点
 */

import { eventBus } from './user/user-events';
import { userProfileManager } from './user/user-profile';
import { GameDataManager } from './user/user-storage';
import { scoreCalculator } from './scoring/score-calculator';
import { rankingEngine } from './ranking/ranking-engine';
import { levelCalculator } from './level/level-calculator';
// import { levelRewardManager } from './level/level-rewards';
// import { experienceTracker } from './level/experience-tracker';
// import { achievementEngine } from './achievement/achievement-engine';
import type { GameResult } from '@/games/shared/types';
import type { UserProfile } from './user/user-profile';
import type { RankingEntry, RankingType } from './ranking/ranking-engine';
import type { ScoreBreakdown } from './scoring/score-calculator';
import type { UserLevel, GameSpecificLevel, SkillLevel } from './level/level-calculator';
import type { UserRewards } from './level/level-rewards';
import type { Achievement, UserAchievement } from './achievement/achievement-engine';

// 游戏服务接口
export interface GameServiceInterface {
  // 用户管理
  initializeUser(userId?: string): Promise<UserProfile>;
  getCurrentUser(): UserProfile | null;
  updateUserProfile(updates: Partial<UserProfile>): Promise<UserProfile>;
  updateUserPreferences(preferences: Record<string, unknown>): Promise<void>;

  // 游戏生命周期
  startGame(gameId: string, difficulty: string): Promise<void>;
  endGame(gameResult: GameResult): Promise<GameServiceResult>;

  // 积分和排名
  calculateScore(gameResult: GameResult): ScoreBreakdown;
  getRanking(type: string, gameId?: string, limit?: number): Promise<RankingEntry[]>;
  getUserRank(userId: string, type: string, gameId?: string): Promise<number>;

  // 成就系统
  checkAchievements(gameResult: GameResult): Promise<string[]>;
  getUserAchievements(): UserAchievement[];
  getAllAchievements(): Achievement[];
  getAchievementStats(): Record<string, unknown>;

  // 等级系统
  getUserLevel(): UserLevel | null;
  getGameLevel(gameId: string): GameSpecificLevel | null;
  getSkillLevel(skillType: string): SkillLevel | null;
  getUserRewards(): UserRewards | null;
  activateTitle(titleId: string): Promise<boolean>;
  activateBadge(badgeId: string): Promise<boolean>;
  getLevelProgress(): number;
  getExperienceToNextLevel(): number;

  // 统计数据
  getUserStats(): Record<string, unknown> | null;
  getGameStats(gameId: string): Record<string, unknown> | null;
}

// 游戏服务结果
export interface GameServiceResult {
  success: boolean;
  scoreBreakdown: ScoreBreakdown;
  newAchievements: string[];
  isNewRecord: boolean;
  rankingUpdates: {
    global: number;
    game: number;
    daily: number;
  };
  levelUp?: {
    oldLevel: number;
    newLevel: number;
    rewards: Record<string, unknown>[];
  };
}

// 游戏服务实现
export class GameService implements GameServiceInterface {
  private static instance: GameService;
  private initialized: boolean = false;

  static getInstance(): GameService {
    if (!GameService.instance) {
      GameService.instance = new GameService();
    }
    return GameService.instance;
  }

  /**
   * 初始化服务
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    // 注册事件监听器
    this.registerEventListeners();

    this.initialized = true;
  }

  /**
   * 初始化用户
   */
  async initializeUser(userId?: string): Promise<UserProfile> {
    await this.initialize();
    return await userProfileManager.initializeProfile(userId);
  }

  /**
   * 获取当前用户
   */
  getCurrentUser(): UserProfile | null {
    return userProfileManager.getCurrentProfile();
  }

  /**
   * 刷新用户数据（从存储重新加载）
   */
  async refreshUserData(): Promise<UserProfile | null> {
    return await userProfileManager.refreshCurrentProfile();
  }

  /**
   * 更新用户档案
   */
  async updateUserProfile(updates: Partial<UserProfile>): Promise<UserProfile> {
    return await userProfileManager.updateProfile(updates);
  }

  /**
   * 更新用户偏好设置
   */
  async updateUserPreferences(preferences: Record<string, unknown>): Promise<void> {
    await userProfileManager.updatePreferences(preferences);
  }

  /**
   * 开始游戏
   */
  async startGame(gameId: string, difficulty: string): Promise<void> {
    const user = this.getCurrentUser();
    if (!user) {
      throw new Error('用户未初始化');
    }

    // 发布游戏开始事件
    const gameStartEvent = eventBus.createGameStartEvent(user.userId, gameId, difficulty);
    await eventBus.emit(gameStartEvent);

    // 更新用户统计
    await userProfileManager.updateStatistics({
      lastGamePlayed: gameId,
    });
  }

  /**
   * 结束游戏
   */
  async endGame(gameResult: GameResult): Promise<GameServiceResult> {
    const user = this.getCurrentUser();
    if (!user) {
      throw new Error('用户未初始化');
    }

    // 计算积分详情（在处理前计算，获取完整上下文）
    const userContext = {
      gameHistory: user.bestScores,
      currentStreak: user.statistics.currentStreak,
      isFirstGameToday: this.isFirstGameToday(user),
      consecutiveDays: this.getConsecutiveDays(user),
      dailyGoalCompleted: this.isDailyGoalCompleted(user),
    };

    const scoreBreakdown = scoreCalculator.calculateScore(gameResult, userContext);

    // 使用游戏数据管理器处理游戏结果
    const gameDataManager = GameDataManager.getInstance();
    await gameDataManager.processGameResult(gameResult);

    // 检查新纪录
    const isNewRecord = await userProfileManager.updateBestScore(gameResult.gameId, gameResult.score);

    // 获取排名更新
    const rankingUpdates = {
      global: await rankingEngine.getUserRank(user.userId, 'global'),
      game: await rankingEngine.getUserRank(user.userId, 'game', gameResult.gameId),
      daily: await rankingEngine.getUserRank(user.userId, 'daily'),
    };

    // 检查等级提升
    const updatedUser = this.getCurrentUser();
    const levelUp =
      updatedUser && updatedUser.level > user.level
        ? {
            oldLevel: user.level,
            newLevel: updatedUser.level,
            rewards: [], // TODO: 实现等级奖励
          }
        : undefined;

    return {
      success: true,
      scoreBreakdown,
      newAchievements: [], // TODO: 实现成就检查
      isNewRecord,
      rankingUpdates,
      levelUp,
    };
  }

  /**
   * 计算积分
   */
  calculateScore(gameResult: GameResult): ScoreBreakdown {
    const user = this.getCurrentUser();
    const userContext = user
      ? {
          gameHistory: user.bestScores,
          currentStreak: user.statistics.currentStreak,
          isFirstGameToday: this.isFirstGameToday(user),
          consecutiveDays: this.getConsecutiveDays(user),
          dailyGoalCompleted: this.isDailyGoalCompleted(user),
        }
      : undefined;

    return scoreCalculator.calculateScore(gameResult, userContext);
  }

  /**
   * 获取排行榜
   */
  async getRanking(type: string, gameId?: string, limit: number = 100): Promise<RankingEntry[]> {
    return await rankingEngine.getRanking(type as RankingType, gameId, limit);
  }

  /**
   * 获取用户排名
   */
  async getUserRank(userId: string, type: string, gameId?: string): Promise<number> {
    return await rankingEngine.getUserRank(userId, type as RankingType, gameId);
  }

  /**
   * 检查成就
   */
  async checkAchievements(gameResult: GameResult): Promise<string[]> {
    return await userProfileManager.checkGameAchievements(gameResult);
  }

  /**
   * 获取用户成就
   */
  getUserAchievements(): UserAchievement[] {
    return userProfileManager.getUserAchievements();
  }

  /**
   * 获取所有可用成就
   */
  getAllAchievements(): Achievement[] {
    return userProfileManager.getAllAchievements();
  }

  /**
   * 获取成就统计
   */
  getAchievementStats(): Record<string, unknown> {
    return userProfileManager.getAchievementStats();
  }

  /**
   * 获取用户等级信息
   */
  getUserLevel(): UserLevel | null {
    return userProfileManager.getLevelInfo();
  }

  /**
   * 获取游戏专项等级
   */
  getGameLevel(gameId: string): GameSpecificLevel | null {
    return userProfileManager.getGameLevel(gameId);
  }

  /**
   * 获取技能等级
   */
  getSkillLevel(skillType: string): SkillLevel | null {
    return userProfileManager.getSkillLevel(skillType);
  }

  /**
   * 获取用户奖励
   */
  getUserRewards(): UserRewards | null {
    return userProfileManager.getUserRewards();
  }

  /**
   * 激活称号
   */
  async activateTitle(titleId: string): Promise<boolean> {
    return await userProfileManager.activateTitle(titleId);
  }

  /**
   * 激活徽章
   */
  async activateBadge(badgeId: string): Promise<boolean> {
    return await userProfileManager.activateBadge(badgeId);
  }

  /**
   * 获取等级进度
   */
  getLevelProgress(): number {
    const user = this.getCurrentUser();
    if (!user) return 0;

    return levelCalculator.getLevelProgress(user.experience);
  }

  /**
   * 获取到下一级所需经验
   */
  getExperienceToNextLevel(): number {
    const user = this.getCurrentUser();
    if (!user) return 0;

    return levelCalculator.getExperienceToNextLevel(user.experience);
  }

  /**
   * 获取用户统计
   */
  getUserStats(): Record<string, unknown> | null {
    const user = this.getCurrentUser();
    if (!user) return null;

    return {
      level: user.level,
      experience: user.experience,
      totalScore: user.totalScore,
      gamesPlayed: user.gamesPlayed,
      bestScores: user.bestScores,
      achievements: user.achievements.length,
      statistics: user.statistics,
      levelInfo: user.levelInfo,
      rewards: user.rewards,
    };
  }

  /**
   * 获取游戏统计
   */
  getGameStats(gameId: string): Record<string, unknown> | null {
    const user = this.getCurrentUser();
    if (!user) return null;

    return {
      gamesPlayed: user.gamesPlayed[gameId] || 0,
      bestScore: user.bestScores[gameId] || 0,
      // TODO: 添加更多游戏特定统计
    };
  }

  /**
   * 注册事件监听器
   */
  private registerEventListeners(): void {
    // 监听游戏开始事件
    eventBus.on('GAME_START', async (event) => {
      console.log('游戏开始:', event.data.gameId);
    });

    // 监听游戏结束事件
    eventBus.on('GAME_END', async (event) => {
      console.log('游戏结束:', event.data.gameId, '分数:', event.data.score);
    });

    // 监听等级提升事件
    eventBus.on('LEVEL_UP', async (event) => {
      console.log('等级提升:', event.data.oldLevel, '->', event.data.newLevel);
    });

    // 监听成就解锁事件
    eventBus.on('ACHIEVEMENT_UNLOCK', async (event) => {
      console.log('成就解锁:', event.data.achievementName);
    });

    // 监听排名更新事件
    eventBus.on('RANKING_UPDATE', async (event) => {
      console.log('排名更新:', event.data.oldRank, '->', event.data.newRank);
    });
  }

  /**
   * 辅助方法
   */
  private isFirstGameToday(user: UserProfile): boolean {
    const today = new Date().toDateString();
    const lastActive = new Date(user.lastActiveAt).toDateString();
    return today !== lastActive;
  }

  private getConsecutiveDays(user: UserProfile): number {
    return user.statistics.currentStreak || 1;
  }

  private isDailyGoalCompleted(user: UserProfile): boolean {
    return user.statistics.dailyGoalProgress >= 100;
  }
}

// 导出单例实例
export const gameService = GameService.getInstance();
