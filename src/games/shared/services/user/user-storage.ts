/**
 * 简化的用户数据存储系统
 * 适用于单用户本地游戏，移除了复杂的锁和事务机制
 */

import { userProfileManager } from './user-profile';
import type { UserProfile } from './user-profile';
import { scoreCalculator } from '../scoring/score-calculator';
import { rankingEngine } from '../ranking/ranking-engine';
import { experienceTracker } from '../level/experience-tracker';
import { levelCalculator } from '../level/level-calculator';
import { storage, storageManager, StorageErrorType, type StorageError } from '@/utils/storage';
import type { GameResult } from '@/types/game';

// 分数历史记录类型
interface ScoreHistoryRecord {
  score: number;
  breakdown: Record<string, unknown>;
  timestamp: number;
}

// 简化的游戏数据管理器
export class GameDataManager {
  private static instance: GameDataManager;

  static getInstance(): GameDataManager {
    if (!GameDataManager.instance) {
      GameDataManager.instance = new GameDataManager();
    }
    return GameDataManager.instance;
  }

  /**
   * 处理游戏结果 - 简化版本，无锁无事务
   */
  async processGameResult(gameResult: GameResult): Promise<void> {
    const userProfile = userProfileManager.getCurrentProfile();
    if (!userProfile) {
      throw new Error('用户档案未初始化');
    }

    try {
      console.log('开始处理游戏结果:', gameResult);

      // 1. 计算积分
      const userContext = {
        gameHistory: userProfile.bestScores,
        currentStreak: userProfile.statistics.currentStreak,
        isFirstGameToday: this.isFirstGameToday(userProfile),
        consecutiveDays: this.getConsecutiveDays(userProfile),
        dailyGoalCompleted: this.isDailyGoalCompleted(userProfile),
      };

      console.log('用户上下文:', userContext);

      const scoreBreakdown = scoreCalculator.calculateScore(gameResult, userContext);

      // 详细的积分计算日志
      console.log('🎯 积分计算详情:', {
        gameScore: gameResult.score,
        baseScore: scoreBreakdown.baseScore,
        timeBonus: scoreBreakdown.timeBonus,
        accuracyBonus: scoreBreakdown.accuracyBonus,
        comboBonus: scoreBreakdown.comboBonus,
        skillBonus: scoreBreakdown.skillBonus,
        activityBonus: scoreBreakdown.activityBonus,
        difficultyMultiplier: scoreBreakdown.difficultyMultiplier,
        finalScore: scoreBreakdown.finalScore,
        totalIncrease: scoreBreakdown.finalScore,
      });

      // 2. 计算经验值
      const experienceSources = experienceTracker.calculateGameExperience(gameResult, {
        isFirstGameToday: this.isFirstGameToday(userProfile),
        consecutiveDays: this.getConsecutiveDays(userProfile),
        dailyGoalCompleted: this.isDailyGoalCompleted(userProfile),
      });

      // 3. 更新最佳分数
      await userProfileManager.updateBestScore(gameResult.gameId, gameResult.score);

      // 4. 添加经验值并计算新等级
      const experienceResult = await experienceTracker.addExperience(
        userProfile.userId,
        experienceSources,
        userProfile.experience,
        userProfile.level,
        { calculateLevel: (exp: number) => levelCalculator.calculateLevel(exp).level }
      );

      // 5. 更新用户档案
      const newTotalScore = userProfile.totalScore + scoreBreakdown.finalScore;
      await userProfileManager.updateProfile({
        totalScore: newTotalScore,
        experience: experienceResult.newTotalExperience,
        level: experienceResult.newLevel,
        lastActiveAt: Date.now(),
      });

      // 6. 更新游戏统计
      await this.updateGameStats(
        gameResult.gameId,
        gameResult.score,
        scoreBreakdown as unknown as Record<string, unknown>
      );

      // 7. 更新排名
      await this.updateRankings(userProfile.userId, userProfile.username, newTotalScore, gameResult);

      // 8. 检查成就解锁
      const newAchievements = await userProfileManager.checkGameAchievements(gameResult);
      if (newAchievements.length > 0) {
        await this.addAchievements(newAchievements);
      }

      console.log('游戏结果处理完成');
    } catch (error) {
      console.error('处理游戏结果失败:', error);
      throw error;
    }
  }

  /**
   * 更新游戏统计
   */
  private async updateGameStats(gameId: string, score: number, breakdown: Record<string, unknown>): Promise<void> {
    // 更新游戏统计
    await userProfileManager.incrementGamePlayed(gameId);

    // 检查存储配额
    const hasQuota = await storageManager.checkQuota();
    if (!hasQuota) {
      console.warn('存储空间不足，跳过分数历史保存');
      return;
    }

    // 保存分数历史，使用改进的存储工具
    const scoreHistory = storage.get<ScoreHistoryRecord[]>(`score_history_${gameId}`, [], {
      onError: (error: StorageError) => {
        if (error.type === StorageErrorType.PARSE_ERROR) {
          console.warn(`分数历史数据损坏，重置游戏 ${gameId} 的历史记录`);
          storage.set(`score_history_${gameId}`, []);
        }
      },
    });

    scoreHistory.push({
      score,
      breakdown,
      timestamp: Date.now(),
    });

    // 只保留最近100条记录
    if (scoreHistory.length > 100) {
      scoreHistory.splice(0, scoreHistory.length - 100);
    }

    const success = storage.set(`score_history_${gameId}`, scoreHistory, {
      onError: (error: StorageError) => {
        if (error.type === StorageErrorType.QUOTA_EXCEEDED) {
          console.warn('存储空间不足，尝试清理后重试');
          // 自动清理并重试
          storageManager.cleanup({ clearExpiredCache: true, clearOldHistory: true }).then(() => {
            storage.set(`score_history_${gameId}`, scoreHistory.slice(-50)); // 只保留最近50条
          });
        }
      },
    });

    if (!success) {
      console.error(`保存游戏 ${gameId} 分数历史失败`);
    }
  }

  /**
   * 更新排名
   */
  private async updateRankings(
    userId: string,
    username: string,
    totalScore: number,
    gameResult: GameResult
  ): Promise<void> {
    try {
      // 更新全局排名
      await rankingEngine.updateUserScore(userId, username, totalScore, 'global');

      // 更新游戏专项排名
      await rankingEngine.updateUserScore(userId, username, gameResult.score, 'game', gameResult.gameId);

      // 更新每日排名
      await rankingEngine.updateUserScore(userId, username, totalScore, 'daily');
    } catch (error) {
      console.error('更新排名失败:', error);
    }
  }

  /**
   * 添加成就
   */
  private async addAchievements(achievements: string[]): Promise<void> {
    for (const achievementId of achievements) {
      await userProfileManager.addAchievement(achievementId);
    }
  }

  /**
   * 辅助方法
   */
  private isFirstGameToday(userProfile: UserProfile): boolean {
    const today = new Date().toDateString();
    const lastActive = new Date(userProfile.lastActiveAt).toDateString();
    return today !== lastActive;
  }

  private getConsecutiveDays(userProfile: UserProfile): number {
    return userProfile.statistics.currentStreak || 1;
  }

  private isDailyGoalCompleted(userProfile: UserProfile): boolean {
    return userProfile.statistics.dailyGoalProgress >= 100;
  }
}

// 导出单例实例 - 保持向后兼容
export const dataConsistencyManager = GameDataManager.getInstance();

/**
 * 游戏数据访问器 - 提供便捷的数据访问API
 */
export const gameDataAccessor = {
  /**
   * 获取游戏分数历史
   */
  getScoreHistory(gameId: string, options?: { maxRecords?: number }): ScoreHistoryRecord[] {
    const { maxRecords = 100 } = options || {};

    const history = storage.get<ScoreHistoryRecord[]>(`score_history_${gameId}`, [], {
      onError: (error: StorageError) => {
        if (error.type === StorageErrorType.PARSE_ERROR) {
          console.warn(`游戏 ${gameId} 分数历史数据损坏，返回空数组`);
          return [];
        }
      },
    });

    return history.slice(-maxRecords).reverse(); // 最新的在前
  },

  /**
   * 获取最近N次游戏记录
   */
  getRecentGames(gameId: string, count: number = 10): ScoreHistoryRecord[] {
    return this.getScoreHistory(gameId, { maxRecords: count });
  },

  /**
   * 获取最高分记录
   */
  getBestScore(gameId: string): number {
    const history = this.getScoreHistory(gameId);
    return history.reduce((max, record) => Math.max(max, record.score), 0);
  },

  /**
   * 获取游戏统计信息
   */
  getGameStats(gameId: string): {
    totalGames: number;
    bestScore: number;
    averageScore: number;
    recentAverage: number; // 最近10次平均分
    lastPlayed: number | null;
  } {
    const history = this.getScoreHistory(gameId);

    if (history.length === 0) {
      return {
        totalGames: 0,
        bestScore: 0,
        averageScore: 0,
        recentAverage: 0,
        lastPlayed: null,
      };
    }

    const totalGames = history.length;
    const bestScore = Math.max(...history.map((h) => h.score));
    const averageScore = Math.round(history.reduce((sum, h) => sum + h.score, 0) / totalGames);

    const recent10 = history.slice(0, 10); // 最新的10次
    const recentAverage =
      recent10.length > 0 ? Math.round(recent10.reduce((sum, h) => sum + h.score, 0) / recent10.length) : 0;

    const lastPlayed = history[0]?.timestamp || null;

    return {
      totalGames,
      bestScore,
      averageScore,
      recentAverage,
      lastPlayed,
    };
  },

  /**
   * 清理游戏数据
   */
  async cleanupGameData(
    gameId: string,
    options?: {
      keepRecentDays?: number;
      maxRecords?: number;
    }
  ): Promise<void> {
    const { keepRecentDays = 30, maxRecords = 100 } = options || {};

    const history = this.getScoreHistory(gameId);
    const cutoffTime = Date.now() - keepRecentDays * 24 * 60 * 60 * 1000;

    // 按时间和数量双重过滤
    const filteredHistory = history.filter((record) => record.timestamp > cutoffTime).slice(-maxRecords);

    if (filteredHistory.length !== history.length) {
      storage.set(`score_history_${gameId}`, filteredHistory.reverse()); // 恢复时间顺序
      console.log(`清理游戏 ${gameId} 数据：${history.length} → ${filteredHistory.length} 条记录`);
    }
  },

  /**
   * 导出游戏数据（用于备份）
   */
  exportGameData(gameId: string) {
    const stats = this.getGameStats(gameId);
    return {
      gameId,
      exportTime: Date.now(),
      history: this.getScoreHistory(gameId),
      stats,
    };
  },
};
