/**
 * 统一用户事件系统
 * 实现事件驱动架构，确保各系统间的松耦合
 */

// 事件类型定义
export interface GameEvent {
  type: string;
  timestamp: number;
  userId: string;
  data: Record<string, unknown>;
}

export interface GameStartEvent extends GameEvent {
  type: 'GAME_START';
  data: {
    gameId: string;
    difficulty: string;
    timestamp: number;
  };
}

export interface GameEndEvent extends GameEvent {
  type: 'GAME_END';
  data: {
    gameId: string;
    score: number;
    duration: number;
    accuracy: number;
    difficulty: string;
    result: Record<string, unknown>;
  };
}

export interface ScoreUpdateEvent extends GameEvent {
  type: 'SCORE_UPDATE';
  data: {
    gameId: string;
    oldScore: number;
    newScore: number;
    scoreChange: number;
  };
}

export interface LevelUpEvent extends GameEvent {
  type: 'LEVEL_UP';
  data: {
    oldLevel: number;
    newLevel: number;
    experience: number;
    rewards: Record<string, unknown>[];
  };
}

export interface AchievementUnlockEvent extends GameEvent {
  type: 'ACHIEVEMENT_UNLOCK';
  data: {
    achievementId: string;
    achievementName: string;
    rewards: Record<string, unknown>[];
    points?: number;
  };
}

export interface RankingUpdateEvent extends GameEvent {
  type: 'RANKING_UPDATE';
  data: {
    gameId?: string;
    oldRank: number;
    newRank: number;
    rankingType: string;
  };
}

// 事件监听器类型
export type EventListener<T extends GameEvent = GameEvent> = (event: T) => void | Promise<void>;

// 事件总线类
export class EventBus {
  private listeners: Map<string, EventListener[]> = new Map();
  private static instance: EventBus;

  static getInstance(): EventBus {
    if (!EventBus.instance) {
      EventBus.instance = new EventBus();
    }
    return EventBus.instance;
  }

  /**
   * 注册事件监听器
   */
  on<T extends GameEvent>(eventType: string, listener: EventListener<T>): void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, []);
    }
    this.listeners.get(eventType)!.push(listener as EventListener);
  }

  /**
   * 移除事件监听器
   */
  off<T extends GameEvent>(eventType: string, listener: EventListener<T>): void {
    const listeners = this.listeners.get(eventType);
    if (listeners) {
      const index = listeners.indexOf(listener as EventListener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 发布事件
   */
  async emit<T extends GameEvent>(event: T): Promise<void> {
    const listeners = this.listeners.get(event.type);
    if (listeners) {
      // 并行执行所有监听器
      const promises = listeners.map((listener) => {
        try {
          return Promise.resolve(listener(event));
        } catch (error) {
          console.error(`事件监听器执行失败 [${event.type}]:`, error);
          return Promise.resolve();
        }
      });

      await Promise.allSettled(promises);
    }
  }

  /**
   * 创建游戏开始事件
   */
  createGameStartEvent(userId: string, gameId: string, difficulty: string): GameStartEvent {
    return {
      type: 'GAME_START',
      timestamp: Date.now(),
      userId,
      data: {
        gameId,
        difficulty,
        timestamp: Date.now(),
      },
    };
  }

  /**
   * 创建游戏结束事件
   */
  createGameEndEvent(
    userId: string,
    gameId: string,
    score: number,
    duration: number,
    accuracy: number,
    difficulty: string,
    result: Record<string, unknown>
  ): GameEndEvent {
    return {
      type: 'GAME_END',
      timestamp: Date.now(),
      userId,
      data: {
        gameId,
        score,
        duration,
        accuracy,
        difficulty,
        result,
      },
    };
  }

  /**
   * 创建分数更新事件
   */
  createScoreUpdateEvent(userId: string, gameId: string, oldScore: number, newScore: number): ScoreUpdateEvent {
    return {
      type: 'SCORE_UPDATE',
      timestamp: Date.now(),
      userId,
      data: {
        gameId,
        oldScore,
        newScore,
        scoreChange: newScore - oldScore,
      },
    };
  }

  /**
   * 创建等级提升事件
   */
  createLevelUpEvent(
    userId: string,
    oldLevel: number,
    newLevel: number,
    experience: number,
    rewards: Record<string, unknown>[]
  ): LevelUpEvent {
    return {
      type: 'LEVEL_UP',
      timestamp: Date.now(),
      userId,
      data: {
        oldLevel,
        newLevel,
        experience,
        rewards,
      },
    };
  }

  /**
   * 创建成就解锁事件
   */
  createAchievementUnlockEvent(
    userId: string,
    achievementId: string,
    achievementName: string,
    rewards: Record<string, unknown>[]
  ): AchievementUnlockEvent {
    return {
      type: 'ACHIEVEMENT_UNLOCK',
      timestamp: Date.now(),
      userId,
      data: {
        achievementId,
        achievementName,
        rewards,
        points: (rewards.find((r) => r.type === 'experience')?.value as number) || 0,
      },
    };
  }

  /**
   * 创建排名更新事件
   */
  createRankingUpdateEvent(
    userId: string,
    oldRank: number,
    newRank: number,
    rankingType: string,
    gameId?: string
  ): RankingUpdateEvent {
    return {
      type: 'RANKING_UPDATE',
      timestamp: Date.now(),
      userId,
      data: {
        gameId,
        oldRank,
        newRank,
        rankingType,
      },
    };
  }

  /**
   * 清除所有监听器（主要用于测试）
   */
  clear(): void {
    this.listeners.clear();
  }
}

// 导出单例实例
export const eventBus = EventBus.getInstance();
