/**
 * 用户档案管理系统
 * 统一管理用户的基本信息和游戏数据
 */

import { eventBus } from './user-events';
import { levelCalculator } from '../level/level-calculator';
import { levelRewardManager } from '../level/level-rewards';
import { experienceTracker } from '../level/experience-tracker';
import { achievementEngine } from '../achievement/achievement-engine';
import type { UserLevel, GameSpecificLevel, SkillLevel } from '../level/level-calculator';
import type { UserRewards } from '../level/level-rewards';
import type { UserAchievement, Achievement } from '../achievement/achievement-engine';
import type { GameResult } from '@/types/game';

// 用户档案接口
export interface UserProfile {
  userId: string;
  username: string;
  avatar?: string;
  level: number;
  experience: number;
  totalScore: number;
  gamesPlayed: Record<string, number>;
  bestScores: Record<string, number>;
  achievements: string[];
  createdAt: number;
  lastActiveAt: number;
  preferences: UserPreferences;
  statistics: UserStatistics;
  // 等级系统相关
  levelInfo: UserLevel;
  gameSpecificLevels: Record<string, GameSpecificLevel>;
  skillLevels: Record<string, SkillLevel>;
  rewards: UserRewards;
  experienceHistory: Record<string, unknown>[]; // 经验值历史记录
  // 成就系统相关
  userAchievements: UserAchievement[]; // 用户成就列表
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  soundEnabled: boolean;
  vibrationEnabled: boolean;
  language: string;
  notifications: {
    achievements: boolean;
    levelUp: boolean;
    rankings: boolean;
  };
}

export interface UserStatistics {
  totalPlayTime: number;
  averageAccuracy: number;
  longestStreak: number;
  currentStreak: number;
  favoriteGame?: string;
  lastGamePlayed?: string;
  dailyGoalProgress: number;
  weeklyGoalProgress: number;
}

// 用户档案管理器
export class UserProfileManager {
  private static instance: UserProfileManager;
  private currentProfile: UserProfile | null = null;

  static getInstance(): UserProfileManager {
    if (!UserProfileManager.instance) {
      UserProfileManager.instance = new UserProfileManager();
    }
    return UserProfileManager.instance;
  }

  /**
   * 初始化用户档案
   */
  async initializeProfile(userId?: string): Promise<UserProfile> {
    // 获取或生成持久化的用户ID
    const actualUserId = userId || (await this.getOrCreatePersistentUserId());
    console.log('初始化用户档案，用户ID:', actualUserId);

    // 尝试从存储中加载现有档案
    let profile = await this.loadProfile(actualUserId);

    if (!profile) {
      console.log('未找到现有档案，创建新的用户档案');
      // 创建新的用户档案
      profile = this.createDefaultProfile(actualUserId);
      await this.saveProfile(profile);
    } else {
      console.log('成功加载现有用户档案:', {
        userId: profile.userId,
        totalScore: profile.totalScore,
        experience: profile.experience,
        level: profile.level,
      });
    }

    this.currentProfile = profile;

    // 加载成就数据
    if (profile.userAchievements) {
      achievementEngine.loadUserAchievements(profile.userAchievements);
    }

    return profile;
  }

  /**
   * 获取当前用户档案
   */
  getCurrentProfile(): UserProfile | null {
    return this.currentProfile;
  }

  /**
   * 刷新当前用户档案（从存储重新加载）
   */
  async refreshCurrentProfile(): Promise<UserProfile | null> {
    if (!this.currentProfile) {
      return null;
    }

    console.log('刷新用户档案，当前用户ID:', this.currentProfile.userId);

    const refreshedProfile = await this.loadProfile(this.currentProfile.userId);
    if (refreshedProfile) {
      console.log('成功从存储加载用户档案:', refreshedProfile);
      this.currentProfile = refreshedProfile;

      // 重新加载成就数据
      if (refreshedProfile.userAchievements) {
        achievementEngine.loadUserAchievements(refreshedProfile.userAchievements);
      }
    } else {
      console.warn('从存储加载用户档案失败，保持当前档案不变');
      // 如果从存储加载失败，保持当前档案不变，不要重置
      // 这可能是因为存储读取错误或数据损坏
    }

    return this.currentProfile;
  }

  /**
   * 更新用户档案
   */
  async updateProfile(updates: Partial<UserProfile>): Promise<UserProfile> {
    if (!this.currentProfile) {
      throw new Error('用户档案未初始化');
    }

    const oldProfile = { ...this.currentProfile };
    this.currentProfile = { ...this.currentProfile, ...updates };
    this.currentProfile.lastActiveAt = Date.now();

    await this.saveProfile(this.currentProfile);

    // 发布相关事件
    await this.emitProfileUpdateEvents(oldProfile, this.currentProfile);

    return this.currentProfile;
  }

  /**
   * 更新用户统计数据
   */
  async updateStatistics(updates: Partial<UserStatistics>): Promise<void> {
    if (!this.currentProfile) return;

    this.currentProfile.statistics = {
      ...this.currentProfile.statistics,
      ...updates,
    };

    await this.saveProfile(this.currentProfile);
  }

  /**
   * 更新用户偏好设置
   */
  async updatePreferences(updates: Partial<UserPreferences>): Promise<void> {
    if (!this.currentProfile) return;

    this.currentProfile.preferences = {
      ...this.currentProfile.preferences,
      ...updates,
    };

    await this.saveProfile(this.currentProfile);
  }

  /**
   * 增加游戏次数
   */
  async incrementGamePlayed(gameId: string): Promise<void> {
    if (!this.currentProfile) return;

    this.currentProfile.gamesPlayed[gameId] = (this.currentProfile.gamesPlayed[gameId] || 0) + 1;
    this.currentProfile.statistics.lastGamePlayed = gameId;

    await this.saveProfile(this.currentProfile);
  }

  /**
   * 更新最佳分数
   */
  async updateBestScore(gameId: string, score: number): Promise<boolean> {
    if (!this.currentProfile) return false;

    const currentBest = this.currentProfile.bestScores[gameId] || 0;
    if (score > currentBest) {
      this.currentProfile.bestScores[gameId] = score;
      await this.saveProfile(this.currentProfile);
      return true; // 新纪录
    }
    return false;
  }

  /**
   * 添加成就
   */
  async addAchievement(achievementId: string): Promise<boolean> {
    if (!this.currentProfile) return false;

    if (!this.currentProfile.achievements.includes(achievementId)) {
      this.currentProfile.achievements.push(achievementId);

      // 添加成就经验值
      const expGained = await experienceTracker.addAchievementExperience(
        this.currentProfile.userId,
        achievementId,
        this.currentProfile.experience
      );

      await this.addExperience(expGained);
      await this.saveProfile(this.currentProfile);
      return true; // 新成就
    }
    return false;
  }

  /**
   * 添加经验值
   */
  async addExperience(amount: number): Promise<boolean> {
    if (!this.currentProfile) return false;

    const oldLevel = this.currentProfile.level;
    const oldExperience = this.currentProfile.experience;
    const newExperience = oldExperience + amount;

    // 更新经验值
    this.currentProfile.experience = newExperience;

    // 重新计算等级
    const newLevelInfo = levelCalculator.calculateLevel(newExperience);
    this.currentProfile.level = newLevelInfo.level;
    this.currentProfile.levelInfo = newLevelInfo;

    // 检查是否升级
    if (newLevelInfo.level > oldLevel) {
      // 处理等级提升奖励
      const newRewards = await levelRewardManager.processLevelUpRewards(
        oldLevel,
        newLevelInfo.level,
        this.currentProfile.userId
      );

      // 更新用户奖励
      this.currentProfile.rewards = levelRewardManager.getUserRewards();

      // 发布等级提升事件
      const levelUpEvent = eventBus.createLevelUpEvent(
        this.currentProfile.userId,
        oldLevel,
        newLevelInfo.level,
        newExperience,
        newRewards as unknown as Record<string, unknown>[]
      );
      await eventBus.emit(levelUpEvent);

      return true; // 升级了
    }

    return false; // 没有升级
  }

  /**
   * 更新游戏专项等级
   */
  async updateGameLevel(
    gameId: string,
    gameStats: {
      gamesPlayed: number;
      totalScore: number;
      bestScore: number;
      averageAccuracy: number;
    }
  ): Promise<void> {
    if (!this.currentProfile) return;

    const gameLevel = levelCalculator.calculateGameLevel(gameId, gameStats);
    this.currentProfile.gameSpecificLevels[gameId] = gameLevel;

    await this.saveProfile(this.currentProfile);
  }

  /**
   * 更新技能等级
   */
  async updateSkillLevel(
    skillType: 'speed' | 'accuracy' | 'consistency' | 'endurance',
    skillData: {
      totalGames: number;
      skillValue: number;
    }
  ): Promise<void> {
    if (!this.currentProfile) return;

    const skillLevel = levelCalculator.calculateSkillLevel(skillType, skillData);
    this.currentProfile.skillLevels[skillType] = skillLevel;

    await this.saveProfile(this.currentProfile);
  }

  /**
   * 获取等级信息
   */
  getLevelInfo(): UserLevel | null {
    return this.currentProfile?.levelInfo || null;
  }

  /**
   * 获取游戏专项等级
   */
  getGameLevel(gameId: string): GameSpecificLevel | null {
    return this.currentProfile?.gameSpecificLevels[gameId] || null;
  }

  /**
   * 获取技能等级
   */
  getSkillLevel(skillType: string): SkillLevel | null {
    return this.currentProfile?.skillLevels[skillType] || null;
  }

  /**
   * 获取用户奖励
   */
  getUserRewards(): UserRewards | null {
    return this.currentProfile?.rewards || null;
  }

  /**
   * 激活称号
   */
  async activateTitle(titleId: string): Promise<boolean> {
    if (!this.currentProfile) return false;

    const success = levelRewardManager.activateTitle(titleId);
    if (success) {
      this.currentProfile.rewards = levelRewardManager.getUserRewards();
      await this.saveProfile(this.currentProfile);
    }
    return success;
  }

  /**
   * 激活徽章
   */
  async activateBadge(badgeId: string): Promise<boolean> {
    if (!this.currentProfile) return false;

    const success = levelRewardManager.activateBadge(badgeId);
    if (success) {
      this.currentProfile.rewards = levelRewardManager.getUserRewards();
      await this.saveProfile(this.currentProfile);
    }
    return success;
  }

  /**
   * 检查游戏成就
   */
  async checkGameAchievements(gameResult: GameResult): Promise<string[]> {
    if (!this.currentProfile) return [];

    const userStats = {
      userId: this.currentProfile.userId,
      totalScore: this.currentProfile.totalScore,
      gamesPlayed: this.currentProfile.gamesPlayed,
      statistics: this.currentProfile.statistics,
    };

    const newAchievements = await achievementEngine.checkGameAchievements(gameResult, userStats);

    if (newAchievements.length > 0) {
      // 更新用户成就列表
      this.currentProfile.userAchievements = achievementEngine.getUserAchievements();

      // 为每个新成就添加到achievements数组（保持向后兼容）
      for (const achievementId of newAchievements) {
        if (!this.currentProfile.achievements.includes(achievementId)) {
          this.currentProfile.achievements.push(achievementId);
        }
      }

      await this.saveProfile(this.currentProfile);
    }

    return newAchievements;
  }

  /**
   * 检查用户统计成就
   */
  async checkUserStatsAchievements(): Promise<string[]> {
    if (!this.currentProfile) return [];

    const userStats = {
      userId: this.currentProfile.userId,
      totalScore: this.currentProfile.totalScore,
      gamesPlayed: this.currentProfile.gamesPlayed,
      statistics: this.currentProfile.statistics,
    };

    const newAchievements = await achievementEngine.checkUserStatsAchievements(userStats);

    if (newAchievements.length > 0) {
      // 更新用户成就列表
      this.currentProfile.userAchievements = achievementEngine.getUserAchievements();

      // 为每个新成就添加到achievements数组（保持向后兼容）
      for (const achievementId of newAchievements) {
        if (!this.currentProfile.achievements.includes(achievementId)) {
          this.currentProfile.achievements.push(achievementId);
        }
      }

      await this.saveProfile(this.currentProfile);
    }

    return newAchievements;
  }

  /**
   * 获取用户成就列表
   */
  getUserAchievements(): UserAchievement[] {
    return this.currentProfile?.userAchievements || [];
  }

  /**
   * 获取成就统计
   */
  getAchievementStats(): Record<string, unknown> {
    return achievementEngine.getAchievementStats();
  }

  /**
   * 获取所有可用成就
   */
  getAllAchievements(): Achievement[] {
    return achievementEngine.getAllAchievements();
  }

  /**
   * 创建默认用户档案
   */
  private createDefaultProfile(userId: string): UserProfile {
    const defaultLevelInfo = levelCalculator.calculateLevel(0);

    return {
      userId,
      username: '易学者',
      level: 1,
      experience: 0,
      totalScore: 0,
      gamesPlayed: {},
      bestScores: {},
      achievements: [],
      createdAt: Date.now(),
      lastActiveAt: Date.now(),
      preferences: {
        theme: 'auto',
        soundEnabled: true,
        vibrationEnabled: true,
        language: 'zh-CN',
        notifications: {
          achievements: true,
          levelUp: true,
          rankings: true,
        },
      },
      statistics: {
        totalPlayTime: 0,
        averageAccuracy: 0,
        longestStreak: 0,
        currentStreak: 0,
        dailyGoalProgress: 0,
        weeklyGoalProgress: 0,
      },
      // 等级系统相关
      levelInfo: defaultLevelInfo,
      gameSpecificLevels: {},
      skillLevels: {},
      rewards: levelRewardManager.getUserRewards(),
      experienceHistory: [],
      // 成就系统相关
      userAchievements: [],
    };
  }

  /**
   * 从存储中加载用户档案
   */
  private async loadProfile(userId: string): Promise<UserProfile | null> {
    try {
      const storageKey = `user_profile_${userId}`;
      console.log('尝试从存储加载用户档案，key:', storageKey);

      const profileData = uni.getStorageSync(storageKey);

      if (profileData) {
        console.log('成功从存储读取用户档案数据');
        return profileData;
      } else {
        console.warn('存储中没有找到用户档案数据，key:', storageKey);
        return null;
      }
    } catch (error) {
      console.error('加载用户档案失败:', error);
      return null;
    }
  }

  /**
   * 保存用户档案到存储
   */
  private async saveProfile(profile: UserProfile): Promise<void> {
    try {
      const storageKey = `user_profile_${profile.userId}`;
      console.log('保存用户档案到存储，key:', storageKey, '数据:', {
        userId: profile.userId,
        totalScore: profile.totalScore,
        experience: profile.experience,
        level: profile.level,
      });

      uni.setStorageSync(storageKey, profile);
      console.log('用户档案保存成功');
    } catch (error) {
      console.error('保存用户档案失败:', error);
      throw error;
    }
  }

  /**
   * 获取或创建持久化的用户ID
   */
  private async getOrCreatePersistentUserId(): Promise<string> {
    try {
      // 尝试从存储中获取已有的用户ID
      let userId = uni.getStorageSync('app_user_id');

      if (!userId) {
        // 检查是否有旧的用户档案（向后兼容）
        const existingProfile = await this.findExistingUserProfile();
        if (existingProfile) {
          userId = existingProfile.userId;
          uni.setStorageSync('app_user_id', userId);
          console.log('找到现有用户档案，使用其ID:', userId);
        } else {
          // 如果没有找到，生成新的用户ID并保存
          userId = this.generateUserId();
          uni.setStorageSync('app_user_id', userId);
          console.log('生成新的用户ID并保存:', userId);
        }
      } else {
        console.log('使用已有的用户ID:', userId);
      }

      return userId;
    } catch (error) {
      console.error('获取用户ID失败，使用临时ID:', error);
      // 如果存储操作失败，返回一个临时ID
      return this.generateUserId();
    }
  }

  /**
   * 查找现有的用户档案（向后兼容）
   */
  private async findExistingUserProfile(): Promise<UserProfile | null> {
    try {
      // 获取所有存储的键
      const storageInfo = await new Promise<{ keys: string[] }>((resolve, reject) => {
        uni.getStorageInfo({
          success: resolve,
          fail: reject,
        });
      });

      // 查找用户档案键
      const userProfileKeys = storageInfo.keys.filter((key: string) => key.startsWith('user_profile_'));

      if (userProfileKeys.length > 0) {
        // 返回第一个找到的用户档案
        const profileData = uni.getStorageSync(userProfileKeys[0]);
        return profileData || null;
      }

      return null;
    } catch (error) {
      console.error('查找现有用户档案失败:', error);
      return null;
    }
  }

  /**
   * 生成用户ID
   */
  private generateUserId(): string {
    return 'user_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
  }

  /**
   * 发布档案更新相关事件
   */
  private async emitProfileUpdateEvents(oldProfile: UserProfile, newProfile: UserProfile): Promise<void> {
    // 检查等级是否提升
    if (newProfile.level > oldProfile.level) {
      const levelUpEvent = eventBus.createLevelUpEvent(
        newProfile.userId,
        oldProfile.level,
        newProfile.level,
        newProfile.experience,
        [] // TODO: 添加等级奖励
      );
      await eventBus.emit(levelUpEvent);
    }

    // 检查分数是否更新
    if (newProfile.totalScore !== oldProfile.totalScore) {
      const scoreUpdateEvent = eventBus.createScoreUpdateEvent(
        newProfile.userId,
        'total', // 总分更新
        oldProfile.totalScore,
        newProfile.totalScore
      );
      await eventBus.emit(scoreUpdateEvent);
    }
  }
}

// 导出单例实例
export const userProfileManager = UserProfileManager.getInstance();
