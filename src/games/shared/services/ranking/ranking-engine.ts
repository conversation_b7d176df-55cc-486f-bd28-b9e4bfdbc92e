/**
 * 排名计算引擎
 * 提供多维度排名计算和实时更新机制
 */

import { eventBus } from '../user/user-events';

// 排名条目接口
export interface RankingEntry {
  userId: string;
  username: string;
  score: number;
  rank: number;
  change: number; // 排名变化
  lastUpdated: number;
  gameSpecificData?: Record<string, unknown>;
}

// 排名类型
export type RankingType = 'global' | 'game' | 'daily' | 'weekly' | 'monthly';

// 排名配置
export interface RankingConfig {
  maxEntries: number;
  updateInterval: number; // 更新间隔（毫秒）
  cacheTimeout: number; // 缓存超时（毫秒）
  batchSize: number; // 批量更新大小
}

// 默认排名配置
const DEFAULT_RANKING_CONFIG: RankingConfig = {
  maxEntries: 1000,
  updateInterval: 5000, // 5秒
  cacheTimeout: 60000, // 1分钟
  batchSize: 50,
};

// 排名计算引擎
export class RankingEngine {
  private config: RankingConfig;
  private rankings: Map<string, RankingEntry[]> = new Map();
  private updateQueue: Set<string> = new Set();
  private lastUpdate: Map<string, number> = new Map();
  private updateTimer: number | null = null;

  constructor(config: RankingConfig = DEFAULT_RANKING_CONFIG) {
    this.config = config;
    this.startUpdateTimer();
  }

  /**
   * 获取排行榜
   */
  async getRanking(
    type: RankingType,
    gameId?: string,
    limit: number = 100,
    offset: number = 0
  ): Promise<RankingEntry[]> {
    const key = this.getRankingKey(type, gameId);

    // 检查缓存是否有效
    if (this.isCacheValid(key)) {
      const ranking = this.rankings.get(key) || [];
      return ranking.slice(offset, offset + limit);
    }

    // 重新计算排名
    await this.calculateRanking(type, gameId);
    const ranking = this.rankings.get(key) || [];
    return ranking.slice(offset, offset + limit);
  }

  /**
   * 获取用户排名
   */
  async getUserRank(userId: string, type: RankingType, gameId?: string): Promise<number> {
    const ranking = await this.getRanking(type, gameId, this.config.maxEntries);
    const userEntry = ranking.find((entry) => entry.userId === userId);
    return userEntry?.rank || -1;
  }

  /**
   * 更新用户分数
   */
  async updateUserScore(
    userId: string,
    username: string,
    score: number,
    type: RankingType,
    gameId?: string,
    gameSpecificData?: Record<string, unknown>
  ): Promise<void> {
    const key = this.getRankingKey(type, gameId);

    // 获取当前排名
    let ranking = this.rankings.get(key) || [];

    // 查找用户现有条目
    const existingIndex = ranking.findIndex((entry) => entry.userId === userId);
    const oldRank = existingIndex >= 0 ? ranking[existingIndex].rank : -1;

    if (existingIndex >= 0) {
      // 更新现有条目
      ranking[existingIndex] = {
        ...ranking[existingIndex],
        score,
        username,
        lastUpdated: Date.now(),
        gameSpecificData,
      };
    } else {
      // 添加新条目
      ranking.push({
        userId,
        username,
        score,
        rank: 0, // 临时排名，稍后重新计算
        change: 0,
        lastUpdated: Date.now(),
        gameSpecificData,
      });
    }

    // 重新排序和计算排名
    ranking = this.sortAndRankEntries(ranking);

    // 限制条目数量
    if (ranking.length > this.config.maxEntries) {
      ranking = ranking.slice(0, this.config.maxEntries);
    }

    // 更新缓存
    this.rankings.set(key, ranking);
    this.lastUpdate.set(key, Date.now());

    // 计算排名变化并发布事件
    const newRank = ranking.findIndex((entry) => entry.userId === userId) + 1;
    if (oldRank > 0 && newRank !== oldRank) {
      const rankingUpdateEvent = eventBus.createRankingUpdateEvent(userId, oldRank, newRank, type, gameId);
      await eventBus.emit(rankingUpdateEvent);
    }
  }

  /**
   * 批量更新排名
   */
  async batchUpdateRanking(
    updates: Array<{
      userId: string;
      username: string;
      score: number;
      type: RankingType;
      gameId?: string;
      gameSpecificData?: Record<string, unknown>;
    }>
  ): Promise<void> {
    // 按排名类型分组
    const groupedUpdates = new Map<string, typeof updates>();

    updates.forEach((update) => {
      const key = this.getRankingKey(update.type, update.gameId);
      if (!groupedUpdates.has(key)) {
        groupedUpdates.set(key, []);
      }
      groupedUpdates.get(key)!.push(update);
    });

    // 批量处理每个排名类型
    for (const [key, keyUpdates] of groupedUpdates) {
      await this.processBatchUpdates(key, keyUpdates);
    }
  }

  /**
   * 清理过期数据
   */
  cleanupExpiredData(maxAge: number = 30 * 24 * 60 * 60 * 1000): void {
    const cutoffTime = Date.now() - maxAge;

    for (const [key, ranking] of this.rankings) {
      const filteredRanking = ranking.filter((entry) => entry.lastUpdated > cutoffTime);
      if (filteredRanking.length !== ranking.length) {
        this.rankings.set(key, this.sortAndRankEntries(filteredRanking));
      }
    }
  }

  /**
   * 获取排名统计信息
   */
  getRankingStats(
    type: RankingType,
    gameId?: string
  ): {
    totalEntries: number;
    topScore: number;
    averageScore: number;
    lastUpdated: number;
  } {
    const key = this.getRankingKey(type, gameId);
    const ranking = this.rankings.get(key) || [];

    if (ranking.length === 0) {
      return {
        totalEntries: 0,
        topScore: 0,
        averageScore: 0,
        lastUpdated: 0,
      };
    }

    const totalScore = ranking.reduce((sum, entry) => sum + entry.score, 0);

    return {
      totalEntries: ranking.length,
      topScore: ranking[0]?.score || 0,
      averageScore: Math.round(totalScore / ranking.length),
      lastUpdated: this.lastUpdate.get(key) || 0,
    };
  }

  /**
   * 私有方法：获取排名键
   */
  private getRankingKey(type: RankingType, gameId?: string): string {
    return gameId ? `${type}_${gameId}` : type;
  }

  /**
   * 私有方法：检查缓存是否有效
   */
  private isCacheValid(key: string): boolean {
    const lastUpdateTime = this.lastUpdate.get(key) || 0;
    return Date.now() - lastUpdateTime < this.config.cacheTimeout;
  }

  /**
   * 私有方法：计算排名
   */
  private async calculateRanking(type: RankingType, gameId?: string): Promise<void> {
    // 这里应该从数据源重新加载数据
    // 目前使用现有缓存数据重新排序
    const key = this.getRankingKey(type, gameId);
    const ranking = this.rankings.get(key) || [];

    const sortedRanking = this.sortAndRankEntries(ranking);
    this.rankings.set(key, sortedRanking);
    this.lastUpdate.set(key, Date.now());
  }

  /**
   * 私有方法：排序和计算排名
   */
  private sortAndRankEntries(entries: RankingEntry[]): RankingEntry[] {
    // 按分数降序排序
    const sorted = [...entries].sort((a, b) => b.score - a.score);

    // 计算排名（处理并列情况）
    let currentRank = 1;
    for (let i = 0; i < sorted.length; i++) {
      if (i > 0 && sorted[i].score < sorted[i - 1].score) {
        currentRank = i + 1;
      }

      const oldRank = sorted[i].rank;
      sorted[i].rank = currentRank;
      sorted[i].change = oldRank > 0 ? oldRank - currentRank : 0;
    }

    return sorted;
  }

  /**
   * 私有方法：处理批量更新
   */
  private async processBatchUpdates(
    key: string,
    updates: Array<{
      userId: string;
      username: string;
      score: number;
      gameSpecificData?: Record<string, unknown>;
    }>
  ): Promise<void> {
    let ranking = this.rankings.get(key) || [];

    // 应用所有更新
    updates.forEach((update) => {
      const existingIndex = ranking.findIndex((entry) => entry.userId === update.userId);

      if (existingIndex >= 0) {
        ranking[existingIndex] = {
          ...ranking[existingIndex],
          score: update.score,
          username: update.username,
          lastUpdated: Date.now(),
          gameSpecificData: update.gameSpecificData,
        };
      } else {
        ranking.push({
          userId: update.userId,
          username: update.username,
          score: update.score,
          rank: 0,
          change: 0,
          lastUpdated: Date.now(),
          gameSpecificData: update.gameSpecificData,
        });
      }
    });

    // 重新排序和更新缓存
    ranking = this.sortAndRankEntries(ranking);
    if (ranking.length > this.config.maxEntries) {
      ranking = ranking.slice(0, this.config.maxEntries);
    }

    this.rankings.set(key, ranking);
    this.lastUpdate.set(key, Date.now());
  }

  /**
   * 私有方法：启动更新定时器
   */
  private startUpdateTimer(): void {
    this.updateTimer = setInterval(() => {
      this.processUpdateQueue();
    }, this.config.updateInterval) as unknown as number;
  }

  /**
   * 私有方法：处理更新队列
   */
  private async processUpdateQueue(): Promise<void> {
    if (this.updateQueue.size === 0) return;

    const keysToUpdate = Array.from(this.updateQueue).slice(0, this.config.batchSize);
    this.updateQueue.clear();

    for (const key of keysToUpdate) {
      await this.calculateRanking(
        key.includes('_') ? (key.split('_')[0] as RankingType) : (key as RankingType),
        key.includes('_') ? key.split('_')[1] : undefined
      );
    }
  }

  /**
   * 销毁引擎
   */
  destroy(): void {
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
      this.updateTimer = null;
    }
    this.rankings.clear();
    this.updateQueue.clear();
    this.lastUpdate.clear();
  }
}

// 导出默认实例
export const rankingEngine = new RankingEngine();
