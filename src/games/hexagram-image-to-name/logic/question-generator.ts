/**
 * 看图识卦游戏题目生成器
 *
 * 继承基础生成器，只实现特定游戏逻辑
 */

import { getAllHexagrams } from '@/utils/hexagram';
import type { Hexagram } from '@/utils/hexagram';
import type { GameQuestion, GameOption } from './types';
import type { GameConfig } from '@/games/shared/types';
import { BaseQuestionGenerator } from '@/games/shared/base/base-question-generator';

export class HexagramImageToNameQuestionGenerator extends BaseQuestionGenerator<GameQuestion, GameOption> {
  private allHexagrams: Hexagram[];

  constructor(config: GameConfig) {
    super(config);
    this.allHexagrams = getAllHexagrams();
  }

  /**
   * 生成单个题目 - 实现基类的抽象方法
   */
  generateQuestion(index: number): GameQuestion | null {
    // 随机选择卦象
    const targetHexagram = this.selectHexagram();
    if (!targetHexagram) {
      return null;
    }

    // 标记为已使用
    this.markItemAsUsed(targetHexagram.id);

    // 生成选项
    const options = this.generateNameOptions(targetHexagram);
    if (options.length < this.getOptionCount()) {
      return null;
    }

    // 创建题目
    return {
      id: `q${index}`,
      question: '🎨 根据卦象图形选择正确的卦名',
      correctAnswer: targetHexagram.name,
      hexagram: targetHexagram,
      options: options.slice(0, this.getOptionCount()),
    };
  }

  /**
   * 选择卦象
   */
  private selectHexagram(): Hexagram | null {
    const availableHexagrams = this.allHexagrams.filter((hexagram) => !this.isItemUsed(hexagram.id));

    if (availableHexagrams.length === 0) {
      return null;
    }

    const randomIndex = Math.floor(Math.random() * availableHexagrams.length);
    return availableHexagrams[randomIndex];
  }

  /**
   * 生成选项
   */
  private generateNameOptions(correctHexagram: Hexagram): GameOption[] {
    const options: GameOption[] = [];

    // 添加正确选项
    options.push({
      id: `option_${correctHexagram.id}`,
      text: correctHexagram.name,
      isCorrect: true,
    });

    // 生成错误选项
    const wrongOptions = this.generateWrongOptions(correctHexagram);
    options.push(...wrongOptions);

    // 打乱选项顺序
    return this.shuffleArray(options);
  }

  /**
   * 生成错误选项
   */
  private generateWrongOptions(correctHexagram: Hexagram): GameOption[] {
    const wrongOptions: GameOption[] = [];
    const wrongOptionsCount = this.getOptionCount() - 1;

    // 获取所有可用的错误选项候选
    const candidateHexagrams = this.allHexagrams.filter((h) => h.id !== correctHexagram.id);

    // 随机选择错误选项
    const shuffledCandidates = this.shuffleArray(candidateHexagrams);

    for (let i = 0; i < wrongOptionsCount && i < shuffledCandidates.length; i++) {
      const hexagram = shuffledCandidates[i];
      wrongOptions.push({
        id: `option_${hexagram.id}`,
        text: hexagram.name,
        isCorrect: false,
      });
    }

    return wrongOptions;
  }
}
