/**
 * 看图识卦游戏配置
 */

import type { GameConfig } from '@/games/shared/types';

export const HEXAGRAM_IMAGE_TO_NAME_CONFIG: GameConfig = {
  id: 'hexagram-image-to-name',
  name: '看图识卦',
  description: '根据卦象图形识别卦名',
  icon: '🔮',
  difficulty: 'medium',
  category: 'hexagram',

  // 计时器配置
  timer: {
    totalTime: 300, // 总时间（秒）- 5分钟，每题平均30秒
    warningThreshold: 30, // 警告阈值（秒）- 剩余30秒时警告
    dangerThreshold: 10, // 危险阈值（秒）- 剩余10秒时危险提示
    autoStart: true, // 是否自动开始
    paused: false, // 是否暂停
  },

  // 游戏特定配置
  gameSpecific: {
    questionCount: 10,
    optionCount: 4,
    minScore: 0,
    maxScore: 1000,
    includeDescriptions: false,
  },

  // 评分配置
  scoring: {
    baseScore: 100,
    timeBonus: 50,
    accuracyBonus: 20,
    comboBonus: 10,
    difficultyMultiplier: 1.0,
    perfectBonus: 200,
  },

  // 成就配置
  achievements: [
    {
      id: 'first_correct',
      name: '初识卦象',
      description: '答对第一题',
      icon: '🎯',
      condition: { type: 'first_correct' },
    },
    {
      id: 'perfect_game',
      name: '卦象大师',
      description: '全部答对',
      icon: '🏆',
      condition: { type: 'perfect_accuracy', value: 100 },
    },
    {
      id: 'speed_master',
      name: '闪电识卦',
      description: '平均每题用时少于5秒',
      icon: '⚡',
      condition: { type: 'average_time', value: 5 },
    },
    {
      id: 'high_score',
      name: '高分达人',
      description: '单局得分超过800分',
      icon: '💎',
      condition: { type: 'single_game_score', value: 800 },
    },
  ],

  // 排行榜配置
  leaderboard: {
    enabled: true,
    categories: ['score', 'accuracy', 'speed'],
    resetPeriod: 'weekly',
  },

  // UI配置
  ui: {
    showProgress: true,
    showTimer: true,
    showScore: true,
    showCombo: true,
    theme: 'hexagram',
  },
};
