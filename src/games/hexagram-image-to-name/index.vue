<template>
  <PageWrapper :show-back="true" customTitle="🔮 看图识卦">
    <GameController
      :game-logic-class="HexagramImageToNameGameLogic"
      :config="HEXAGRAM_IMAGE_TO_NAME_CONFIG.timer!"
    >
      <!-- 游戏内容插槽 -->
      <template #game-content="{
        question,
        disabled,
        gameActive,
        selectedOptionId,
        isAnswered,
        correctOptionId,
        buttonStates,
        currentQuestionIndex,
        totalQuestions,
        onSelect,
        onConfirm,
        onNext,
        onReset
      }">
        <GameContent
          :question="question"
          :disabled="disabled"
          :game-active="gameActive"
          :selected-option-id="selectedOptionId"
          :is-answered="isAnswered"
          :correct-option-id="correctOptionId"
          :button-states="buttonStates"
          :current-question-index="currentQuestionIndex"
          :total-questions="totalQuestions"
          @select="onSelect"
          @confirm="onConfirm"
          @next="onNext"
          @reset="onReset"
        >
          <!-- 题目内容插槽 -->
          <template #question-content="{ question }">
            <HexagramDisplay
              v-if="question.hexagram"
              :hexagram="question.hexagram"
              :binary="question.hexagram.binary"
              size="large"
              :show-animation="true"
            />
          </template>

          <!-- 选项内容插槽 -->
          <template #option-content="{ option }">
            <text class="option-text">{{ option.text }}</text>
          </template>
        </GameContent>
      </template>
    </GameController>
  </PageWrapper>
</template>

<script setup lang="ts">

  import PageWrapper from '@/components/PageWrapper.vue';
  import HexagramDisplay from '@/components/HexagramDisplay.vue';
  import GameController from '@/games/shared/components/GameController.vue';
  import GameContent from '@/games/shared/components/GameContent.vue';
  import { BaseGameLogic } from '@/games/shared/base/base-game-logic';
  import { HexagramImageToNameQuestionGenerator } from './logic/question-generator';
  import { HEXAGRAM_IMAGE_TO_NAME_CONFIG } from './config';
  import type { GameQuestion, GameOption } from './logic/types';

  // 直接在页面中定义游戏逻辑类
  class HexagramImageToNameGameLogic extends BaseGameLogic<
    GameQuestion,
    GameOption,
    HexagramImageToNameQuestionGenerator
  > {
    constructor() {
      super(
        HEXAGRAM_IMAGE_TO_NAME_CONFIG,
        HexagramImageToNameQuestionGenerator,
        'hexagram-image-to-name'
      );
    }
  }


</script>

<style scoped>


</style>
