import { createSSRApp } from 'vue';
import { createPinia } from 'pinia';
import App from './App.vue';
import initWxPolyfill from './platform/mp-weixin/wx-polyfill';
// 初始化微信小程序API兼容处理
// #ifdef MP-WEIXIN
initWxPolyfill();
// #endif

export function createApp() {
  const app = createSSRApp(App);

  // 配置 Pinia
  const pinia = createPinia();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  app.use(pinia as any);

  return {
    app,
  };
}
