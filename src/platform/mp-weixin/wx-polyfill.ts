/**
 * 微信小程序API兼容处理
 * 解决wx.getSystemInfoSync已弃用的警告
 */

// #ifdef MP-WEIXIN
// 声明wx对象，以解决TypeScript错误
declare const wx: {
  getSystemInfoSync: () => Record<string, unknown>;
  getSystemInfo: (options: {
    success?: (res: Record<string, unknown>) => void;
    fail?: (err: Error) => void;
    complete?: () => void;
  }) => void;
  getWindowInfo?: () => Record<string, unknown>;
  getSystemSetting?: () => Record<string, unknown>;
  getAppBaseInfo?: () => Record<string, unknown>;
  getDeviceInfo?: () => Record<string, unknown>;
  getAppAuthorizeSetting?: () => Record<string, unknown>;
};

// 保存原始方法的引用
const originalGetSystemInfoSync = wx.getSystemInfoSync;
const originalGetSystemInfo = wx.getSystemInfo;

// 获取系统信息的通用方法
function getSystemInfoCompat() {
  try {
    // 组合新API的结果
    const windowInfo = wx.getWindowInfo ? wx.getWindowInfo() : {};
    const systemSetting = wx.getSystemSetting ? wx.getSystemSetting() : {};
    const appBaseInfo = wx.getAppBaseInfo ? wx.getAppBaseInfo() : {};
    const deviceInfo = wx.getDeviceInfo ? wx.getDeviceInfo() : {};
    const appAuthorizeSetting = wx.getAppAuthorizeSetting ? wx.getAppAuthorizeSetting() : {};

    // 合并结果
    return {
      ...windowInfo,
      ...systemSetting,
      ...appBaseInfo,
      ...deviceInfo,
      ...appAuthorizeSetting,
      // 如果有需要特别处理的字段，可以在这里添加
    };
  } catch (e) {
    console.warn('新API调用失败，回退到原始方法', e);
    return null;
  }
}

// 重写getSystemInfoSync方法，使用新的推荐API
wx.getSystemInfoSync = function () {
  // 静默处理，不输出日志以减少控制台噪音

  const compatResult = getSystemInfoCompat();
  if (compatResult) {
    return compatResult;
  }

  // 如果新API不可用，回退到原始方法
  return originalGetSystemInfoSync();
};

// 重写getSystemInfo方法，使用新的推荐API
wx.getSystemInfo = function (options = {}) {
  // 静默处理，不输出日志以减少控制台噪音

  const compatResult = getSystemInfoCompat();
  if (compatResult) {
    // 使用新API成功
    if (options.success) {
      options.success(compatResult);
    }
    if (options.complete) {
      options.complete();
    }
    return;
  }

  // 如果新API不可用，回退到原始方法
  originalGetSystemInfo(options);
};
// #endif

export default function initWxPolyfill() {
  // 初始化时执行的代码
  console.log('微信小程序API兼容处理已加载');

  // 立即执行polyfill，确保在任何其他代码之前生效
  if (typeof wx !== 'undefined') {
    console.log('wx对象已存在，polyfill已生效');
  }

  // 尝试拦截uni对象的getSystemInfo调用
  if (typeof uni !== 'undefined' && uni.getSystemInfo) {
    const originalUniGetSystemInfo = uni.getSystemInfo;
    uni.getSystemInfo = function (options: Record<string, unknown> = {}) {
      // 使用我们的兼容方法
      const compatResult = getSystemInfoCompat();
      if (compatResult) {
        if (options.success) {
          options.success(compatResult);
        }
        if (options.complete) {
          options.complete();
        }
        return;
      }
      // 回退到原始方法
      originalUniGetSystemInfo(options);
    };
  }
}

// 立即执行polyfill（在模块加载时）
if (typeof wx !== 'undefined') {
  console.log('立即执行wx polyfill');
}
