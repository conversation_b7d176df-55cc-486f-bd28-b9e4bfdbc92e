<template>
  <!-- #ifndef MP-WEIXIN -->
  <svg
    class="svg-icon"
    :width="size"
    :height="size"
    :style="{ color: color, display: 'inline-block' }"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    v-html="svgContent"
  ></svg>
  <!-- eslint-disable-line vue/no-v-html -->
  <!-- #endif -->
  <!-- #ifdef MP-WEIXIN -->
  <view :class="['mp-svg-icon', `icon-${name}`]" :style="mpStyle"></view>
  <!-- #endif -->
</template>

<script lang="ts" setup>
  import { computed } from 'vue';

  const props = defineProps({
    name: {
      type: String,
      required: true,
    },
    size: {
      type: [Number, String],
      default: 24,
    },
    color: {
      type: String,
      default: 'currentColor',
    },
  });

  // 统一SVG图标定义（确保内容与小程序CSS中的定义视觉效果一致）
  const svgIcons: Record<string, string> = {
    // 首页图标 - 简约现代房子
    home: `<path d="M12,3 L22,11 L20,11 L20,21 L4,21 L4,11 L2,11 L12,3 Z" fill="none" stroke="currentColor" stroke-width="1.5" />
         <path d="M9,21 L9,14 L15,14 L15,21" fill="none" stroke="currentColor" stroke-width="1.5" />`,

    // 卦典图标 - 精致六爻
    book: `<path d="M6,3 H18 C19.1,3 20,3.9 20,5 L20,19 C20,20.1 19.1,21 18,21 H6 C4.9,21 4,20.1 4,19 L4,5 C4,3.9 4.9,3 6,3 Z" fill="none" stroke="currentColor" stroke-width="1.5"/>
         <line x1="8" y1="7" x2="16" y2="7" stroke="currentColor" stroke-width="1.5" />
         <line x1="8" y1="11" x2="13" y2="11" stroke="currentColor" stroke-width="1.5" />
         <line x1="15" y1="11" x2="16" y2="11" stroke="currentColor" stroke-width="1.5" />
         <line x1="8" y1="15" x2="16" y2="15" stroke="currentColor" stroke-width="1.5" />
         <line x1="8" y1="19" x2="13" y2="19" stroke="currentColor" stroke-width="1.5" />
         <line x1="15" y1="19" x2="16" y2="19" stroke="currentColor" stroke-width="1.5" />`,

    // 游戏图标 - 铜钱占卜
    game: `<circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="1.5"/>
         <circle cx="12" cy="12" r="2.5" fill="none" stroke="currentColor" stroke-width="1.5"/>
         <circle cx="7" cy="12" r="1.5" fill="currentColor"/>
         <circle cx="17" cy="12" r="1.5" fill="currentColor"/>
         <circle cx="12" cy="7" r="1.5" fill="currentColor"/>
         <circle cx="12" cy="17" r="1.5" fill="currentColor"/>`,

    // 个人中心图标 - 现代简约
    user: `<path d="M4,20 C4,17 7,14.5 12,14.5 C17,14.5 20,17 20,20" stroke="currentColor" stroke-width="1.5" fill="none" stroke-linecap="round"/>
         <circle cx="12" cy="8" r="5" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>`,

    // 搜索图标
    search: `<path d="M11,4 C14.866,4 18,7.13401 18,11 C18,12.5704 17.481,14.0204 16.6,15.2 L20,18.6 L18.6,20 L15.2,16.6 C14.0204,17.481 12.5704,18 11,18 C7.13401,18 4,14.866 4,11 C4,7.13401 7.13401,4 11,4 Z M11,6 C8.23858,6 6,8.23858 6,11 C6,13.7614 8.23858,16 11,16 C13.7614,16 16,13.7614 16,11 C16,8.23858 13.7614,6 11,6 Z" fill="currentColor"/>`,

    // 菜单图标
    menu: `<path d="M4,6 H20" stroke="currentColor" stroke-width="2" stroke-linecap="round" /><path d="M4,12 H20" stroke="currentColor" stroke-width="2" stroke-linecap="round" /><path d="M4,18 H20" stroke="currentColor" stroke-width="2" stroke-linecap="round" />`,

    // 设置图标
    settings: `<circle cx="12" cy="12" r="2" fill="currentColor" /><path d="M19.4,13 L18.4,15 L15.6,13.6 L15,16 L12,16 L11.4,13.6 L8.6,15 L7.6,13 L10,11 L7.6,9 L8.6,7 L11.4,8.4 L12,6 L15,6 L15.6,8.4 L18.4,7 L19.4,9 L17,11 L19.4,13 Z" fill="currentColor" />`,

    // 星星图标
    star: `<path d="M12,2 L15.09,8.26 L22,9.27 L17,14.14 L18.18,21.02 L12,17.77 L5.82,21.02 L7,14.14 L2,9.27 L8.91,8.26 L12,2 Z" fill="currentColor" />`,

    // 笔记图标
    note: `<path d="M19,3 H5 C3.89,3 3,3.9 3,5 L3,19 C3,20.1 3.89,21 5,21 L19,21 C20.1,21 21,20.1 21,19 L21,5 C21,3.9 20.1,3 19,3 Z M5,5 H19 V19 H5 V5 Z" fill="currentColor" /><path d="M7,8 H17 M7,12 H17 M7,16 H13" stroke="currentColor" stroke-width="2" />`,

    // 评论图标
    comment: `<path d="M20,4 H4 C2.9,4 2,4.9 2,6 L2,18 L6,14 L20,14 C21.1,14 22,13.1 22,12 L22,6 C22,4.9 21.1,4 20,4 Z M4,6 H20 V12 H5.2 L4,13.2 V6 Z" fill="currentColor" />`,

    // 信息图标
    info: `<path d="M12,2 C17.5228,2 22,6.47715 22,12 C22,17.5228 17.5228,22 12,22 C6.47715,22 2,17.5228 2,12 C2,6.47715 6.47715,2 12,2 Z M12,4 C7.58172,4 4,7.58172 4,12 C4,16.4183 7.58172,20 12,20 C16.4183,20 20,16.4183 20,12 C20,7.58172 16.4183,4 12,4 Z M12,11 C12.5523,11 13,11.4477 13,12 L13,16 C13,16.5523 12.5523,17 12,17 C11.4477,17 11,16.5523 11,16 L11,12 C11,11.4477 11.4477,11 12,11 Z M12,7 C12.5523,7 13,7.44772 13,8 C13,8.55228 12.5523,9 12,9 C11.4477,9 11,8.55228 11,8 C11,7.44772 11.4477,7 12,7 Z" fill="currentColor" />`,

    // 箭头图标
    'arrow-right': `<path d="M10,6 L16,12 L10,18" stroke="currentColor" stroke-width="2" stroke-linecap="round" fill="none" />`,

    // 向上箭头图标
    'arrow-up': `<path d="M6,14 L12,8 L18,14" stroke="currentColor" stroke-width="2" stroke-linecap="round" fill="none" />`,

    // 分享图标
    share: `<circle cx="18" cy="5" r="3" fill="none" stroke="currentColor" stroke-width="1.5" />
         <circle cx="6" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="1.5" />
         <circle cx="18" cy="19" r="3" fill="none" stroke="currentColor" stroke-width="1.5" />
         <line x1="9" y1="10.5" x2="15" y2="6.5" stroke="currentColor" stroke-width="1.5" />
         <line x1="9" y1="13.5" x2="15" y2="17.5" stroke="currentColor" stroke-width="1.5" />`,

    coin: `<ellipse cx="12" cy="8" rx="8" ry="3.5" fill="none" stroke="currentColor" stroke-width="1.5"/><ellipse cx="12" cy="12" rx="8" ry="3.5" fill="none" stroke="currentColor" stroke-width="1.5"/><ellipse cx="12" cy="16" rx="8" ry="3.5" fill="none" stroke="currentColor" stroke-width="1.5"/>`,
    heart: `<path d="M4,12 C4,7 12,7 12,12 C12,7 20,7 20,12 C20,17 12,21 12,21 C12,21 4,17 4,12 Z" fill="none" stroke="currentColor" stroke-width="1.5"/><polyline points="6,13 10,17 14,13 18,17" fill="none" stroke="currentColor" stroke-width="1.5"/>`,
  };

  // 计算SVG内容（H5环境使用）
  const svgContent = computed(() => {
    return svgIcons[props.name] || '';
  });

  // 计算小程序环境样式
  const mpStyle = computed(() => {
    const style: Record<string, string> = {};

    if (props.size) {
      const sizeValue = typeof props.size === 'number' ? `${props.size}px` : props.size;
      style.width = sizeValue;
      style.height = sizeValue;
    }

    if (props.color) {
      style.backgroundColor = props.color;
    } else {
      style.backgroundColor = 'currentColor';
    }

    style.maskSize = 'contain';
    style.webkitMaskSize = 'contain';
    style.maskRepeat = 'no-repeat';
    style.webkitMaskRepeat = 'no-repeat';
    style.maskPosition = 'center';
    style.webkitMaskPosition = 'center';

    return style;
  });
</script>

<style scoped>
  .svg-icon {
    vertical-align: middle;
    color: inherit;
  }

  .mp-svg-icon {
    display: inline-block;
    vertical-align: middle;
    /* 确保小程序环境下的基本样式 */
    background-color: currentColor;
    mask-size: contain;
    -webkit-mask-size: contain;
    mask-repeat: no-repeat;
    -webkit-mask-repeat: no-repeat;
    mask-position: center;
    -webkit-mask-position: center;
  }

  /* 小程序环境SVG图标样式 */
  /* #ifdef MP-WEIXIN */

  /* 首页图标 - 简约现代房子 */
  .icon-home {
    mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12,3 L22,11 L20,11 L20,21 L4,21 L4,11 L2,11 L12,3 Z" fill="none" stroke="black" stroke-width="1.5" /><path d="M9,21 L9,14 L15,14 L15,21" fill="none" stroke="black" stroke-width="1.5" /></svg>');
    -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12,3 L22,11 L20,11 L20,21 L4,21 L4,11 L2,11 L12,3 Z" fill="none" stroke="black" stroke-width="1.5" /><path d="M9,21 L9,14 L15,14 L15,21" fill="none" stroke="black" stroke-width="1.5" /></svg>');
  }

  /* 卦典图标 - 精致六爻 */
  .icon-book {
    mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M6,3 H18 C19.1,3 20,3.9 20,5 L20,19 C20,20.1 19.1,21 18,21 H6 C4.9,21 4,20.1 4,19 L4,5 C4,3.9 4.9,3 6,3 Z" fill="none" stroke="black" stroke-width="1.5"/><line x1="8" y1="7" x2="16" y2="7" stroke="black" stroke-width="1.5" /><line x1="8" y1="11" x2="13" y2="11" stroke="black" stroke-width="1.5" /><line x1="15" y1="11" x2="16" y2="11" stroke="black" stroke-width="1.5" /><line x1="8" y1="15" x2="16" y2="15" stroke="black" stroke-width="1.5" /><line x1="8" y1="19" x2="13" y2="19" stroke="black" stroke-width="1.5" /><line x1="15" y1="19" x2="16" y2="19" stroke="black" stroke-width="1.5" /></svg>');
    -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M6,3 H18 C19.1,3 20,3.9 20,5 L20,19 C20,20.1 19.1,21 18,21 H6 C4.9,21 4,20.1 4,19 L4,5 C4,3.9 4.9,3 6,3 Z" fill="none" stroke="black" stroke-width="1.5"/><line x1="8" y1="7" x2="16" y2="7" stroke="black" stroke-width="1.5" /><line x1="8" y1="11" x2="13" y2="11" stroke="black" stroke-width="1.5" /><line x1="15" y1="11" x2="16" y2="11" stroke="black" stroke-width="1.5" /><line x1="8" y1="15" x2="16" y2="15" stroke="black" stroke-width="1.5" /><line x1="8" y1="19" x2="13" y2="19" stroke="black" stroke-width="1.5" /><line x1="15" y1="19" x2="16" y2="19" stroke="black" stroke-width="1.5" /></svg>');
  }

  /* 游戏图标 - 铜钱占卜 */
  .icon-game {
    mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" fill="none" stroke="black" stroke-width="1.5"/><circle cx="12" cy="12" r="2.5" fill="none" stroke="black" stroke-width="1.5"/><circle cx="7" cy="12" r="1.5" fill="black"/><circle cx="17" cy="12" r="1.5" fill="black"/><circle cx="12" cy="7" r="1.5" fill="black"/><circle cx="12" cy="17" r="1.5" fill="black"/></svg>');
    -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" fill="none" stroke="black" stroke-width="1.5"/><circle cx="12" cy="12" r="2.5" fill="none" stroke="black" stroke-width="1.5"/><circle cx="7" cy="12" r="1.5" fill="black"/><circle cx="17" cy="12" r="1.5" fill="black"/><circle cx="12" cy="7" r="1.5" fill="black"/><circle cx="12" cy="17" r="1.5" fill="black"/></svg>');
  }

  /* 个人中心图标 - 现代简约 */
  .icon-user {
    mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M4,20 C4,17 7,14.5 12,14.5 C17,14.5 20,17 20,20" stroke="black" stroke-width="1.5" fill="none" stroke-linecap="round"/><circle cx="12" cy="8" r="5" fill="none" stroke="black" stroke-width="1.5" stroke-linecap="round"/></svg>');
    -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M4,20 C4,17 7,14.5 12,14.5 C17,14.5 20,17 20,20" stroke="black" stroke-width="1.5" fill="none" stroke-linecap="round"/><circle cx="12" cy="8" r="5" fill="none" stroke="black" stroke-width="1.5" stroke-linecap="round"/></svg>');
  }

  /* 搜索图标 */
  .icon-search {
    mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M11,4 C14.866,4 18,7.13401 18,11 C18,12.5704 17.481,14.0204 16.6,15.2 L20,18.6 L18.6,20 L15.2,16.6 C14.0204,17.481 12.5704,18 11,18 C7.13401,18 4,14.866 4,11 C4,7.13401 7.13401,4 11,4 Z M11,6 C8.23858,6 6,8.23858 6,11 C6,13.7614 8.23858,16 11,16 C13.7614,16 16,13.7614 16,11 C16,8.23858 13.7614,6 11,6 Z" fill="black"/></svg>');
    -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M11,4 C14.866,4 18,7.13401 18,11 C18,12.5704 17.481,14.0204 16.6,15.2 L20,18.6 L18.6,20 L15.2,16.6 C14.0204,17.481 12.5704,18 11,18 C7.13401,18 4,14.866 4,11 C4,7.13401 7.13401,4 11,4 Z M11,6 C8.23858,6 6,8.23858 6,11 C6,13.7614 8.23858,16 11,16 C13.7614,16 16,13.7614 16,11 C16,8.23858 13.7614,6 11,6 Z" fill="black"/></svg>');
  }

  /* 菜单图标 */
  .icon-menu {
    mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M4,6 H20" stroke="black" stroke-width="2" stroke-linecap="round" /><path d="M4,12 H20" stroke="black" stroke-width="2" stroke-linecap="round" /><path d="M4,18 H20" stroke="black" stroke-width="2" stroke-linecap="round" /></svg>');
    -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M4,6 H20" stroke="black" stroke-width="2" stroke-linecap="round" /><path d="M4,12 H20" stroke="black" stroke-width="2" stroke-linecap="round" /><path d="M4,18 H20" stroke="black" stroke-width="2" stroke-linecap="round" /></svg>');
  }

  /* 设置图标 */
  .icon-settings {
    mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><circle cx="12" cy="12" r="2" fill="black" /><path d="M19.4,13 L18.4,15 L15.6,13.6 L15,16 L12,16 L11.4,13.6 L8.6,15 L7.6,13 L10,11 L7.6,9 L8.6,7 L11.4,8.4 L12,6 L15,6 L15.6,8.4 L18.4,7 L19.4,9 L17,11 L19.4,13 Z" fill="black" /></svg>');
    -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><circle cx="12" cy="12" r="2" fill="black" /><path d="M19.4,13 L18.4,15 L15.6,13.6 L15,16 L12,16 L11.4,13.6 L8.6,15 L7.6,13 L10,11 L7.6,9 L8.6,7 L11.4,8.4 L12,6 L15,6 L15.6,8.4 L18.4,7 L19.4,9 L17,11 L19.4,13 Z" fill="black" /></svg>');
  }

  /* 星星图标 */
  .icon-star {
    mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12,2 L15.09,8.26 L22,9.27 L17,14.14 L18.18,21.02 L12,17.77 L5.82,21.02 L7,14.14 L2,9.27 L8.91,8.26 L12,2 Z" fill="black" /></svg>');
    -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12,2 L15.09,8.26 L22,9.27 L17,14.14 L18.18,21.02 L12,17.77 L5.82,21.02 L7,14.14 L2,9.27 L8.91,8.26 L12,2 Z" fill="black" /></svg>');
  }

  /* 笔记图标 */
  .icon-note {
    mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19,3 H5 C3.89,3 3,3.9 3,5 L3,19 C3,20.1 3.89,21 5,21 L19,21 C20.1,21 21,20.1 21,19 L21,5 C21,3.9 20.1,3 19,3 Z M5,5 H19 V19 H5 V5 Z" fill="black" /><path d="M7,8 H17 M7,12 H17 M7,16 H13" stroke="black" stroke-width="2" /></svg>');
    -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19,3 H5 C3.89,3 3,3.9 3,5 L3,19 C3,20.1 3.89,21 5,21 L19,21 C20.1,21 21,20.1 21,19 L21,5 C21,3.9 20.1,3 19,3 Z M5,5 H19 V19 H5 V5 Z" fill="black" /><path d="M7,8 H17 M7,12 H17 M7,16 H13" stroke="black" stroke-width="2" /></svg>');
  }

  /* 评论图标 */
  .icon-comment {
    mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20,4 H4 C2.9,4 2,4.9 2,6 L2,18 L6,14 L20,14 C21.1,14 22,13.1 22,12 L22,6 C22,4.9 21.1,4 20,4 Z M4,6 H20 V12 H5.2 L4,13.2 V6 Z" fill="black" /></svg>');
    -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20,4 H4 C2.9,4 2,4.9 2,6 L2,18 L6,14 L20,14 C21.1,14 22,13.1 22,12 L22,6 C22,4.9 21.1,4 20,4 Z M4,6 H20 V12 H5.2 L4,13.2 V6 Z" fill="black" /></svg>');
  }

  /* 信息图标 */
  .icon-info {
    mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12,2 C17.5228,2 22,6.47715 22,12 C22,17.5228 17.5228,22 12,22 C6.47715,22 2,17.5228 2,12 C2,6.47715 6.47715,2 12,2 Z M12,4 C7.58172,4 4,7.58172 4,12 C4,16.4183 7.58172,20 12,20 C16.4183,20 20,16.4183 20,12 C20,7.58172 16.4183,4 12,4 Z M12,11 C12.5523,11 13,11.4477 13,12 L13,16 C13,16.5523 12.5523,17 12,17 C11.4477,17 11,16.5523 11,16 L11,12 C11,11.4477 11.4477,11 12,11 Z M12,7 C12.5523,7 13,7.44772 13,8 C13,8.55228 12.5523,9 12,9 C11.4477,9 11,8.55228 11,8 C11,7.44772 11.4477,7 12,7 Z" fill="black" /></svg>');
    -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12,2 C17.5228,2 22,6.47715 22,12 C22,17.5228 17.5228,22 12,22 C6.47715,22 2,17.5228 2,12 C2,6.47715 6.47715,2 12,2 Z M12,4 C7.58172,4 4,7.58172 4,12 C4,16.4183 7.58172,20 12,20 C16.4183,20 20,16.4183 20,12 C20,7.58172 16.4183,4 12,4 Z M12,11 C12.5523,11 13,11.4477 13,12 L13,16 C13,16.5523 12.5523,17 12,17 C11.4477,17 11,16.5523 11,16 L11,12 C11,11.4477 11.4477,11 12,11 Z M12,7 C12.5523,7 13,7.44772 13,8 C13,8.55228 12.5523,9 12,9 C11.4477,9 11,8.55228 11,8 C11,7.44772 11.4477,7 12,7 Z" fill="black" /></svg>');
  }

  /* 箭头图标 */
  .icon-arrow-right {
    mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9,6 L15,12 L9,18 L9,15 L12,12 L9,9 Z" fill="black" /></svg>');
    -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9,6 L15,12 L9,18 L9,15 L12,12 L9,9 Z" fill="black" /></svg>');
  }

  /* 向上箭头图标 */
  .icon-arrow-up {
    mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M6,15 L12,9 L18,15 L15,15 L12,12 L9,15 Z" fill="black" /></svg>');
    -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M6,15 L12,9 L18,15 L15,15 L12,12 L9,15 Z" fill="black" /></svg>');
  }

  /* 分享图标 */
  .icon-share {
    mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><circle cx="18" cy="5" r="3" fill="none" stroke="black" stroke-width="1.5" /><circle cx="6" cy="12" r="3" fill="none" stroke="black" stroke-width="1.5" /><circle cx="18" cy="19" r="3" fill="none" stroke="black" stroke-width="1.5" /><line x1="9" y1="10.5" x2="15" y2="6.5" stroke="black" stroke-width="1.5" /><line x1="9" y1="13.5" x2="15" y2="17.5" stroke="black" stroke-width="1.5" /></svg>');
    -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><circle cx="18" cy="5" r="3" fill="none" stroke="black" stroke-width="1.5" /><circle cx="6" cy="12" r="3" fill="none" stroke="black" stroke-width="1.5" /><circle cx="18" cy="19" r="3" fill="none" stroke="black" stroke-width="1.5" /><line x1="9" y1="10.5" x2="15" y2="6.5" stroke="black" stroke-width="1.5" /><line x1="9" y1="13.5" x2="15" y2="17.5" stroke="black" stroke-width="1.5" /></svg>');
  }

  /* 财运图标 - 钱币堆叠 */
  .icon-coin {
    mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><ellipse cx="12" cy="8" rx="8" ry="3.5" fill="none" stroke="black" stroke-width="1.5"/><ellipse cx="12" cy="12" rx="8" ry="3.5" fill="none" stroke="black" stroke-width="1.5"/><ellipse cx="12" cy="16" rx="8" ry="3.5" fill="none" stroke="black" stroke-width="1.5"/></svg>');
    -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><ellipse cx="12" cy="8" rx="8" ry="3.5" fill="none" stroke="black" stroke-width="1.5"/><ellipse cx="12" cy="12" rx="8" ry="3.5" fill="none" stroke="black" stroke-width="1.5"/><ellipse cx="12" cy="16" rx="8" ry="3.5" fill="none" stroke="black" stroke-width="1.5"/></svg>');
  }

  /* 身体健康图标 - 心形 */
  .icon-heart {
    mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M4,12 C4,7 12,7 12,12 C12,7 20,7 20,12 C20,17 12,21 12,21 C12,21 4,17 4,12 Z" fill="none" stroke="black" stroke-width="1.5"/><polyline points="6,13 10,17 14,13 18,17" fill="none" stroke="black" stroke-width="1.5"/></svg>');
    -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M4,12 C4,7 12,7 12,12 C12,7 20,7 20,12 C20,17 12,21 12,21 C12,21 4,17 4,12 Z" fill="none" stroke="black" stroke-width="1.5"/><polyline points="6,13 10,17 14,13 18,17" fill="none" stroke="black" stroke-width="1.5"/></svg>');
  }
  /* #endif */

  /* H5环境强制修复SVG图标显示 */
  /* #ifdef H5 */
  .svg-icon {
    display: inline-block !important;
    vertical-align: middle !important;
    color: inherit !important;
    width: auto !important;
    height: auto !important;
  }

  /* 确保SVG内部元素正确显示颜色 */
  .svg-icon path,
  .svg-icon circle,
  .svg-icon line,
  .svg-icon polyline,
  .svg-icon ellipse {
    stroke: currentColor !important;
    fill: none !important;
  }

  .svg-icon path[fill='currentColor'],
  .svg-icon circle[fill='currentColor'] {
    fill: currentColor !important;
  }

  /* TabBar环境下的特殊处理 */
  .yi-tab-bar .svg-icon path,
  .yi-tab-bar .svg-icon circle,
  .yi-tab-bar .svg-icon line {
    stroke: inherit !important;
  }
  /* #endif */
</style>
