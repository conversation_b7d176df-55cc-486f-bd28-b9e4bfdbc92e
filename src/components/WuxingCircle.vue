<template>
  <view class="wuxing-circle">
    <view class="circle-container">
      <!-- 相生相克关系图 -->
      <svg class="relation-svg" viewBox="0 0 750 750">
        <!-- 相生关系 - 外圆连线（绿色） -->
        <g class="sheng-relations">
          <!-- 相生弧线，形成完整圆形，每段都有箭头 -->
          <!-- 圆心(375,375) 半径250，五行元素在圆周上的精确位置 -->

          <!-- 木→火 (从-90°到-18°，72度弧线) -->
          <path d="M 375,120 A 250 250 0 0 1 600,240"
                stroke="rgba(76, 175, 80, 0.4)" stroke-width="6" fill="none"
                marker-end="url(#arrowhead-green)" />
          <text x="520" y="185" class="relation-text sheng-text">生</text>

          <!-- 火→土 (从-18°到54°，72度弧线) -->
          <path d="M 612,280 A 250 250 0 0 1 570,530"
                stroke="rgba(76, 175, 80, 0.4)" stroke-width="6" fill="none"
                marker-end="url(#arrowhead-green)" />
          <text x="600" y="435" class="relation-text sheng-text">生</text>

          <!-- 土→金 (从54°到126°，72度弧线) -->
          <path d="M 525,575 A 250 250 0 0 1 280,595"
                stroke="rgba(76, 175, 80, 0.4)" stroke-width="6" fill="none"
                marker-end="url(#arrowhead-green)" />
          <text x="375" y="600" class="relation-text sheng-text">生</text>

          <!-- 金→水 (从126°到198°，72度弧线) -->
          <path d="M 225,575 A 250 250 0 0 1 130,355"
                stroke="rgba(76, 175, 80, 0.4)" stroke-width="6" fill="none"
                marker-end="url(#arrowhead-green)" />
          <text x="150" y="435" class="relation-text sheng-text">生</text>

          <!-- 水→木 (从198°到270°，72度弧线) -->
          <path d="M 138,280 A 250 250 0 0 1 320,125"
                stroke="rgba(76, 175, 80, 0.4)" stroke-width="6" fill="none"
                marker-end="url(#arrowhead-green)" />
          <text x="230" y="185" class="relation-text sheng-text">生</text>
        </g>

        <!-- 相克关系直线（橙色） - 形成五角星 -->
        <g class="ke-relations">
          <!-- 木克土 -->
          <line x1="375" y1="165" x2="505" y2="515"
                stroke="rgba(255, 152, 0, 0.4)" stroke-width="6"
                marker-end="url(#arrowhead-orange)" />
          <text x="470" y="370" class="relation-text ke-text">克</text>

          <!-- 土克水 -->
          <line x1="500" y1="520" x2="180" y2="310"
                stroke="rgba(255, 152, 0, 0.4)" stroke-width="6"
                marker-end="url(#arrowhead-orange)" />
          <text x="320" y="400" class="relation-text ke-text">克</text>

          <!-- 水克火 -->
          <line x1="170" y1="300" x2="565" y2="300"
                stroke="rgba(255, 152, 0, 0.4)" stroke-width="6"
                marker-end="url(#arrowhead-orange)" />
          <text x="375" y="295" class="relation-text ke-text">克</text>

          <!-- 火克金 -->
          <line x1="580" y1="300" x2="250" y2="525"
                stroke="rgba(255, 152, 0, 0.4)" stroke-width="6"
                marker-end="url(#arrowhead-orange)" />
          <text x="400" y="450" class="relation-text ke-text">克</text>

          <!-- 金克木 -->
          <line x1="235" y1="555" x2="370" y2="165"
                stroke="rgba(255, 152, 0, 0.4)" stroke-width="6"
                marker-end="url(#arrowhead-orange)" />
          <text x="320" y="340" class="relation-text ke-text">克</text>
        </g>

        <!-- 箭头定义 -->
        <defs>
          <marker id="arrowhead-green" markerWidth="6" markerHeight="4"
                  refX="5" refY="2" orient="auto">
            <polygon points="0 0, 6 2, 0 4" fill="rgba(76, 175, 80, 0.8)" />
          </marker>
          <marker id="arrowhead-orange" markerWidth="6" markerHeight="4"
                  refX="5" refY="2" orient="auto">
            <polygon points="0 0, 6 2, 0 4" fill="rgba(255, 152, 0, 0.8)" />
          </marker>
        </defs>
      </svg>

      <!-- 五行元素 -->
      <view
        v-for="(element, index) in wuxingElements"
        :key="element.name"
        :class="['element-item', `element-${element.name}`, { active: selectedElement === element.name }]"
        :style="getElementPosition(index)"
        @tap="selectElement(element.name)"
      >
        <view :class="['element-icon', getTextSizeClass(element.icon)]">{{ element.icon }}</view>
        <view class="element-name">{{ element.name }}</view>
      </view>
    </view>

    <!-- 图例 -->
    <view class="legend">
      <view class="legend-item">
        <view class="legend-arrow sheng-arrow">→</view>
        <view class="legend-text">相生</view>
      </view>
      <view class="legend-item">
        <view class="legend-arrow ke-arrow">→</view>
        <view class="legend-text">相克</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { wuxingProperties } from '@/utils/wuxing';

  // Props
  const props = defineProps<{
    selectedElement?: string;
    customElements?: Array<{
      name: string;
      icon: string;
    }>;
  }>();

  // Emits
  const emit = defineEmits<{
    selectElement: [elementName: string];
  }>();

  // 使用自定义元素或从 wuxingProperties 生成默认元素
  const wuxingElements = computed(() => {
    if (props.customElements) {
      return props.customElements;
    }

    // 直接从 wuxingProperties 生成元素数据，按照圆环位置排列
    const elementOrder = ['木', '火', '土', '金', '水']; // 顶部、右上、右下、左下、左上
    return elementOrder.map(name => ({
      name,
      icon: wuxingProperties[name].icon
    }));
  });

  // 计算元素位置 - 适中的圆环布局
  const getElementPosition = (index: number) => {
    const positions = [
      { x: 0, y: -270 },    // 木 - 顶部
      { x: 250, y: -80 },   // 火 - 右上
      { x: 150, y: 200 },   // 土 - 右下
      { x: -150, y: 200 },  // 金 - 左下
      { x: -250, y: -80 }   // 水 - 左上
    ];

    const pos = positions[index];
    return {
      transform: `translate(${pos.x}rpx, ${pos.y}rpx)`
    };
  };

  // 选择元素
  const selectElement = (elementName: string) => {
    emit('selectElement', elementName);
  };

  // 根据文字长度返回对应的样式类
  const getTextSizeClass = (text: string) => {
    const length = text.length;
    if (length >= 4) {
      return 'long-text';
    }
    return '';
  };
</script>

<style lang="scss" scoped>
  .wuxing-circle {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 40rpx;
    padding: 20rpx;

    .circle-container {
      position: relative;
      width: 750rpx;
      height: 750rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .relation-svg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
      pointer-events: none;

      .relation-text {
        font-size: 36rpx;
        font-weight: 900;
        text-anchor: middle;
        dominant-baseline: middle;
        stroke: white;
        stroke-width: 4;
        paint-order: stroke fill;
        filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.3));

        &.sheng-text {
          fill: #1B5E20;
        }

        &.ke-text {
          fill: #BF360C;
        }
      }
    }

    .element-item {
      position: absolute;
      width: 120rpx;
      height: 120rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: white;
      border-radius: 50%;
      box-shadow: 0 8rpx 24rpx rgba(139, 69, 19, 0.15);
      border: 6rpx solid;
      transition: all 0.3s ease;
      cursor: pointer;
      z-index: 2;

      &:active {
        transform: scale(0.95);
      }

      &.active {
        box-shadow: 0 12rpx 32rpx rgba(139, 69, 19, 0.25);
        transform: scale(1.5);
        border-width: 8rpx;
      }

      .element-icon {
        font-size: 30rpx;
        font-weight: 600;
        margin-bottom: 6rpx;
        color: #2c3e50;
        text-align: center;
        line-height: 1.2;
        max-width: 100rpx;
        word-break: break-all;
        text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
        letter-spacing: 0.5rpx;

        // 针对长文本自动缩小字体
        &.long-text {
          font-size: 22rpx;
          line-height: 1.4;
          max-width: 160rpx;
          letter-spacing: 0rpx;
        }
      }

      .element-name {
        font-size: 24rpx;
        font-weight: 600;
        color: #8b4513;
        letter-spacing: 1rpx;
        position: relative;
        border-top:2px dotted;
      }

      // 五行元素特定样式 - 使用更柔和的配色
      &.element-木 {
        border-color: rgba(76, 175, 80, 0.5);
        background: linear-gradient(135deg, #e8f5e8, #f0f8f0);

        .element-icon {
          color: #2e7d32;
        }

        .element-name {
          color: #1b5e20;

          &::after {
            background: linear-gradient(90deg, transparent, rgba(27, 94, 32, 0.3), transparent);
          }
        }
      }

      &.element-火 {
        border-color: rgba(244, 67, 54, 0.5);
        background: linear-gradient(135deg, #ffeaea, #fff0f0);

        .element-icon {
          color: #c62828;
        }

        .element-name {
          color: #b71c1c;

          &::after {
            background: linear-gradient(90deg, transparent, rgba(183, 28, 28, 0.3), transparent);
          }
        }
      }

      &.element-土 {
        border-color: rgba(139, 69, 19, 0.5);
        background: linear-gradient(135deg, #f8f4e9, #f0ead6);

        .element-icon {
          color: #6d4c1a;
        }

        .element-name {
          color: #5d4037;

          &::after {
            background: linear-gradient(90deg, transparent, rgba(93, 64, 55, 0.3), transparent);
          }
        }
      }

      &.element-金 {
        border-color: rgba(212, 175, 55, 0.5);
        background: linear-gradient(135deg, #faf8f0, #f5f3e8);

        .element-icon {
          color: #b8860b;
        }

        .element-name {
          color: #9a7c0a;

          &::after {
            background: linear-gradient(90deg, transparent, rgba(154, 124, 10, 0.3), transparent);
          }
        }
      }

      &.element-水 {
        border-color: rgba(33, 150, 243, 0.5);
        background: linear-gradient(135deg, #e3f2fd, #f0f8ff);

        .element-icon {
          color: #1565c0;
        }

        .element-name {
          color: #0d47a1;

          &::after {
            background: linear-gradient(90deg, transparent, rgba(13, 71, 161, 0.3), transparent);
          }
        }
      }
    }

    .legend {
      display: flex;
      justify-content: center;
      gap: 60rpx;
      margin-top: -60rpx;
      padding: 20rpx;

      .legend-item {
        display: flex;
        align-items: center;
        gap: 12rpx;
        background: #fff;
        padding: 8rpx 24rpx;
        border-radius: 20rpx;
        box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.1);

        .legend-arrow {
          font-size: 32rpx;
          font-weight: bold;

          &.sheng-arrow {
            color: #4CAF50;
          }

          &.ke-arrow {
            color: #FF9800;
          }
        }

        .legend-text {
          font-size: 26rpx;
          font-weight: 600;
          color: #6d4c1a;
        }
      }
    }

  }

  // 响应式适配
  @media (max-width: 750rpx) {
    .wuxing-circle {
      padding: 10rpx;

      .circle-container {
        width: 650rpx;
        height: 650rpx;
      }
    }

    .wuxing-circle .element-item {
      width: 100rpx;
      height: 100rpx;

      .element-icon {
        font-size: 26rpx;

        &.long-text {
          font-size: 18rpx;
        }
      }

      .element-name {
        font-size: 20rpx;
      }
    }

    .wuxing-circle .relation-svg .relation-text {
      font-size: 30rpx;
    }

    .wuxing-circle .legend {
      margin-top: -40rpx;

      .legend-item {
        padding: 12rpx 20rpx;

        .legend-arrow {
          font-size: 28rpx;
        }

        .legend-text {
          font-size: 24rpx;
        }
      }
    }
  }
</style>
