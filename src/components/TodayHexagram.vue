<template>
  <view class="today-hexagram-wrapper">
    <!-- 标题与详情链接 -->
    <view class="thxg-title-row">
      <text class="thxg-title">当前卦象</text>
      <text class="thxg-ganzhi"
        >{{ ganZhi.yearTG }}{{ ganZhi.yearDZ }}年 {{ ganZhi.monthTG }}{{ ganZhi.monthDZ }}月 {{ ganZhi.dayTG
        }}{{ ganZhi.dayDZ }}日 {{ ganZhi.hourTG }}{{ ganZhi.hourDZ }}时</text
      >
    </view>

    <!-- 卦名与波浪线装饰 -->
    <view class="thxg-gua-name-row">
      <text class="thxg-wave">~ ~</text>
      <text class="thxg-gua-name">{{ todayHexagram.label || '未知卦象' }}</text>
      <text class="thxg-wave">~ ~</text>
      <view class="thxg-detail-link" @tap="goToDetail">
        <text class="thxg-detail-text">详情</text>
      </view>
    </view>
    <!-- 卦象部分（使用 HexagramDisplay 组件） -->
    <view class="thxg-gua-figure">
      <HexagramDisplay
        :hexagram="todayHexagram"
        size="large"
        :show-animation="true"
      />
    </view>
    <!-- 解卦辞（加引号、居中、棕色） -->
    <view class="thxg-interpret-row">
      <text class="thxg-quote">"</text>
      <text class="thxg-interpret">{{ hexagramInterpretation.meihua }}<text class="thxg-quote">"</text></text>
    </view>
    <!-- 傅佩荣解卦卡片区块 -->
    <view v-if="hexagramInterpretation.fpr" class="thxg-fpr-card-wrap">
      <view class="thxg-fpr-card">
        <view class="thxg-fpr-list">
          <view class="thxg-fpr-item">
            <view class="icon-wrapper">
              <YiIcon name="user" :size="32" color="#bfa16a" />
            </view>
            <text class="thxg-fpr-title">时运</text>
            <text class="thxg-fpr-text">{{ fprParsed.shiyun }}</text>
          </view>
          <view class="thxg-fpr-item">
            <view class="icon-wrapper">
              <YiIcon name="coin" :size="32" color="#bfa16a" />
            </view>
            <text class="thxg-fpr-title">财运</text>
            <text class="thxg-fpr-text">{{ fprParsed.caiyun }}</text>
          </view>
          <view class="thxg-fpr-item">
            <view class="icon-wrapper">
              <YiIcon name="home" :size="32" color="#bfa16a" />
            </view>
            <text class="thxg-fpr-title">家宅</text>
            <text class="thxg-fpr-text">{{ fprParsed.jiazhai }}</text>
          </view>
          <view class="thxg-fpr-item">
            <view class="icon-wrapper">
              <YiIcon name="heart" :size="32" color="#bfa16a" />
            </view>
            <text class="thxg-fpr-title">身体</text>
            <text class="thxg-fpr-text">{{ fprParsed.shenti }}</text>
          </view>
        </view>
      </view>
    </view>
    <!-- 宜/忌分栏卡片 -->
    <view class="thxg-yiji-row">
      <view class="thxg-yiji-line thxg-yi">
        <view class="thxg-yiji-icon thxg-yi-icon">宜</view>
        <text class="thxg-yiji-content">{{ yiJi.yi.join('、') }}</text>
      </view>
      <view class="thxg-yiji-line thxg-ji">
        <view class="thxg-yiji-icon thxg-ji-icon">忌</view>
        <text class="thxg-yiji-content">{{ yiJi.ji.join('、') }}</text>
      </view>
    </view>
    <!-- 其他分栏（财运、家宅、身体）可按需扩展 -->
  </view>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { getHexagramById, getHexagramByBinary } from '@/utils/hexagram';
  import { getHexagramBinary, getTimeHexagram, getGanZhi, getYiJi } from '@/utils/hexagram-time';
  import { getHexagramInterpretation } from '@/utils/hexagram-interpretation';
  import type { Hexagram } from '@/utils/hexagram';

  import YiIcon from '@/components/YiIcon.vue';
  import HexagramDisplay from '@/components/HexagramDisplay.vue';

  // 使用响应式获取当前时间，以便后续可能的更新
  const currentTime = ref(new Date());
  // 使用计算属性获取干支信息
  const ganZhi = computed(() => getGanZhi(currentTime.value));
  // 使用计算属性获取时间卦信息
  const hexagram = computed(() => getTimeHexagram(currentTime.value));

  // 通过计算属性获取当前卦象
  const todayHexagram = computed<Hexagram>(() => {
    try {
      // 生成当前卦象的二进制表示
      const todayBinary = getHexagramBinary(hexagram.value.up, hexagram.value.down);

      // 查找匹配的卦象
      return getHexagramByBinary(todayBinary);
    } catch (error) {
      console.error('获取卦象出错:', error);
      return getHexagramById(1); // 出错时返回乾卦
    }
  });

  // 获取卦象解释
  const hexagramInterpretation = computed(() => {
    return getHexagramInterpretation(todayHexagram.value.id);
  });

  // 通过计算属性获取宜忌
  const yiJi = computed(() => getYiJi(todayHexagram.value));

  const goToDetail = () => {
    uni.navigateTo({
      url: `/pages/gua/hexagram/hexagram-detail?id=${todayHexagram.value.id}`,
    });
  };

  // 解析傅佩荣解卦辞为四段，支持多种分隔符和换行
  const fprParsed = computed(() => {
    const fpr = hexagramInterpretation.value.fpr || '';
    // 兼容 <br>、\n、中文标点等
    const clean = fpr.replace(/<br\s*\/?>(\n)?/g, '\n').replace(/。/g, '。\n');
    const lines = clean
      .split(/\n|\r/)
      .map((l) => l.trim())
      .filter(Boolean);
    let shiyun = '',
      caiyun = '',
      jiazhai = '',
      shenti = '';
    lines.forEach((line) => {
      if (/^时运[:：]/.test(line)) shiyun = line.replace(/^时运[:：]/, '');
      else if (/^财运[:：]/.test(line)) caiyun = line.replace(/^财运[:：]/, '');
      else if (/^家宅[:：]/.test(line)) jiazhai = line.replace(/^家宅[:：]/, '');
      else if (/^身体[:：]/.test(line)) shenti = line.replace(/^身体[:：]/, '');
    });
    return { shiyun, caiyun, jiazhai, shenti };
  });
</script>

<style scoped lang="scss">
  .today-hexagram-wrapper {
    position: relative;
    border-radius: 28rpx;
    overflow: hidden;
    background: #fdfcf7;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.07);
    padding: 36rpx 20rpx;
    margin-bottom: 20rpx;
    color: #a97c50;
    border-top: 4rpx solid #a97c50;
    border-bottom: 4rpx solid #a97c50;
  }

  .thxg-title-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16rpx;
    margin-bottom: 32rpx;
  }

  .thxg-detail-link {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    color: #a97c50;
    font-size: 28rpx;
    margin-right: 16rpx;
    cursor: pointer;
    display: flex;
    align-items: center;
    text-decoration: underline;
  }

  .thxg-title-ganzhi-row {
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 4rpx;
    font-size: 32rpx;
    color: #a97c50;
    padding: 0 16rpx 32rpx;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="4" viewBox="0 0 200 4"><path d="M0,2 Q20,0 40,2 Q60,4 80,2 Q100,0 120,2 Q140,4 160,2 Q180,0 200,2" fill="none" stroke="%23d2b48c" stroke-width="0.5"/></svg>')
      bottom repeat-x;
  }
  .thxg-title {
    color: #6d4c1a;
  }
  .thxg-gua-name-row {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 20rpx 0 0 0;
    position: relative;
  }
  .thxg-wave {
    color: #c7a16b;
    font-size: 48rpx;
    font-family: serif;
  }
  .thxg-gua-name {
    font-size: 48rpx;
    color: #865404;
    font-weight: bold;
    margin: 0 20rpx;
    letter-spacing: 4rpx;
  }
  .thxg-gua-figure {
    display: flex;
    justify-content: center;
    margin: 36rpx 0 20rpx 0;
  }
  .thxg-interpret-row {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    margin: 44rpx 0 36rpx 0;
    font-size: 36rpx;
    color: #a97c50;
    line-height: 1.6;
    text-align: left;
    padding: 40rpx 0;
    background:
      url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="4" viewBox="0 0 200 4"><path d="M0,2 Q20,4 40,2 Q60,0 80,2 Q100,4 120,2 Q140,0 160,2 Q180,4 200,2" fill="none" stroke="%23d2b48c" stroke-width="0.5"/></svg>')
        top repeat-x,
      url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="4" viewBox="0 0 200 4"><path d="M0,2 Q20,0 40,2 Q60,4 80,2 Q100,0 120,2 Q140,4 160,2 Q180,0 200,2" fill="none" stroke="%23d2b48c" stroke-width="0.5"/></svg>')
        bottom repeat-x;
  }
  .thxg-quote {
    font-size: 36rpx;
    color: #e2c9a5;
    margin: 0 12rpx;
    line-height: 1;
  }
  .thxg-interpret {
    max-width: 90vw;
    color: #a97c50;
    font-size: 30rpx;
    font-weight: 500;
    // letter-spacing: 1px;
  }
  .thxg-yiji-row {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20rpx;
    margin-top: 20rpx;
  }
  .thxg-yiji-line {
    display: flex;
    align-items: center;
    width: 100%;
    border-radius: 20rpx;
    padding: 20rpx 20rpx 16rpx 20rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
    margin-bottom: 0;
  }
  .thxg-yi {
    background: #e6f4ea;
    color: #2e7d32;
  }
  .thxg-ji {
    background: #fbeaea;
    color: #b71c1c;
  }
  .thxg-yiji-content {
    font-size: 28rpx;
    text-align: left;
    word-break: break-all;
    margin-left: 20rpx;
  }
  .thxg-yiji-icon {
    width: 48rpx;
    height: 48rpx;
    border-radius: 30%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    font-weight: bold;
    color: #fff;
    opacity: 0.8;
  }
  .thxg-yi-icon {
    background: #4caf50;
  }
  .thxg-ji-icon {
    background: #e53935;
  }
  .container {
    /* min-height: 100vh; */
    min-height: 100%;
    background-color: #f8f4e9;
    padding: 40rpx;
    position: relative;
    padding-bottom: 120rpx; /* 预留tabbar高度，按实际调整 */
  }
  .thxg-fpr-card-wrap {
    position: relative;
    padding: 0 0 12rpx 24rpx;
  }
  .thxg-fpr-curve,
  .thxg-fpr-curve-top,
  .thxg-fpr-curve-bottom {
    display: none;
  }
  .thxg-fpr-card {
    background: none;
    border-radius: 0;
    box-shadow: none;
    padding: 0;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
  .thxg-fpr-list {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 8rpx;
  }
  .thxg-fpr-item {
    display: flex;
    align-items: center;
    gap: 12rpx;
    margin-bottom: 0;
    padding: 0;
  }
  .thxg-fpr-svg-wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin-right: 8rpx;
  }
  .thxg-fpr-title {
    font-size: 28rpx;
    color: #a97c50;
    min-width: 88rpx;
  }
  .thxg-fpr-text {
    font-size: 28rpx;
    color: #6d4c1a;
    font-weight: 400;
    word-break: break-all;
  }

  .icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin-right: 8rpx;
  }
</style>
