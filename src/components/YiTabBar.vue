<template>
  <view class="yi-tab-bar">
    <view
      v-for="(item, index) in tabs"
      :key="index"
      class="tab-item"
      :class="{ active: currentTab === item.pagePath }"
      :style="{ color: currentTab === item.pagePath ? props.activeColor : props.inactiveColor }"
      @tap="switchTab(item.pagePath)"
    >
      <!-- 所有环境都使用SVG图标 -->
      <YiIcon :name="item.icon" :size="24" color="currentColor" />
      <text class="tab-text">{{ item.text }}</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
  import { ref, onMounted, onBeforeUnmount } from 'vue';
  import YiIcon from './YiIcon.vue';

  const props = defineProps({
    activeColor: {
      type: String,
      default: '#8B4513',
    },
    inactiveColor: {
      type: String,
      default: '#666666',
    },
  });

  const currentTab = ref<string>('');
  const tabs = [
    { text: '首页', pagePath: 'pages/index/index', icon: 'home' },
    { text: '卦典', pagePath: 'pages/gua/index', icon: 'book' },
    { text: '游戏', pagePath: 'pages/game/index', icon: 'game' },
    { text: '我的', pagePath: 'pages/user/index', icon: 'user' },
  ];

  // 更新当前激活的tab
  const updateCurrentTab = () => {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];

    if (currentPage) {
      let route = (currentPage as { route?: string }).route || '';
      if (route.startsWith('/')) route = route.slice(1);
      route = route.split('?')[0];

      console.log('🔧 TabBar检测当前路径:', route);

      const matchedTab = tabs.find((tab) => tab.pagePath === route);

      if (matchedTab) {
        console.log('🔧 TabBar匹配到tab:', matchedTab.text, matchedTab.pagePath);
        currentTab.value = matchedTab.pagePath;
      } else {
        console.log('🔧 TabBar未匹配到tab，使用默认首页');
        currentTab.value = tabs[0].pagePath;
      }
    }
  };

  onMounted(() => {
    updateCurrentTab();

    // 监听页面切换事件
    uni.$on('tabChange', (path: string) => {
      console.log('🔧 TabBar收到切换事件:', path);
      if (path) {
        if (path.startsWith('/')) path = path.slice(1);
        currentTab.value = path;
      }
    });

    // 监听页面显示事件，确保状态正确
    uni.$on('onShow', () => {
      console.log('🔧 TabBar页面显示，重新检测状态');
      setTimeout(updateCurrentTab, 100); // 延迟一点确保页面已完全加载
    });
  });

  // 组件卸载时移除事件监听
  onBeforeUnmount(() => {
    uni.$off('tabChange');
    uni.$off('onShow');
  });

  const switchTab = (path: string) => {
    if (currentTab.value !== path) {
      console.log('🔧 TabBar切换到:', path);

      // 立即更新状态，避免延迟
      currentTab.value = path;

      uni.switchTab({
        url: '/' + path,
        success: () => {
          console.log('🔧 TabBar切换成功:', path);
          // 确保状态正确
          currentTab.value = path;
          uni.$emit('tabChange', path);

          // 延迟再次确认状态，处理可能的异步问题
          setTimeout(() => {
            updateCurrentTab();
          }, 200);
        },
        fail: (err) => {
          console.error('🔧 TabBar切换失败:', err);
          // 切换失败时恢复状态
          updateCurrentTab();
        }
      });
    }
  };
</script>

<!-- 全局样式：隐藏原生tabBar -->
<style lang="scss">
  /* H5环境TabBar处理 */
  /* #ifdef H5 */
  /* 强制隐藏原生tabBar，确保自定义TabBar正常显示 */
  uni-tabbar,
  .uni-tabbar,
  .uni-tabbar-border,
  .uni-app--showtabbar .uni-tabbar,
  .uni-tabbar__bd,
  .uni-tabbar__item,
  .uni-tabbar__icon,
  .uni-tabbar__label,
  .uni-tabbar__reddot,
  .uni-tabbar__badge {
    display: none !important;
    visibility: hidden !important;
    height: 0 !important;
    width: 0 !important;
    opacity: 0 !important;
    position: absolute !important;
    top: -19998rpx !important;
    left: -19998rpx !important;
    z-index: -1 !important;
  }

  /* 确保页面内容不被原生tabBar影响 */
  .uni-app--showtabbar .uni-page-wrapper,
  .uni-page-wrapper {
    padding-bottom: 0 !important;
  }

  /* 强制覆盖可能的原生TabBar样式 */
  body .uni-tabbar,
  #app .uni-tabbar,
  .uni-app .uni-tabbar {
    display: none !important;
  }
  /* #endif */
</style>

<!-- 组件样式 -->
<style scoped lang="scss">
  .yi-tab-bar {
    display: flex;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 112rpx;
    background-color: #ffffff; /* 统一设置白色背景 */
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
    z-index: 999;

    /* H5环境确保在原生tabBar之上 */
    /* #ifdef H5 */
    z-index: 999999 !important;
    /* #endif */
  }

  .tab-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 12rpx 8rpx;
    position: relative;
    transition: all 0.3s ease;
    cursor: pointer;

    /* H5环境悬停效果 */
    /* #ifdef H5 */
    &:hover:not(.active) {
      opacity: 0.7;
    }
    /* #endif */

    &.active {
      /* 激活状态只显示底部指示条，不改变背景 */
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 40rpx;
        height: 6rpx;
        background-color: v-bind(activeColor);
        border-radius: 6rpx;
      }
    }
  }

  .tab-text {
    font-size: 24rpx;
    margin-top: 4rpx;
    color: inherit;
    transition: color 0.3s ease;
  }

  .icon-emoji {
    font-size: 40rpx;
    line-height: 1;
    display: block;
    text-align: center;
  }

  // H5环境强制修复样式
  /* #ifdef H5 */
  .yi-tab-bar {
    display: flex !important;
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    height: 112rpx !important;
    background-color: #ffffff !important; /* 确保H5有白色背景 */
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05) !important;
    z-index: 999999 !important; /* 提高层级，确保在原生TabBar之上 */
    font-family: inherit !important;
    border: none !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .tab-item {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 8rpx 0 !important;
    position: relative !important;
    transition: all 0.3s ease !important;
  }

  .tab-text {
    font-size: 24rpx !important;
    margin-top: 4rpx !important;
    transition: color 0.3s ease !important;
    display: block !important;
  }

  /* 强制确保H5环境下颜色与小程序一致 */
  .tab-item.active {
    color: #8b4513 !important; /* 激活状态：棕色 */
  }

  .tab-item:not(.active) {
    color: #666666 !important; /* 非激活状态：深灰色 */
  }

  .tab-item.active .yi-icon-wrapper,
  .tab-item.active .svg-icon,
  .tab-item.active .tab-text {
    color: #8b4513 !important;
  }

  .tab-item:not(.active) .yi-icon-wrapper,
  .tab-item:not(.active) .svg-icon,
  .tab-item:not(.active) .tab-text {
    color: #666666 !important;
  }
  /* #endif */
</style>
