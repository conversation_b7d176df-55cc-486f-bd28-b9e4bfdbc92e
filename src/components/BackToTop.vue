<template>
  <view v-if="show" class="back-to-top" @tap="backToTop">
    <text style="color: #fff; font-size: 48rpx; font-weight: bold">↑</text>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        show: false,
        windowHeight: 0,
      };
    },
    created() {
      // #ifdef MP-WEIXIN
      uni.getSystemInfo({
        success: (info) => {
          this.windowHeight = info.windowHeight;
        },
        fail: () => {
          this.windowHeight = 667; // 默认值
        },
      });
      // #endif
    },
    mounted() {
      // H5环境监听
      // #ifdef H5
      window.addEventListener('scroll', this.onScroll);
      // #endif
      // 小程序环境监听
      // #ifdef MP-WEIXIN
      if (typeof uni !== 'undefined' && uni.$on) {
        uni.$on('onPageScroll', this.onPageScrollMP);
      }
      // #endif
    },
    beforeUnmount() {
      // #ifdef H5
      window.removeEventListener('scroll', this.onScroll);
      // #endif
      // #ifdef MP-WEIXIN
      if (typeof uni !== 'undefined' && uni.$off) {
        uni.$off('onPageScroll', this.onPageScrollMP);
      }
      // #endif
    },
    methods: {
      onScroll() {
        // #ifdef H5
        this.show = window.scrollY > window.innerHeight;
        // #endif
      },
      onPageScrollMP(e) {
        // #ifdef MP-WEIXIN
        this.show = e.scrollTop > this.windowHeight;
        // #endif
      },
      backToTop() {
        // #ifdef H5
        window.scrollTo({ top: 0, behavior: 'smooth' });
        // #endif
        // #ifdef MP-WEIXIN
        uni.pageScrollTo({ scrollTop: 0, duration: 300 });
        // #endif
      },

      // 供外部调用的方法
      setMPShow(scrollTop) {
        // #ifdef MP-WEIXIN
        this.show = scrollTop > this.windowHeight;
        // #endif
      },
    },
  };
</script>

<style scoped>
  .back-to-top {
    position: fixed;
    right: 64rpx;
    bottom: 160rpx;
    z-index: 999;
    width: 96rpx;
    height: 96rpx;
    background-color: #865404;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.6;
    cursor: pointer;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
    transition: opacity 0.2s;
  }
</style>
