<template>
  <view class="page-wrapper" :class="{ 'no-safe-area': !needsSafeArea }">
    <!-- 状态栏占位 -->
    <view v-if="needsSafeArea" class="status-bar" :style="{ height: statusBarHeight + 'rpx' }"></view>

    <!-- 统一的自定义导航栏 -->
    <view v-if="showNavBar" class="unified-nav-bar" :style="unifiedNavBarStyles">
      <!-- 微信小程序状态栏区域 -->
      <!-- #ifdef MP-WEIXIN -->
      <view class="status-bar-area"></view>
      <!-- #endif -->



      <!-- 导航内容区域 -->
      <view class="nav-content" :style="navContentStyles">
        <!-- 左侧内容 -->
        <view class="nav-left">
          <!-- 返回按钮 -->
          <view v-if="showBack" class="back-button" @tap="handleBack">
            <YiIcon name="arrow-right" :size="30" style="transform: rotate(180deg)" />
          </view>

          <!-- Logo -->
          <view v-if="showLogo" class="logo-container">
            <image :src="logoSrc" class="logo-image" mode="aspectFit" />
            <!-- Logo标签（紧邻Logo右侧） -->
            <text v-if="logoLabel" class="logo-label">{{ logoLabel }}</text>
          </view>

          <slot name="nav-left"></slot>
        </view>

        <!-- 中间标题 -->
        <view class="nav-center">
          <!-- 自定义装饰标题 -->
          <view v-if="customTitle" class="custom-title">
            <text class="decoration-text">{{ customTitle }}</text>
          </view>
          <!-- 普通标题 -->
          <text v-else-if="title" class="nav-title">{{ title }}</text>
          <!-- 插槽标题 -->
          <slot v-else name="nav-center"></slot>
        </view>

        <!-- 右侧内容 -->
        <view class="nav-right">
          <slot name="nav-right"></slot>
        </view>
      </view>
    </view>

    <!-- 导航栏调试组件 -->
    <NavDebugger
      v-if="navDebugSettings.enabled"
      :show-toggle-btn="navDebugSettings.showToggleBtn"
      :debug-info="debugInfo"
      :platform="platform"
      :enabled="navDebugSettings.enabled"
    />

    <!-- 页面内容 -->
    <view class="page-content" :style="contentStyle">
      <slot></slot>
    </view>

    <!-- 底部安全区域 -->
    <view
      v-if="includeBottom && needsSafeArea"
      class="safe-area-bottom"
      :style="{ height: safeAreaBottom + 'rpx' }"
    ></view>
  </view>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue';
  import { getSafeAreaInfo, needsSafeAreaAdaptation, getNavBarLayoutInfo, getCapsuleButtonInfo } from '@/utils/safe-area';
  import YiIcon from './YiIcon.vue';
  import NavDebugger from './NavDebugger.vue';

  interface Props {
    // 是否显示自定义导航栏
    showNavBar?: boolean;
    // 导航栏标题
    title?: string;
    // 自定义装饰标题（优先级高于title）
    customTitle?: string;
    // 是否显示Logo
    showLogo?: boolean;
    // Logo源地址
    logoSrc?: string;
    // Logo标签文字（显示在Logo右侧）
    logoLabel?: string;
    // 是否显示返回按钮
    showBack?: boolean;
    // 是否包含底部安全区域
    includeBottom?: boolean;
    // 导航栏背景色
    navBarBg?: string;
    // 页面背景色
    pageBg?: string;
    // 强制禁用安全区域（用于特殊页面）
    disableSafeArea?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    showNavBar: true,
    title: '',
    customTitle: '',
    showLogo: false,
    logoSrc: '/static/logo.png',
    logoLabel: '',
    showBack: true,
    includeBottom: false,
    navBarBg: '#f8f4e9',
    pageBg: '#f8f4e9',
    disableSafeArea: false,
  });

  const emit = defineEmits<{
    back: [];
  }>();

  // 安全区域信息
  const statusBarHeight = ref(0);
  const navigationBarHeight = ref(0);
  const safeAreaTop = ref(0);
  const safeAreaBottom = ref(0);
  const needsSafeArea = ref(false);

  // 调试信息
  const debugInfo = ref<any>({});
  const capsuleInfo = ref<any>(null);
  const navLayoutInfo = ref<any>(null);

  // 调试设置
  const navDebugSettings = ref({
    enabled: false,
    showToggleBtn: false
  });

  // 平台信息
  const platform = ref('Unknown');

  // 统一导航栏区域样式
  const unifiedNavBarStyles = computed(() => {
    const bgColor = props.navBarBg || '#f8f4e9';

    // 根据平台设置不同的高度和固定策略
    let height = '100rpx'; // H5默认高度
    let extraStyles = '';

    // #ifdef MP-WEIXIN
    height = '176rpx'; // 小程序高度（状态栏 + 导航栏）
    // 小程序专用的强固定定位策略
    extraStyles = `
      transform: translateZ(0);
      -webkit-transform: translateZ(0);
      will-change: transform;
      -webkit-overflow-scrolling: auto;
      overflow: hidden;
    `;
    // #endif

    // #ifndef MP-WEIXIN
    // H5平台使用标准的硬件加速
    extraStyles = `
      transform: translate3d(0, 0, 0);
      -webkit-transform: translate3d(0, 0, 0);
      backface-visibility: hidden;
      -webkit-backface-visibility: hidden;
    `;
    // #endif

    return `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      width: 100%;
      height: ${height};
      background-color: ${bgColor};
      z-index: 9999;
      ${extraStyles}
    `;
  });

  // 导航内容区域样式
  const navContentStyles = computed(() => {
    let top = '20rpx'; // H5平台顶部位置
    let rightMargin = '32rpx'; // H5平台右边距
    let height = '64rpx';
    let debugBorder = '';

    // #ifdef MP-WEIXIN
    if (navLayoutInfo.value && navLayoutInfo.value.capsule) {
      // 使用精确的胶囊按钮对齐计算
      const capsule = navLayoutInfo.value.capsule;
      const statusBarHeight = navLayoutInfo.value.statusBarHeight;

      // 关键发现：胶囊按钮的top是内容区域的top，但胶囊按钮外面还有边框
      // 胶囊按钮的视觉顶部（包括边框）应该与状态栏底部齐平
      // 所以nav-content应该对齐状态栏高度，而不是胶囊按钮的top

      top = `${statusBarHeight * 2 -8}rpx`; // 使用状态栏高度作为top位置
      height = `${capsule.height * 2}rpx`; // 使用胶囊按钮的高度
      rightMargin = `${(750 - capsule.left * 2 + 32)}rpx`; // 精确计算右边距

      // 调试边框
      if (navDebugSettings.value.enabled) {
        debugBorder = 'border: 2px solid #ff0000;'; // 红色边框用于调试
      }

      console.log('🔧 导航栏对齐调试信息 (修正为状态栏对齐):', {
        胶囊按钮top_rpx: capsule.top * 2,
        胶囊按钮height_rpx: capsule.height * 2,
        状态栏高度_rpx: statusBarHeight * 2,
        nav_content_top: top,
        nav_content_height: height,
        rightMargin: rightMargin,
        关键发现: '胶囊按钮外有边框，视觉顶部与状态栏底部齐平',
        对齐策略: '使用状态栏高度作为nav-content的top值'
      });
    } else {
      // 默认值
      top = '100rpx'; // 小程序状态栏下方
      rightMargin = '220rpx'; // 小程序胶囊按钮避让
      if (navDebugSettings.value.enabled) {
        debugBorder = 'border: 2px solid #ffaa00;'; // 橙色边框表示使用默认值
      }
    }
    // #endif

    // #ifndef MP-WEIXIN
    if (navDebugSettings.value.enabled) {
      debugBorder = 'border: 2px solid #00ff00;'; // 绿色边框表示H5环境
    }
    // #endif

    // 布局方式固定为左对齐
    const justifyContent = 'flex-start';

    return `
      position: absolute;
      top: ${top};
      left: 20rpx;
      right: ${rightMargin};
      height: ${height};
      background-color: ${navDebugSettings.value.enabled ? 'rgba(255, 255, 0, 0.2)' : 'transparent'};
      display: flex;
      align-items: center;
      justify-content: ${justifyContent};
      ${debugBorder}
    `;
  });

  // 加载调试设置
  const loadDebugSettings = () => {
    try {
      // 清除旧的全局存储设置，因为现在调试功能改为页面级别
      uni.removeStorageSync('nav_debug_enabled')
      uni.removeStorageSync('nav_debug_toggle_btn')

      // 默认关闭调试功能
      navDebugSettings.value = {
        enabled: false,
        showToggleBtn: false
      }

      console.log('🔧 PageWrapper调试设置已重置为默认关闭状态')
    } catch (error) {
      console.error('重置调试设置失败:', error)
      // 出错时确保调试功能关闭
      navDebugSettings.value = {
        enabled: false,
        showToggleBtn: false
      }
    }
  }

  // 内容区域样式
  const contentStyle = computed(() => {
    const style: Record<string, string> = {
      backgroundColor: props.pageBg,
    };

    // 如果显示导航栏，需要添加导航栏高度的间距
    if (props.showNavBar) {
      // #ifdef MP-WEIXIN
      style.paddingTop = '100rpx'; // 导航栏高度 + 间距
      // #endif
      // #ifndef MP-WEIXIN
      style.paddingTop = '100rpx'; // H5平台：导航栏高度(100rpx) + 间距(12rpx)
      // #endif
    }
    // 如果不显示导航栏，需要添加安全区域顶部间距
    else if (needsSafeArea.value) {
      style.paddingTop = safeAreaTop.value + 'rpx';
    }

    return style;
  });

  // 返回按钮处理
  const handleBack = () => {
    // 先触发父组件的back事件，让父组件有机会处理自定义逻辑
    emit('back');

    // 获取当前页面栈
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];

    // 检查当前页面路径
    const currentRoute = currentPage?.route || '';

    // 如果当前就在TabBar页面，不执行返回操作
    const tabBarPages = ['pages/index/index', 'pages/gua/index', 'pages/game/index', 'pages/user/index'];

    if (tabBarPages.includes(currentRoute)) {
      console.log('当前在TabBar页面，不执行返回操作');
      return;
    }

    if (pages.length > 1) {
      // 检查上一个页面是否是TabBar页面
      const prevPage = pages[pages.length - 2];
      const prevRoute = prevPage?.route || '';

      if (tabBarPages.includes(prevRoute)) {
        // 如果上一个页面是TabBar页面，使用switchTab
        uni.switchTab({
          url: `/${prevRoute}`,
          fail: (err) => {
            console.warn('切换到TabBar页面失败:', err);
            // 默认回到首页
            uni.switchTab({ url: '/pages/index/index' });
          },
        });
      } else {
        // 普通页面返回
        uni.navigateBack({
          delta: 1,
          fail: (err) => {
            console.warn('返回失败:', err);
            // 返回失败时，跳转到首页
            uni.switchTab({ url: '/pages/index/index' });
          },
        });
      }
    } else {
      // 如果没有历史堆栈，跳转到首页
      uni.switchTab({
        url: '/pages/index/index',
        fail: (err) => {
          console.error('跳转首页失败:', err);
        },
      });
    }
  };



  // 初始化安全区域信息
  onMounted(async () => {
    try {
      // 检查是否需要安全区域适配
      needsSafeArea.value = needsSafeAreaAdaptation() && !props.disableSafeArea;

      if (needsSafeArea.value) {
        const safeAreaInfo = await getSafeAreaInfo();
        statusBarHeight.value = safeAreaInfo.statusBarHeight;
        navigationBarHeight.value = safeAreaInfo.navigationBarHeight;
        safeAreaTop.value = safeAreaInfo.safeAreaTop;
        safeAreaBottom.value = safeAreaInfo.safeAreaBottom;
      }

      // 获取胶囊按钮信息（用于调试和精确对齐）
      // #ifdef MP-WEIXIN
      capsuleInfo.value = getCapsuleButtonInfo();
      navLayoutInfo.value = await getNavBarLayoutInfo();

      console.log('🔧 胶囊按钮信息 (rpx单位):', {
        ...capsuleInfo.value,
        top_rpx: capsuleInfo.value?.top * 2,
        height_rpx: capsuleInfo.value?.height * 2,
        left_rpx: capsuleInfo.value?.left * 2,
        right_rpx: capsuleInfo.value?.right * 2,
        bottom_rpx: capsuleInfo.value?.bottom * 2,
        width_rpx: capsuleInfo.value?.width * 2
      });
      console.log('🔧 导航栏布局信息 (rpx单位):', navLayoutInfo.value);

      // 设置调试信息
      platform.value = 'MP-WEIXIN';
      debugInfo.value = {
        platform: 'MP-WEIXIN',
        capsule: capsuleInfo.value,
        navLayout: navLayoutInfo.value,
        safeArea: {
          statusBarHeight: statusBarHeight.value,
          navigationBarHeight: navigationBarHeight.value,
          safeAreaTop: safeAreaTop.value,
          safeAreaBottom: safeAreaBottom.value
        }
      };
      // #endif

      // #ifndef MP-WEIXIN
      platform.value = 'H5';
      debugInfo.value = {
        platform: 'H5',
        capsule: null,
        navLayout: null,
        safeArea: {
          statusBarHeight: statusBarHeight.value,
          navigationBarHeight: navigationBarHeight.value,
          safeAreaTop: safeAreaTop.value,
          safeAreaBottom: safeAreaBottom.value
        }
      };
      // #endif

      console.log('🔧 PageWrapper调试信息:', debugInfo.value);

    } catch (error) {
      console.warn('获取安全区域信息失败:', error);
      needsSafeArea.value = false;
    }

    // 加载调试设置
    loadDebugSettings();
    console.log('🔧 调试设置加载完成:', navDebugSettings.value);

  });
</script>

<!-- 全局样式：安全区域适配 -->
<style lang="scss">
  /**
 * 安全区域适配样式
 * 解决小程序页面顶部与状态栏、关闭按钮重叠问题
 */

  /* 安全区域变量 */
  .page-wrapper {
    --status-bar-height: 40rpx;
    --safe-area-top: 128rpx;
    --safe-area-bottom: 0rpx;
    --nav-bar-height: 100rpx;
  }

  /* 微信小程序安全区域适配 */
  /* #ifdef MP-WEIXIN */
  .safe-area-top {
    padding-top: constant(safe-area-inset-top);
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-left {
    padding-left: constant(safe-area-inset-left);
    padding-left: env(safe-area-inset-left);
  }

  .safe-area-right {
    padding-right: constant(safe-area-inset-right);
    padding-right: env(safe-area-inset-right);
  }

  /* 状态栏高度适配 */
  .status-bar {
    height: var(--status-bar-height);
    width: 100%;
  }

  /* 导航栏高度适配 */
  .nav-bar {
    height: calc(var(--status-bar-height) + var(--nav-bar-height));
    padding-top: var(--status-bar-height);
  }

  /* 页面容器适配 */
  .page-wrapper {
    min-height: 100vh;
    padding-top: constant(safe-area-inset-top);
    padding-top: env(safe-area-inset-top);
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* 安全区域组合类 */
  .safe-top {
    padding-top: constant(safe-area-inset-top);
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-left {
    padding-left: constant(safe-area-inset-left);
    padding-left: env(safe-area-inset-left);
  }

  .safe-right {
    padding-right: constant(safe-area-inset-right);
    padding-right: env(safe-area-inset-right);
  }

  .safe-all {
    padding-top: constant(safe-area-inset-top);
    padding-top: env(safe-area-inset-top);
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: constant(safe-area-inset-left);
    padding-left: env(safe-area-inset-left);
    padding-right: constant(safe-area-inset-right);
    padding-right: env(safe-area-inset-right);
  }

  .nav-bar {
    height: 128rpx; /* 状态栏40rpx + 导航栏100rpx */
    padding-top: 40rpx;
  }
  /* #endif */

  /* H5环境适配 - 完全禁用安全区域 */
  /* #ifdef H5 */
  .page-wrapper .safe-area-top,
  .page-wrapper .safe-area-bottom,
  .page-wrapper .safe-area-left,
  .page-wrapper .safe-area-right,
  .page-wrapper .safe-top,
  .page-wrapper .safe-bottom,
  .page-wrapper .safe-left,
  .page-wrapper .safe-right,
  .page-wrapper .safe-all {
    padding: 0 !important;
  }

  .page-wrapper .nav-bar {
    height: 100rpx;
    padding-top: 0;
  }

  .page-wrapper .status-bar {
    display: none;
  }

  .page-wrapper {
    --status-bar-height: 0rpx;
    --safe-area-top: 0rpx;
    --safe-area-bottom: 0rpx;
  }
  /* #endif */

  /* APP环境适配 */
  /* #ifdef APP-PLUS */
  .safe-area-top {
    padding-top: constant(safe-area-inset-top);
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-left {
    padding-left: constant(safe-area-inset-left);
    padding-left: env(safe-area-inset-left);
  }

  .safe-area-right {
    padding-right: constant(safe-area-inset-right);
    padding-right: env(safe-area-inset-right);
  }

  .safe-top {
    padding-top: constant(safe-area-inset-top);
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-left {
    padding-left: constant(safe-area-inset-left);
    padding-left: env(safe-area-inset-left);
  }

  .safe-right {
    padding-right: constant(safe-area-inset-right);
    padding-right: env(safe-area-inset-right);
  }

  .safe-all {
    padding-top: constant(safe-area-inset-top);
    padding-top: env(safe-area-inset-top);
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: constant(safe-area-inset-left);
    padding-left: env(safe-area-inset-left);
    padding-right: constant(safe-area-inset-right);
    padding-right: env(safe-area-inset-right);
  }

  .nav-bar {
    height: 128rpx;
    padding-top: 40rpx;
  }
  /* #endif */
</style>

<!-- 组件样式 -->
<style lang="scss" scoped>
  .page-wrapper {
    position: relative;
    width: 100%;
    min-height: 100vh;
    background-color: #f8f4e9;

    &.no-safe-area {
      .status-bar,
      .safe-area-bottom {
        display: none;
      }
    }
  }

  .status-bar {
    width: 100%;
    background-color: inherit;
  }

  .unified-nav-bar {
    /* 样式主要由内联样式控制 */

    .status-bar-area {
      height: 88rpx; /* 状态栏高度 */
    }

    .nav-content {
      /* 样式主要由内联样式控制 */
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 100rpx;
      padding: 0 20rpx;
      position: relative;

      .nav-left {
        display: flex;
        align-items: center;
        min-width: 120rpx;
        gap: 12rpx; /* 返回按钮和Logo之间的间距 */

        .back-button {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 64rpx;
          height: 64rpx;
          border-radius: 50%;
          background: rgba(0, 0, 0, 0.05);
          transition: all 0.3s ease;
          cursor: pointer;
        }
      }

      .nav-center {
        display: flex;
        align-items: center;
        flex: 1;
        justify-content: center;

        /* 小程序环境下考虑胶囊按钮的影响 */
        /* #ifdef MP-WEIXIN */
        margin-left: 60rpx; /* 向左偏移，补偿胶囊按钮占用的空间 */
        /* #endif */

        .nav-title {
          font-size: 36rpx;
          font-weight: 500;
          color: #8b4513;
          line-height: 1.2;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          text-align: center;
          max-width: 400rpx; /* 限制最大宽度，避免过长 */
        }
      }

      .nav-right {
        display: flex;
        align-items: center;
        min-width: 120rpx;
        justify-content: flex-end;
      }

      .logo-container {
        display: flex;
        align-items: center;
        margin-right: 12rpx;

        .logo-image {
          width: 56rpx;
          height: 56rpx;
          border-radius: 12rpx;
          flex-shrink: 0;
        }

        .logo-label {
          font-size: 40rpx;
          font-weight: 500;
          color: #8b4513;
          margin-left: 12rpx;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }

  .safe-area-bottom {
    background: inherit;
  }

  // H5环境特殊处理
  /* #ifdef H5 */
  .page-wrapper {
    .status-bar,
    .safe-area-bottom {
      display: none;
    }

    .page-content {
      min-height: 100vh;
      /* 移除强制的padding-top重置，允许contentStyle设置导航栏间距 */
      padding-bottom: 0 !important;
    }
  }

  /* #endif */

  // 调试模式切换按钮
  .debug-toggle-btn {
    position: fixed;
    top: 120rpx;
    right: 20rpx;
    width: 60rpx;
    height: 60rpx;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    z-index: 10001;
    cursor: pointer;
    border: 2px solid #666;
  }

  // 调试信息样式
  .debug-info {
    position: fixed;
    top: 200rpx;
    right: 20rpx;
    width: 400rpx;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 20rpx;
    border-radius: 10rpx;
    font-size: 24rpx;
    z-index: 10000;
    max-height: 600rpx;
    overflow-y: auto;

    .debug-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10rpx;

      .debug-toggle {
        cursor: pointer;
        font-size: 20rpx;
        padding: 5rpx 10rpx;
        background: rgba(255, 0, 0, 0.3);
        border-radius: 5rpx;
      }
    }

    .debug-title {
      font-weight: bold;
      color: #ffff00;
    }

    .debug-subtitle {
      font-weight: bold;
      margin: 10rpx 0 5rpx 0;
      color: #00ffff;
    }

    .debug-item {
      margin: 5rpx 0;
      font-family: monospace;
    }

    .debug-section {
      margin: 10rpx 0;
      padding-left: 10rpx;
      border-left: 2px solid #666;
    }
  }

  /* 自定义导航栏标题容器 */
.custom-title {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 基础装饰文本样式 */
.decoration-text {
  font-size: 30rpx;
  font-weight: 600;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: 2rpx solid;
  box-shadow: 0 2rpx 8rpx;
  backdrop-filter: blur(10rpx);
  letter-spacing: 1rpx;
  transition: all 0.3s ease;
  color: #8b4513;
  background: linear-gradient(135deg, rgba(139, 69, 19, 0.08), rgba(160, 82, 45, 0.08));
  border-color: rgba(139, 69, 19, 0.4);
  box-shadow: 0 2rpx 8rpx rgba(139, 69, 19, 0.1);
}
</style>
