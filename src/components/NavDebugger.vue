<template>
  <view>
    <!-- 调试模式切换按钮 -->
    <view v-if="showToggleBtn" class="debug-toggle-btn" @tap="toggleDebug">
      {{ showDebug ? '🔧' : '🔧' }}
    </view>

    <!-- 调试信息显示 -->
    <view v-if="showDebug" class="debug-info">
      <view class="debug-header">
        <text class="debug-title">🔧 导航栏调试信息</text>
        <view class="debug-close" @tap="closeDebug">✕</view>
      </view>

      <view class="debug-content">
        <view class="debug-section">
          <view class="debug-subtitle">平台: {{ platform }}</view>
        </view>

        <view v-if="debugInfo.capsule" class="debug-section">
          <view class="debug-subtitle">胶囊按钮:</view>
          <view class="debug-item">top: {{ debugInfo.capsule.top * 2 }}rpx</view>
          <view class="debug-item">height: {{ debugInfo.capsule.height * 2 }}rpx</view>
          <view class="debug-item">left: {{ debugInfo.capsule.left * 2 }}rpx</view>
          <view class="debug-item">bottom: {{ debugInfo.capsule.bottom * 2 }}rpx</view>
          <view class="debug-item">right: {{ debugInfo.capsule.right * 2 }}rpx</view>
          <view class="debug-item">width: {{ debugInfo.capsule.width * 2 }}rpx</view>
        </view>

        <view v-if="debugInfo.safeArea" class="debug-section">
          <view class="debug-subtitle">安全区域:</view>
          <view class="debug-item">状态栏高度: {{ debugInfo.safeArea.statusBarHeight * 2 }}rpx</view>
          <view class="debug-item">导航栏高度: {{ debugInfo.safeArea.navigationBarHeight * 2 }}rpx</view>
        </view>

        <view v-if="debugInfo.navLayout" class="debug-section">
          <view class="debug-subtitle">nav-content计算:</view>
          <view class="debug-item">胶囊内容top: {{ debugInfo.navLayout.capsule ? debugInfo.navLayout.capsule.top * 2 : 'N/A' }}rpx</view>
          <view class="debug-item" style="color: #00ff00;">状态栏高度: {{ debugInfo.navLayout.statusBarHeight ? debugInfo.navLayout.statusBarHeight * 2 : 'N/A' }}rpx</view>
          <view class="debug-item" style="color: #ff6600;">nav-content top: {{ debugInfo.navLayout.statusBarHeight ? debugInfo.navLayout.statusBarHeight * 2 - 8 : 'N/A' }}rpx</view>
          <view class="debug-item">height: {{ debugInfo.navLayout.capsule ? debugInfo.navLayout.capsule.height * 2 : 'N/A' }}rpx</view>
          <view class="debug-item">rightMargin: {{ debugInfo.navLayout.capsule ? (750 - debugInfo.navLayout.capsule.left * 2 + 32) : 'N/A' }}rpx</view>
          <view class="debug-item" style="color: #ffff00;">
            对齐状态: {{ debugInfo.navLayout.capsule ? '✅ 对齐胶囊视觉顶部' : '❌ 使用默认值' }}
          </view>
        </view>

        <!-- 调试信息为空时的提示 -->
        <view v-if="!debugInfo.capsule && !debugInfo.navLayout && !debugInfo.safeArea" class="debug-section">
          <view class="debug-subtitle">⚠️ 调试信息</view>
          <view class="debug-item">调试信息加载中或不可用</view>
          <view class="debug-item">平台: {{ platform }}</view>
          <view class="debug-item">请检查PageWrapper组件是否正确初始化</view>
        </view>
      </view>
    </view>

    <!-- 胶囊按钮参考框（仅在微信小程序环境显示） -->
    <!-- #ifdef MP-WEIXIN -->
    <view v-if="showDebug && debugInfo?.capsule" class="capsule-debug-box" :style="capsuleDebugStyles"></view>

    <!-- 胶囊按钮top位置标记线 -->
    <!-- <view v-if="showDebug && debugInfo?.navLayout?.capsule"
          class="capsule-top-marker"
          :style="`
            position: absolute;
            top: ${debugInfo.navLayout.capsule.top * 2}rpx;
            left: 0;
            right: 0;
            height: 4rpx;
            background: linear-gradient(90deg, #ff0000, #ff6600, #ffaa00);
            z-index: 9999;
            pointer-events: none;
          `">

      <view style="
        position: absolute;
        left: 20rpx;
        top: -40rpx;
        background: rgba(255, 0, 0, 0.9);
        color: white;
        padding: 8rpx 16rpx;
        border-radius: 8rpx;
        font-size: 24rpx;
        font-weight: bold;
        white-space: nowrap;
        z-index: 10000;
      ">
        胶囊top: {{ debugInfo.navLayout.capsule.top * 2 }}rpx
      </view>
    </view> -->

    <!-- 状态栏高度标记线 -->
    <view v-if="showDebug && debugInfo?.navLayout?.statusBarHeight"
          class="status-bar-marker"
          :style="`
            position: absolute;
            top: ${debugInfo.navLayout.statusBarHeight * 2}rpx;
            left: 0;
            right: 0;
            height: 2rpx;
            background: linear-gradient(90deg, #00ff00, #00aa00);
            z-index: 9999;
            pointer-events: none;
          `">
      <!-- 标记文字 -->
      <view style="
        position: absolute;
        right: 20rpx;
        top: -40rpx;
        background: rgba(0, 255, 0, 0.9);
        color: white;
        padding: 8rpx 16rpx;
        border-radius: 8rpx;
        font-size: 24rpx;
        font-weight: bold;
        white-space: nowrap;
        z-index: 10000;
      ">
        状态栏: {{ debugInfo.navLayout.statusBarHeight * 2 }}rpx
      </view>
    </view>
    <!-- #endif -->

    <!-- 非微信小程序环境的提示 -->
    <!-- #ifndef MP-WEIXIN -->
    <view v-if="showDebug" class="platform-notice">
      <view class="notice-content">
        <view class="notice-title">⚠️ 平台提示</view>
        <view class="notice-text">完整的导航栏调试功能仅在微信小程序环境可用</view>
        <view class="notice-text">当前平台: {{ platform }}</view>
      </view>
    </view>
    <!-- #endif -->
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'

interface DebugInfo {
  capsule?: any
  safeArea?: any
  navLayout?: any
}

interface Props {
  showToggleBtn?: boolean
  debugInfo: DebugInfo
  platform: string
  enabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showToggleBtn: false,
  enabled: true
})

const emit = defineEmits<{
  'update:showDebug': [value: boolean]
}>()

const showDebug = ref(true) // 默认显示调试信息

// 胶囊按钮调试框样式
const capsuleDebugStyles = computed(() => {
  if (!props.debugInfo.capsule) return ''
  
  const capsule = props.debugInfo.capsule
  
  return `
    position: absolute;
    top: ${capsule.top * 2}rpx;
    left: ${capsule.left * 2}rpx;
    width: ${capsule.width * 2}rpx;
    height: ${capsule.height * 2}rpx;
    border: 3px dashed #00ff00;
    background: rgba(0, 255, 0, 0.1);
    z-index: 9998;
    pointer-events: none;
  `
})

const toggleDebug = () => {
  showDebug.value = !showDebug.value
  emit('update:showDebug', showDebug.value)
}

const closeDebug = () => {
  showDebug.value = false
  emit('update:showDebug', false)
}

// 监听外部控制
watch(() => props.showToggleBtn, (newVal) => {
  if (!newVal) {
    showDebug.value = false
    emit('update:showDebug', false)
  }
})

// 监听enabled属性变化
watch(() => props.enabled, (newVal) => {
  console.log('🔧 NavDebugger enabled属性变化:', newVal);
  if (!newVal) {
    showDebug.value = false
    emit('update:showDebug', false)
  }
})

// 调试组件加载状态
console.log('🔧 NavDebugger组件已加载:', {
  showToggleBtn: props.showToggleBtn,
  platform: props.platform,
  enabled: props.enabled,
  hasDebugInfo: !!props.debugInfo
})

// 暴露方法给父组件
defineExpose({
  toggleDebug,
  closeDebug
})
</script>

<style scoped lang="scss">
// 调试模式切换按钮
.debug-toggle-btn {
  position: fixed;
  top: 120rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  z-index: 9999;
  cursor: pointer;
  
  &:hover {
    background: rgba(0, 0, 0, 0.9);
  }
}

// 调试信息面板
.debug-info {
  position: fixed;
  top: 200rpx;
  right: 20rpx;
  width: 600rpx;
  max-height: 800rpx;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  border-radius: 16rpx;
  z-index: 9999;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.debug-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #ffff00;
}

.debug-close {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  font-size: 24rpx;
  cursor: pointer;
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
  }
}

.debug-content {
  padding: 20rpx;
  max-height: 600rpx;
  overflow-y: auto;
}

.debug-section {
  margin-bottom: 24rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.debug-subtitle {
  font-size: 26rpx;
  font-weight: bold;
  color: #00ffff;
  margin-bottom: 12rpx;
  border-bottom: 1px solid rgba(0, 255, 255, 0.3);
  padding-bottom: 8rpx;
}

.debug-item {
  font-size: 24rpx;
  line-height: 1.5;
  margin-bottom: 8rpx;
  padding-left: 16rpx;
  color: #ffffff;
  
  &:last-child {
    margin-bottom: 0;
  }
}

/* 胶囊按钮调试框样式通过内联样式动态设置 */

// 平台提示样式
.platform-notice {
  position: fixed;
  top: 200rpx;
  right: 20rpx;
  width: 500rpx;
  background: rgba(255, 165, 0, 0.9);
  border-radius: 16rpx;
  z-index: 9999;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}

.notice-content {
  padding: 30rpx;
}

.notice-title {
  font-size: 28rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 16rpx;
}

.notice-text {
  font-size: 24rpx;
  color: white;
  line-height: 1.5;
  margin-bottom: 8rpx;

  &:last-child {
    margin-bottom: 0;
  }
}
</style>
