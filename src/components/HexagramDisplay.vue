<template>
  <view
    class="hexagram-display"
    :class="{
      interactive: interactive,
      tiny: size === 'tiny',
      small: size === 'small',
      large: size === 'large'
    }"
  >
    <view
      v-for="(yao, index) in yaoLines"
      :key="`${yao}-${index}`"
      :class="['yao', yao === '阳' ? 'yang' : 'yin', { 'with-animation': showAnimation, 'clickable': interactive }]"
      :style="{
        animationDelay: showAnimation ? (index * 0.08) + 's' : '0s',
        animationDuration: '0.4s'
      }"
      @tap="handleYaoClick(index)"
    >
      <view v-if="yao === '阳'" class="yang-line"></view>
      <view v-else class="yin-line">
        <view class="yin-segment"></view>
        <view class="yin-segment"></view>
      </view>
    </view>

    <view v-if="showName && hexagram" class="hexagram-name">
      {{ hexagram.name }}
    </view>
  </view>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import type { Hexagram } from '@/utils/hexagram';

  interface Props {
    hexagram?: Hexagram | null;
    binary?: string; // 二进制字符串，如 "111000"
    size?: 'tiny' | 'small' | 'medium' | 'large';
    interactive?: boolean;
    showName?: boolean;
    showAnimation?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    hexagram: null,
    binary: '',
    size: 'medium',
    interactive: false,
    showName: false,
    showAnimation: false,
  });

  // 定义事件
  const emit = defineEmits<{
    'yao-click': [yaoIndex: number]
  }>();

  // 处理爻线点击
  const handleYaoClick = (yaoIndex: number) => {
    if (props.interactive) {
      emit('yao-click', yaoIndex);
    }
  };

  // 计算爻线数组
  const yaoLines = computed(() => {
    let binaryStr = '';

    if (props.hexagram) {
      binaryStr = props.hexagram.binary;
    } else if (props.binary) {
      binaryStr = props.binary;
    } else {
      return [];
    }

    // 将二进制字符串转换为爻线数组，从下到上排列
    return binaryStr
      .split('')
      .reverse()
      .map((bit: string) => (bit === '1' ? '阳' : '阴'));
  });
</script>

<style lang="scss" scoped>
  .hexagram-display {
    display: flex;
    flex-direction: column-reverse;
    align-items: center;
    gap: 10rpx;

    &.interactive {
      .yao.clickable {
        cursor: pointer;
        transition: all 0.2s ease;
        padding: 8rpx;
        margin: -8rpx;
        border-radius: 8rpx;

        &:hover {
          background: rgba(139, 69, 19, 0.1);
          transform: scale(1.05);
        }

        &:active {
          background: rgba(139, 69, 19, 0.2);
          transform: scale(0.95);
        }
      }
    }

    &.tiny {
      gap: 4rpx;

      .yao {
        &.yang .yang-line {
          width: 50rpx;
          height: 8rpx;
        }

        &.yin .yin-line {
          width: 50rpx;
          justify-content: space-between;

          .yin-segment {
            width: 22rpx;
            height: 8rpx;
          }
        }
      }

      .hexagram-name {
        font-size: 18rpx;
        margin-top: 8rpx;
      }
    }

    &.small {
      gap: 6rpx;

      .yao {
        &.yang .yang-line {
          width: 80rpx;
          height: 12rpx;
        }

        &.yin .yin-line {
          width: 80rpx;
          justify-content: space-between;

          .yin-segment {
            width: 36rpx;
            height: 12rpx;
          }
        }
      }

      .hexagram-name {
        font-size: 24rpx;
        margin-top: 12rpx;
      }
    }

    &.large {
      gap: 16rpx;

      .yao {
        &.yang .yang-line {
          width: 220rpx;
          height: 32rpx;
        }

        &.yin .yin-line {
          width: 220rpx;
          justify-content: space-between;

          .yin-segment {
            width: 99rpx;
            height: 32rpx;
          }
        }
      }

      .hexagram-name {
        font-size: 36rpx;
        margin-top: 28rpx;
      }
    }
  }

  .yao {
    &.with-animation {
      animation: fadeInUp 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
    }

    &.yang .yang-line {
      width: 130rpx;
      height: 20rpx;
      background: linear-gradient(90deg, #865404 0%, #a0522d 100%);
      border-radius: 10rpx;
      box-shadow: 0 4rpx 8rpx rgba(134, 84, 4, 0.3);
      transition: all 0.1s ease;
    }

    &.yin .yin-line {
      display: flex;
      width: 130rpx;
      justify-content: space-between;

      .yin-segment {
        width: 58.5rpx;
        height: 20rpx;
        background: linear-gradient(90deg, #7c7a7a 0%, #999 100%);
        border-radius: 10rpx;
        box-shadow: 0 4rpx 8rpx rgba(124, 122, 122, 0.3);
        transition: all 0.1s ease;
      }
    }
  }

  .hexagram-name {
    font-size: 28rpx;
    font-weight: bold;
    color: #865404;
    margin-top: 20rpx;
    text-align: center;
  }

  @keyframes fadeInUp {
    0% {
      opacity: 0;
      transform: translateY(30rpx) scale(0.8);
    }
    50% {
      opacity: 0.8;
      transform: translateY(-5rpx) scale(1.05);
    }
    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  // 悬停效果
  .hexagram-display.interactive:hover {
    .yao.yang .yang-line {
      background: linear-gradient(90deg, #a0522d 0%, #8b4513 100%);
      box-shadow: 0 8rpx 16rpx rgba(134, 84, 4, 0.4);
    }

    .yao.yin .yin-segment {
      background: linear-gradient(90deg, #999 0%, #7c7a7a 100%);
      box-shadow: 0 8rpx 16rpx rgba(124, 122, 122, 0.4);
    }
  }
</style>
