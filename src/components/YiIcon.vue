<template>
  <view class="yi-icon-wrapper">
    <UniSvgIcon :name="name" :size="size" :color="color" />
  </view>
</template>

<script lang="ts" setup>
  import UniSvgIcon from './UniSvgIcon.vue';

  defineProps({
    name: {
      type: String,
      required: true,
    },
    size: {
      type: [Number, String],
      default: 24,
    },
    color: {
      type: String,
      default: '',
    },
  });
</script>

<style scoped>
  .yi-icon-wrapper {
    display: inline-block;
    line-height: 1;
  }
</style>
