<template>
  <view :class="containerClass">
    <!-- 八卦布局和属性切换 -->
    <view class="bagua-tabs" v-if="showLayoutSwitch">
      <!-- 布局切换 Tab -->
      <view class="layout-tabs">
        <view
          :class="['tab-item', { active: layout === 'xiantian' }]"
          @tap="switchLayout('xiantian')"
        >
          先天八卦
        </view>
        <view
          :class="['tab-item', { active: layout === 'houtian' }]"
          @tap="switchLayout('houtian')"
        >
          后天八卦
        </view>
      </view>

      <!-- 属性切换 Tab -->
      <view class="attribute-tabs">
        <view
          :class="['attr-tab', { active: currentAttribute === 'direction' }]"
          @tap="switchAttribute('direction')"
        >
          方位
        </view>
        <view
          :class="['attr-tab', { active: currentAttribute === 'element' }]"
          @tap="switchAttribute('element')"
        >
          五行
        </view>
        <view
          :class="['attr-tab', { active: currentAttribute === 'family' }]"
          @tap="switchAttribute('family')"
        >
          家人
        </view>
        <view
          :class="['attr-tab', { active: currentAttribute === 'body' }]"
          @tap="switchAttribute('body')"
        >
          身体
        </view>
      </view>
    </view>

    <!-- 八卦环绕布局 -->
    <view
      v-for="gua in positionedBaguaData"
      :key="`${layout}-${gua.name}`"
      :class="['bagua-item', `position-${gua.positionIndex}`]"
      @tap="showGuaDetail(gua)"
    >
      <!-- 卦名（外侧） -->
      <view class="gua-name-outer">{{ gua.name }}（{{ gua.nature }}）</view>

      <!-- 序数（卦名内侧） -->
      <view class="gua-sequence">{{ gua.sequence }}</view>

      <!-- 使用 HexagramDisplay 组件 -->
      <HexagramDisplay
        :binary="gua.binary"
        size="small"
        :show-name="false"
        :interactive="false"
        :show-animation="isAnimating"
      />

      <!-- 动态属性信息（最内侧） -->
      <view class="gua-attribute-inner">{{ getAttributeValue(gua) }}</view>
    </view>

    <!-- 中心提示区域 -->
    <view class="center-hint">
      <view class="hint-icon">👆</view>
      <view class="hint-text">点击卦象</view>
      <view class="hint-subtext">查看详情</view>
    </view>
  </view>

  <!-- 卦象详情弹框 -->
  <view v-if="showDetailModal" class="modal-overlay" @tap="closeDetailModal">
    <view class="modal-content" @tap.stop>
      <view class="modal-header">
        <view class="modal-title">{{ selectedGua?.symbol }} {{ selectedGua?.description }}</view>
        <view class="modal-close" @tap="closeDetailModal">×</view>
      </view>
      <view class="modal-body">
        <view class="gua-detail-content" v-if="selectedGua">
          <!-- 所有属性 -->
          <view class="detail-section">
            <view class="detail-grid">
              <view class="detail-item detail-item-full" v-if="selectedGua.phenomena">
                <view class="detail-label">天象</view>
                <view class="detail-value">{{ selectedGua.phenomena }}</view>
              </view>
              <view class="detail-item detail-item-full" v-if="selectedGua.soma">
                <view class="detail-label">身体</view>
                <view class="detail-value">{{ selectedGua.soma }}</view>
              </view>
              <view class="detail-item detail-item-full" v-if="selectedGua.geography">
                <view class="detail-label">地理</view>
                <view class="detail-value">{{ selectedGua.geography }}</view>
              </view>
              <view class="detail-item detail-item-full" v-if="selectedGua.character">
                <view class="detail-label">人物</view>
                <view class="detail-value">{{ selectedGua.character }}</view>
              </view>
              <view class="detail-item detail-item-full" v-if="selectedGua.personality">
                <view class="detail-label">人事</view>
                <view class="detail-value">{{ selectedGua.personality }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';
  import HexagramDisplay from './HexagramDisplay.vue';
  import {
    getPositionedBaguaData,
    type PositionedBagua
  } from '@/utils/bagua';

  const props = defineProps({
    centerText: {
      type: String,
      default: '太极'
    },
    size: {
      type: String,
      default: 'medium'
    },
    layout: {
      type: String as () => 'xiantian' | 'houtian',
      default: 'xiantian'
    },
    showFamily: {
      type: Boolean,
      default: true
    },
    showDirection: {
      type: Boolean,
      default: true
    },
    showLayoutSwitch: {
      type: Boolean,
      default: false
    }
  });

  // 计算容器类名
  const containerClass = computed(() => {
    return ['bagua-circle', props.size, props.layout];
  });

  const emit = defineEmits(['gua-click', 'layout-change']);

  // 当前显示的属性类型
  const currentAttribute = ref<'direction' | 'element' | 'family' | 'body'>('direction');

  // 动画状态
  const isAnimating = ref(false);

  // 弹框相关状态
  const showDetailModal = ref(false);
  const selectedGua = ref<PositionedBagua | null>(null);

  // 计算定位后的八卦数据
  const positionedBaguaData = computed((): PositionedBagua[] => {
    return getPositionedBaguaData(props.layout);
  });

  // 切换布局
  const switchLayout = (newLayout: 'xiantian' | 'houtian') => {
    isAnimating.value = true;
    setTimeout(() => {
      isAnimating.value = false;
    }, 800);
    emit('layout-change', newLayout);
  };

  // 切换属性显示
  const switchAttribute = (attribute: 'direction' | 'element' | 'family' | 'body') => {
    currentAttribute.value = attribute;
  };

  // 获取当前属性值
  const getAttributeValue = (gua: PositionedBagua): string => {
    switch (currentAttribute.value) {
      case 'direction':
        return gua.direction;
      case 'element':
        return gua.element;
      case 'family':
        return gua.family;
      case 'body':
        return gua.body;
      default:
        return gua.direction;
    }
  };

  // 显示卦象详情弹框
  const showGuaDetail = (gua: PositionedBagua) => {
    selectedGua.value = gua;
    showDetailModal.value = true;
  };

  // 关闭详情弹框
  const closeDetailModal = () => {
    showDetailModal.value = false;
    selectedGua.value = null;
  };


</script>

<style lang="scss" scoped>
  .bagua-circle {
    position: relative;
    width: 620rpx;
    height: 620rpx;
    margin: 0 auto;
    transition: all 0.5s ease;
  }

  .bagua-tabs {
    position: absolute;
    top: -200rpx;
    left: 0rpx;
    right: 0rpx;
    z-index: 20;
    display: flex;
    flex-direction: column;
    gap: 20rpx;

    .layout-tabs {
      display: flex;
      justify-content: center;
      gap: 0;
      background: transparent;
      border-radius: 0;
      padding: 12rpx 0 6rpx;
      box-shadow: none;
      border: none;

      .tab-item {
        flex: 1;
        padding: 12rpx 24rpx;
        text-align: center;
        font-size: 26rpx;
        font-weight: 500;
        background: #f8f9fa;
        color: #6c757d;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid #865404;
        border-right: none; // 移除右边框，避免叠加

        &:first-child {
          border-radius: 12rpx 0 0 12rpx;
        }

        &:last-child {
          border-radius: 0 12rpx 12rpx 0;
          border-right: 1px solid #865404; // 最后一个恢复右边框
        }

        &.active {
          background: #865404;
          color: white;
        }

        &:not(.active):hover {
          background: #e9ecef;
        }

        &:active {
          transform: scale(0.98);
        }
      }
    }

    .attribute-tabs {
      display: flex;
      justify-content: center;
      gap: 12rpx;
      padding: 8rpx 4rpx;
      margin: 16rpx 0;

      .attr-tab {
        flex: 1;
        min-width: 80rpx;
        padding: 8rpx 20rpx;
        background: #faf8f5;
        border: 2rpx solid #e8ddd4;
        border-radius: 12rpx;
        font-size: 22rpx;
        color: #8b4513;
        cursor: pointer;
        transition: all 0.25s ease;
        text-align: center;
        font-weight: 500;
        box-shadow: 0 2rpx 6rpx rgba(139, 69, 19, 0.08);
        position: relative;

        // 增加点击区域
        &::before {
          content: '';
          position: absolute;
          top: -4rpx;
          left: -4rpx;
          right: -4rpx;
          bottom: -4rpx;
          border-radius: 20rpx;
        }

        &.active {
          background: linear-gradient(135deg, #8b4513, #a0522d);
          color: #ffffff;
          border-color: #8b4513;
          font-weight: 600;
          transform: translateY(-1rpx);
          box-shadow: 0 4rpx 12rpx rgba(139, 69, 19, 0.25);
        }

        &:not(.active):active {
          transform: scale(0.97);
          background: #f5f1ec;
          border-color: #d4c4b0;
        }
      }
    }
  }



  // 中心提示区域
  .center-hint {
    position: absolute;
    top: 46%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    z-index: 10;

    .hint-icon {
      font-size: 42rpx;
      margin-bottom: 4rpx;
      opacity: 0.3;
      // animation: bounce 2s infinite;
    }

    .hint-text {
      font-size: 20rpx;
      color: #999;
      margin-bottom: 4rpx;
    }

    .hint-subtext {
      font-size: 20rpx;
      color: #999;
    }
  }

  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-8rpx);
    }
    60% {
      transform: translateY(-4rpx);
    }
  }

  .bagua-item {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
  }

  .gua-name-outer {
    font-size: 28rpx;
    font-weight: 500;
    color: #2c3e50;
    padding-left: 22rpx;
  }

  .gua-sequence {
    font-size: 18rpx;
    font-weight: 400;
    color: #8b4513;
    margin-bottom: 8rpx;
    text-align: center;
    background: rgba(139, 69, 19, 0.1);
    border-radius: 12rpx;
    padding: 4rpx 8rpx;
    min-width: 32rpx;
  }

  .gua-attribute-inner {
    font-size: 22rpx;
    color: #e74c3c;
    margin-top: 8rpx;
    text-align: center;
    font-weight: 500;
    background: rgba(231, 76, 60, 0.1);
    border-radius: 8rpx;
    padding: 2rpx 6rpx;
    min-width: 24rpx;
  }

  .position-0 {
    top: 20rpx;
    left: 50%;
    transform: translateX(-50%);
  }

  .position-1 {
    top: 80rpx;
    right: 80rpx;
    transform: rotate(45deg);
  }

  .position-2 {
    top: 50%;
    right: 20rpx;
    transform: translateY(-50%) rotate(90deg);
  }

  .position-3 {
    bottom: 80rpx;
    right: 80rpx;
    transform: rotate(135deg);
  }

  .position-4 {
    bottom: 20rpx;
    left: 50%;
    transform: translateX(-50%) rotate(180deg);
  }

  .position-5 {
    bottom: 80rpx;
    left: 80rpx;
    transform: rotate(225deg);
  }

  .position-6 {
    top: 50%;
    left: 20rpx;
    transform: translateY(-50%) rotate(270deg);
  }

  .position-7 {
    top: 80rpx;
    left: 80rpx;
    transform: rotate(315deg);
  }



  .bagua-circle.small {
    width: 400rpx;
    height: 400rpx;

    .gua-name-outer {
      font-size: 24rpx;
    }

    .gua-sequence {
      font-size: 20rpx;
      padding: 2rpx 6rpx;
    }

    .gua-attribute-inner {
      font-size: 14rpx;
      padding: 1rpx 4rpx;
    }

    .center-hint {
      .hint-icon {
        font-size: 32rpx;
      }

      .hint-text {
        font-size: 20rpx;
      }

      .hint-subtext {
        font-size: 16rpx;
      }
    }
  }

  .bagua-circle.large {
    width: 800rpx;
    height: 800rpx;

    .gua-name-outer {
      font-size: 40rpx;
    }

    .gua-sequence {
      font-size: 28rpx;
      padding: 6rpx 12rpx;
    }

    .gua-attribute-inner {
      font-size: 22rpx;
      padding: 4rpx 8rpx;
    }

    .center-hint {
      .hint-icon {
        font-size: 64rpx;
      }

      .hint-text {
        font-size: 32rpx;
      }

      .hint-subtext {
        font-size: 24rpx;
      }
    }
  }

  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 40rpx;
  }

  .modal-content {
    background: #ffffff;
    border-radius: 24rpx;
    width: 100%;
    max-width: 600rpx;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
    animation: modalSlideIn 0.3s ease-out;
  }

  @keyframes modalSlideIn {
    from {
      opacity: 0;
      transform: translateY(-50rpx) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx 32rpx 24rpx;
    border-bottom: 1rpx solid #e0e0e0;
  }

  .modal-title {
    font-size: 30rpx;
    font-weight: bold;
    color: #8b4513;
  }

  .modal-close {
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    color: #666;
    cursor: pointer;
    border-radius: 50%;
    transition: all 0.3s ease;

    &:hover {
      background: #f5f5f5;
      color: #333;
    }
  }

  .modal-body {
    padding: 32rpx;
    max-height: 50vh;
    overflow-y: auto;
  }

  .detail-section {
    margin-bottom: 32rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .section-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 16rpx;
    padding-bottom: 8rpx;
    border-bottom: 2rpx solid #8b4513;
  }

  .detail-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16rpx;
  }

  .detail-item {
    display: flex;
    align-items: center;
    padding: 12rpx;
    background: #f8f9fa;
    border-radius: 8rpx;

    &.detail-item-full {
      grid-column: 1 / -1;
      align-items: flex-start;
      flex-direction: column;
      gap: 8rpx;
    }
  }

  .detail-label {
    font-size: 24rpx;
    color: #666;
    margin-right: 16rpx;
    min-width: 60rpx;
    color:#8b4513;
    font-weight: 500;
    text-align: left;
  }

  .detail-value {
    font-size: 24rpx;
    color: #333;
    font-weight: 500;
    text-align: left;
  }

  .detail-description {
    font-size: 26rpx;
    color: #333;
    line-height: 1.6;
    padding: 16rpx;
    background: #f8f4e9;
    border-radius: 12rpx;
    border-left: 4rpx solid #8b4513;
  }

  .modal-footer {
    display: flex;
    gap: 16rpx;
    padding: 24rpx 32rpx 32rpx;
    border-top: 1rpx solid #e0e0e0;
  }

  .footer-button {
    flex: 1;
    padding: 16rpx 24rpx;
    text-align: center;
    font-size: 28rpx;
    font-weight: 500;
    border-radius: 12rpx;
    cursor: pointer;
    transition: all 0.3s ease;

    &.secondary {
      background: #f8f9fa;
      color: #666;
      border: 1rpx solid #e0e0e0;

      &:hover {
        background: #e9ecef;
        color: #333;
      }
    }

    &.primary {
      background: #8b4513;
      color: white;

      &:hover {
        background: #a0522d;
        transform: translateY(-1rpx);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
</style>
