# 易境万象 (Uni-Yi)

## 项目概述
易境万象是一款基于周易卦象的跨平台应用，旨在为用户提供卦象查询、解卦和互动游戏等功能。通过现代化的界面设计和交互体验，让传统易经智慧在数字时代焕发新生。

## 技术栈
- **框架**: 基于uni-app开发，支持多平台部署
- **语言**: TypeScript
- **样式**: SCSS
- **平台支持**: H5、微信小程序、Android、iOS
- **组件库**: 自定义组件 + uni-ui
- **工具库**: lunar-javascript (日期计算)、mp-html (富文本渲染)

## 项目结构
```
uni-yi/
├── src/                      # 源代码目录
│   ├── components/           # 公共组件
│   │   ├── TodayHexagram.vue # 今日卦象组件
│   │   ├── YiIcon.vue        # 图标组件
│   │   ├── UniSvgIcon.vue    # SVG图标组件
│   │   ├── BackToTop.vue     # 返回顶部组件
│   │   └── YiTabBar.vue      # 底部标签栏组件
│   ├── pages/                # 页面目录
│   │   ├── index/            # 首页
│   │   ├── hexagram/         # 卦象相关页面
│   │   ├── game/             # 游戏相关页面
│   │   └── user/             # 用户相关页面
│   ├── utils/                # 工具函数
│   │   ├── hexagram.ts       # 卦象数据和工具
│   │   ├── hexagram-time.ts   # 时间卦算法
│   │   └── hexagram-interpretation.ts # 卦象解释
│   ├── assets/               # 静态资源
│   │   └── *.html            # 卦象详细解释HTML文件
│   └── static/               # 静态文件
│       └── images/           # 图片资源
└── scripts/                  # 脚本工具
```

## 主要功能
1. **今日卦象**: 根据当前时间生成卦象，提供卦象解释和宜忌指导
2. **卦象详情**: 64卦详细解释，包含多种解卦方式(周易正义、梅花易数等)
3. **卦象列表**: 浏览全部64卦
4. **卦象游戏**:
   - 卦象速配: 测试用户对卦象的熟悉程度
   - 卦辞连连看: 考验用户对卦辞和卦名的理解

## 特色亮点
- **跨平台兼容**: 同一套代码，多平台运行
- **精美UI**: 传统与现代结合的界面设计
- **SVG图标**: 使用内联SVG图标，无需额外网络请求
- **多种解卦**: 集成多种易经解读体系
- **时间卦算法**: 基于年、月、日、时计算当前卦象
- **宜忌推算**: 根据卦象特性智能推荐宜忌事项

## 使用说明
1. 安装依赖:
```bash
pnpm install
```

2. 运行开发环境:
```bash
# H5版本
pnpm dev:h5

# 微信小程序版本
pnpm dev:mp-weixin
```

3. 构建生产版本:
```bash
# H5版本
pnpm build:h5

# 微信小程序版本
pnpm build:mp-weixin
```

## 项目规范
- TypeScript类型检查: `pnpm type-check`
- 代码风格检查: `pnpm lint`
- 代码格式化: `pnpm format`
- 单元测试: `pnpm test`