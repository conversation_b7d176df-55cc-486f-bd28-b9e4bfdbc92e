# 开发选项功能说明

## 📱 功能概述

开发选项页面提供了一系列开发和调试工具，帮助开发者管理用户数据、调试问题和分析应用状态。

## 🚀 访问方式

1. 打开应用
2. 点击底部 TabBar 的"我的"
3. 在"开发选项"部分点击"开发选项"
4. 进入开发选项页面

## 🛠️ 功能详解

### 用户数据管理

#### 1. 查看用户数据
- **功能**: 显示当前用户的详细数据
- **用途**: 快速查看用户档案信息
- **操作**: 点击"查看"按钮
- **输出**: 
  - 弹窗显示基本信息
  - 控制台输出完整数据

#### 2. 重置用户数据 ⚠️
- **功能**: 清除所有用户数据，恢复到初始状态
- **用途**: 解决数据异常、测试新用户流程
- **操作**: 点击"重置"按钮 → 确认操作
- **影响**: 
  - ✅ 清除总积分、经验值、等级
  - ✅ 清除游戏记录和最佳分数
  - ✅ 清除成就和统计数据
  - ✅ 保留用户ID（避免重复生成）

#### 3. 导出用户数据
- **功能**: 导出用户数据用于备份或分析
- **用途**: 数据备份、问题分析、数据迁移
- **操作**: 点击"导出"按钮
- **输出**: 控制台输出JSON格式的完整用户数据

### 游戏数据管理

#### 1. 清理游戏历史
- **功能**: 清除所有游戏历史记录
- **用途**: 清理测试数据，保留用户等级
- **操作**: 点击"清理"按钮 → 确认操作
- **影响**: 
  - ✅ 清除游戏历史记录
  - ❌ 保留用户等级和总积分

#### 2. 重新计算积分
- **功能**: 重新计算用户总积分
- **用途**: 修复积分计算错误
- **操作**: 点击"重算"按钮
- **过程**: 遍历所有游戏记录，重新计算总积分

### 系统信息

实时显示当前用户的关键信息：

- **用户ID**: 唯一标识符
- **总积分**: 累计获得的总积分
- **经验值**: 当前经验值
- **等级**: 当前用户等级
- **游戏次数**: 总游戏次数

### 调试日志

- **功能**: 实时显示操作日志
- **类型**: 
  - 🔵 信息 (info)
  - 🟢 成功 (success)  
  - 🟡 警告 (warning)
  - 🔴 错误 (error)
- **管理**: 点击"清除日志"清空记录

## ⚠️ 使用注意事项

### 重要警告

1. **数据不可恢复**: 重置用户数据后无法恢复
2. **谨慎操作**: 仅在必要时使用重置功能
3. **备份数据**: 重置前建议先导出数据备份

### 适用场景

#### 开发测试
- 测试新用户注册流程
- 验证等级计算逻辑
- 清理测试数据

#### 问题调试
- 解决积分计算错误
- 修复数据不一致问题
- 分析用户行为数据

#### 数据维护
- 定期清理无效数据
- 备份重要用户数据
- 迁移用户档案

## 🔧 技术实现

### 重置用户数据流程

```typescript
// 1. 获取当前用户ID
const currentProfile = userProfileManager.getCurrentProfile();
const userId = currentProfile.userId;

// 2. 清除存储数据
uni.removeStorageSync(`user_profile_${userId}`);

// 3. 重新初始化
await userProfileManager.initializeProfile(userId);
```

### 数据导出格式

```json
{
  "exportTime": "2025-01-05T10:30:00.000Z",
  "userData": {
    "userId": "user_1234567890_abc123",
    "username": "易学者",
    "totalScore": 179,
    "experience": 73,
    "level": 1,
    "gamesPlayed": {
      "hexagram-match": 1
    },
    "bestScores": {
      "hexagram-match": 30
    },
    // ... 其他数据
  }
}
```

## 📋 常见问题

### Q: 重置后数据能恢复吗？
A: 不能。重置操作会永久删除用户数据，请谨慎使用。

### Q: 为什么总积分和游戏得分不一致？
A: 可能是积分计算逻辑问题，可以使用"重新计算积分"功能修复。

### Q: 如何备份用户数据？
A: 使用"导出用户数据"功能，将控制台输出的JSON数据保存。

### Q: 开发选项对普通用户可见吗？
A: 是的，但有明显的"DEV"标识，提醒这是开发功能。

## 🎯 最佳实践

1. **测试前备份**: 进行重置操作前先导出数据
2. **分步调试**: 先查看数据，再决定是否重置
3. **记录操作**: 关注调试日志，了解操作结果
4. **验证修复**: 重置后验证问题是否解决

---

**创建时间**: 2025-01-05  
**适用版本**: v1.0.0+  
**维护状态**: 活跃开发中
