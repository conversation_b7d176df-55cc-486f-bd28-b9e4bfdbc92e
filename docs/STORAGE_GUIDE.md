# 简化的存储和状态管理方案

项目采用了 **Pinia + 本地存储** 的轻量级结合方案，只保留核心功能。

## 📁 目录结构

```
src/
├── stores/            # Pinia 状态管理
│   ├── index.ts       # 统一导出
│   ├── app.ts         # 应用运行时状态
│   └── settings.ts    # 用户设置（字体大小）
└── utils/             # 工具函数
    ├── constants.ts   # 项目常量（存储键名等）
    └── storage.ts     # 存储工具（简化版）
```

## 🎯 设计原则

### **分层管理**
- **Pinia**: 管理运行时状态（页面状态、加载状态等）
- **本地存储**: 管理持久化数据（用户设置、历史记录等）
- **自动同步**: 用户设置在 Pinia 和本地存储间自动同步

### **数据分类**
1. **运行时状态** → Pinia（内存中，响应式）
2. **用户设置** → Pinia + 本地存储（响应式 + 持久化）
3. **内容缓存** → 本地存储（带过期时间）

## 📝 使用方式

### **1. 运行时状态 (Pinia)**
```typescript
import { useAppStore } from '@/stores';

const app = useAppStore();

// 设置加载状态
app.setLoading(true, '加载中...');

// 设置当前卦象
app.setCurrentHexagram(hexagram);

// 游戏分数
app.updateGameScore(100);
```

### **2. 用户设置 (Pinia + 自动持久化)**
```typescript
import { useSettingsStore } from '@/stores';

const settings = useSettingsStore();

// 修改设置（自动保存到本地存储）
settings.setFontSize(18);

// 在模板中使用（响应式）
// {{ settings.fontSize }}px
```

### **3. 内容缓存**
```typescript
import { hexagramCache } from '@/utils/storage';

// 卦象内容缓存（24小时过期）
hexagramCache.set(1, content, 24);
const cached = hexagramCache.get(1);
```

### **4. 基础存储工具**
```typescript
import { storage } from '@/utils/storage';
import { STORAGE_KEYS } from '@/utils/constants';

// 基础存储
storage.set('key', value);
const value = storage.get('key', defaultValue);

// 带过期的缓存
storage.setCache('content', data, 12); // 12小时过期
const cached = storage.getCache('content');

// 使用预定义的键名
storage.set(STORAGE_KEYS.FONT_SIZE, 16);
```

## 🔄 数据流向

```
用户操作 → Pinia Store → 自动同步 → 本地存储
    ↓
响应式更新 → UI 界面
```

## 💡 最佳实践

### **什么时候用 Pinia？**
- 需要响应式更新的数据
- 组件间共享的状态
- 需要计算属性的场景
- 临时的运行时状态

### **什么时候用本地存储？**
- 需要持久化的数据
- 用户设置和偏好
- 历史记录
- 内容缓存

### **性能优化**
- 大数据使用缓存过期机制
- 历史记录限制数量（如100条）
- 定期清理过期缓存
- 避免频繁的存储操作

## 🚀 实际应用场景

### **字体大小设置**
```typescript
// 在设置页面
const settings = useSettingsStore();
settings.setFontSize(18); // 自动保存 + 响应式更新

// 在其他页面
const fontSize = settings.fontSize; // 响应式获取
```

### **卦象详情缓存**
```typescript
// 获取卦象详情时
const getCachedContent = async (id: number) => {
  // 先从缓存获取
  let content = hexagramCache.get(id);
  if (!content) {
    // 缓存未命中，从网络获取
    content = await fetchFromNetwork(id);
    hexagramCache.set(id, content, 24); // 缓存24小时
  }
  return content;
};
```

### **浏览历史记录**
```typescript
// 访问卦象详情时
const viewHexagram = (hexagram: Hexagram) => {
  // 设置当前状态
  app.setCurrentHexagram(hexagram);
  
  // 添加到历史记录
  history.addBrowseHistory(hexagram, 'browse');
  
  // 导航到详情页
  uni.navigateTo({ url: `/pages/hexagram/detail?id=${hexagram.id}` });
};
```

这个方案既保持了 uni-app 项目的简洁性，又提供了完整的状态管理和缓存能力，非常适合您的易经应用项目！
