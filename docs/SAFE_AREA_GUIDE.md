# 统一安全区域适配解决方案

## 🎯 问题描述

在小程序和APP中，特别是使用自定义导航栏（`navigationStyle: "custom"`）的页面，容易出现页面内容与以下元素重叠的问题：
- 系统状态栏（时间、电量、信号等）
- 小程序关闭按钮（胶囊按钮）
- 手机刘海屏区域
- 底部安全区域（如iPhone X系列的Home指示器）

**注意：H5环境不需要安全区域适配，本方案会自动跳过H5环境。**

## 🛠️ 解决方案

### 1. 核心工具文件

#### `src/utils/safe-area.ts`
提供获取安全区域信息的工具函数：
- `getStatusBarHeight()` - 获取状态栏高度
- `getNavigationBarHeight()` - 获取导航栏总高度
- `getSafeAreaTop()` - 获取顶部安全区域距离
- `getSafeAreaBottom()` - 获取底部安全区域距离
- `getSafeAreaInfo()` - 获取完整安全区域信息

#### `src/components/PageWrapper.vue`
提供CSS工具类和变量（已集成在组件中）：
- `.safe-area-top` - 顶部安全区域适配
- `.safe-area-bottom` - 底部安全区域适配
- `.safe-top` - 通用顶部安全间距
- `.fixed-top` - 固定顶部元素适配

### 2. 统一组件解决方案

#### `PageWrapper.vue` - 统一页面包装器（推荐）
**所有使用自定义导航栏的页面都应该使用这个组件**，它会自动处理平台差异：

```vue
<template>
  <!-- 带导航栏的页面 -->
  <PageWrapper title="页面标题" :show-back="true">
    <view>页面内容</view>
  </PageWrapper>

  <!-- 不带导航栏的页面（如首页） -->
  <PageWrapper :show-nav-bar="false">
    <view>页面内容</view>
  </PageWrapper>
</template>
```

**特性：**
- ✅ 自动检测平台（H5/小程序/APP）
- ✅ H5环境自动禁用安全区域适配
- ✅ 小程序和APP环境自动适配
- ✅ 统一的导航栏样式和交互
- ✅ 支持自定义导航栏内容

#### `SafeAreaHeader.vue` - 简化头部适配（特殊场景）
仅在特殊场景下使用，一般推荐使用 `PageWrapper`：

```vue
<template>
  <SafeAreaHeader>
    <view class="header">
      <!-- 头部内容 -->
    </view>
  </SafeAreaHeader>
</template>
```

### 3. 使用方式

#### 方式一：使用PageWrapper（强烈推荐）

**所有自定义导航栏页面的标准用法：**

```vue
<template>
  <PageWrapper title="页面标题" :show-back="true">
    <view class="content">
      <!-- 页面内容 -->
    </view>
  </PageWrapper>
</template>

<script setup>
import PageWrapper from '@/components/PageWrapper.vue';
</script>
```

**首页等不需要导航栏的页面：**

```vue
<template>
  <PageWrapper :show-nav-bar="false">
    <view class="content">
      <!-- 页面内容，会自动添加安全区域适配 -->
    </view>
  </PageWrapper>
</template>
```

#### 方式二：使用CSS类

```vue
<template>
  <view class="page">
    <view class="header safe-area-top">
      <text class="title">页面标题</text>
    </view>
    
    <view class="content">
      <!-- 页面内容 -->
    </view>
  </view>
</template>

<style>
.header {
  background: white;
  padding: 16px;
}
</style>
```

#### 方式三：使用工具函数

```vue
<template>
  <view class="page">
    <view class="header" :style="headerStyle">
      <text class="title">页面标题</text>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { getSafeAreaTop } from '@/utils/safe-area';

const headerStyle = ref({});

onMounted(async () => {
  const safeAreaTop = await getSafeAreaTop();
  headerStyle.value = {
    paddingTop: safeAreaTop + 'px'
  };
});
</script>
```

## 📱 平台适配

### H5环境
- **完全禁用安全区域适配** - 自动检测并跳过所有安全区域处理
- 不添加任何额外的padding或margin
- 保持原生H5的正常显示效果

### 微信小程序
- 自动获取胶囊按钮位置信息
- 支持CSS安全区域变量
- 适配不同设备的状态栏高度
- 精确计算胶囊按钮避让区域

### 支付宝小程序
- 使用固定的状态栏高度（20px）
- 导航栏高度为44px
- 适配支付宝特有的UI规范

### 其他小程序平台
- 百度小程序、字节跳动小程序等
- 使用通用的适配方案
- 状态栏高度20px + 导航栏44px

### APP环境（Android & iOS）
- **iOS和Android使用相同的处理方式**
- 使用系统提供的安全区域API
- 支持刘海屏、水滴屏等异形屏
- 自动适配横屏、竖屏切换

## 🎨 样式变量

```scss
:root {
  --status-bar-height: 20px;      // 状态栏高度
  --safe-area-top: 64px;          // 顶部安全区域
  --safe-area-bottom: 0px;        // 底部安全区域
  --nav-bar-height: 44px;         // 导航栏高度
}
```

## 📋 页面配置

对于使用自定义导航栏的页面，需要在 `pages.json` 中配置：

```json
{
  "path": "pages/example/index",
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "页面标题"
  }
}
```

## 🔧 最佳实践

### 1. 统一使用组件
推荐在所有自定义导航栏页面中使用 `SafeAreaHeader` 或 `SafeAreaContainer` 组件。

### 2. 避免硬编码高度
不要在代码中硬编码状态栏或安全区域高度，使用提供的工具函数动态获取。

### 3. 测试多种设备
在不同设备上测试适配效果：
- iPhone X/11/12/13/14系列（刘海屏）
- iPhone 8及以下（传统屏幕）
- 各种Android设备
- 不同小程序平台

### 4. 响应式设计
考虑横屏、竖屏切换时的适配效果。

## 🐛 常见问题

### Q: 为什么内容还是被遮挡？
A: 检查是否正确引入了安全区域样式，确保组件或CSS类正确应用。

### Q: 不同设备上高度不一致？
A: 这是正常现象，不同设备的状态栏和安全区域高度确实不同，工具会自动适配。

### Q: H5环境下出现多余的顶部间距？
A: H5环境已经做了特殊处理，如果还有问题，检查是否重复应用了安全区域样式。

### Q: H5环境下TabBar图标消失或字体变小？
A: 已提供专门的H5修复样式文件 `src/styles/h5-fixes.scss`，确保H5环境下所有组件正常显示。

### Q: 胶囊按钮位置获取失败？
A: 在某些情况下可能获取失败，代码中已提供了后备方案，使用默认值。

## 📝 更新日志

- v1.0.0: 初始版本，支持基础安全区域适配
- v1.1.0: 新增组件化解决方案
- v1.2.0: 完善多平台适配
- v1.3.0: 新增响应式支持和横屏适配

## 🔗 相关文件

- `src/utils/safe-area.ts` - 安全区域工具函数
- `src/components/PageWrapper.vue` - 统一页面包装器组件（包含安全区域样式）
- `src/components/YiTabBar.vue` - TabBar组件（包含TabBar相关样式）
- `src/App.vue` - 全局样式

通过这套完整的解决方案，可以彻底解决小程序中页面内容与系统UI重叠的问题，确保在各种设备和平台上都有良好的显示效果。
