# 易经游戏系统完整指南

## 📋 系统概述

本文档是易经小程序游戏系统的完整指南，涵盖了架构设计、开发规范、积分体系、等级系统、优化实施等所有核心内容。

## 🏗️ 系统架构

### 设计原则
1. **高内聚，低耦合** - 每个游戏独立开发，通过标准接口交互
2. **模块化开发** - 游戏逻辑与UI分离，配置与代码分离
3. **标准化接口** - 统一的游戏生命周期和数据格式
4. **事件驱动** - 各系统通过事件进行松耦合通信

### 目录结构
```
src/games/
├── shared/                          # 🔄 游戏间共享内容
│   ├── components/                  # 通用游戏组件
│   │   ├── GameTimer.vue           # 游戏计时器
│   │   ├── GameResult.vue          # 游戏结果弹窗
│   │   ├── GameScore.vue           # 游戏分数显示
│   │   └── GameCard.vue            # 游戏卡片
│   └── services/                    # ✅ 优化后的公共服务架构
│       ├── user/                   # 用户管理服务
│       │   ├── user-events.ts      # ✅ 统一事件系统
│       │   ├── user-profile.ts     # ✅ 用户档案管理
│       │   └── user-storage.ts     # ✅ 数据一致性管理
│       ├── scoring/                # 积分系统
│       │   └── score-calculator.ts # ✅ 积分计算引擎
│       ├── ranking/                # 排名系统
│       │   └── ranking-engine.ts   # ✅ 排名计算引擎
│       ├── achievement/            # ✅ 成就系统（已完成）
│       │   ├── achievement-engine.ts # ✅ 成就判定引擎
│       │   └── achievement-examples.ts # ✅ 成就系统使用示例
│       ├── level/                  # ✅ 等级系统（已完成）
│       │   ├── level-calculator.ts # ✅ 等级计算器
│       │   ├── level-rewards.ts    # ✅ 等级奖励系统
│       │   ├── level-titles.ts     # ✅ 统一称号配置
│       │   └── experience-tracker.ts # ✅ 经验值追踪器
│       ├── analytics/              # 数据分析
│       │   └── game-analytics.ts   # ⏳ 游戏数据分析
│       └── game-service.ts         # ✅ 统一游戏服务接口
│
├── {game-name}/                     # 🎯 具体游戏模块
│   ├── index.vue                   # 游戏主页面
│   ├── config.ts                   # 游戏配置
│   ├── components/                 # 游戏专用组件
│   └── logic/                      # 游戏逻辑
│       ├── game-logic.ts           # 主要游戏逻辑
│       ├── question-generator.ts   # 题目生成器
│       └── types.ts                # 类型定义
│
└── registry.ts                     # 🎮 游戏注册中心
```

## 🎮 核心服务系统

### ✅ 统一游戏服务 (`game-service.ts`)
**一站式游戏服务接口，自动处理所有游戏相关功能**

```typescript
import { gameService } from '@/games/shared/services/game-service';

// 初始化用户（应用启动时调用）
await gameService.initializeUser();

// 开始游戏
await gameService.startGame('game-id', 'medium');

// 结束游戏（自动处理积分、排名、成就、等级等）
const result = await gameService.endGame(gameResult);
```

### ✅ 事件驱动架构 (`user-events.ts`)
**统一事件系统，实现系统间松耦合通信**

```typescript
import { eventBus } from '@/games/shared/services/user/user-events';

// 监听游戏事件
eventBus.on('GAME_END', async (event) => {
  console.log('游戏结束:', event.data.score);
});

eventBus.on('LEVEL_UP', async (event) => {
  console.log('等级提升:', event.data.newLevel);
});

eventBus.on('ACHIEVEMENT_UNLOCK', async (event) => {
  console.log('成就解锁:', event.data.achievementName);
});
```

### ✅ 积分计算引擎 (`score-calculator.ts`)
**智能积分计算，支持多维度奖励**

#### 积分计算公式
```
最终积分 = (基础分数 + 奖励分数) × 难度倍数
```

#### 积分组成
- **基础积分**：游戏基本得分
- **时间奖励**：基于效率的时间奖励
- **准确率奖励**：基于准确率的奖励
- **连击奖励**：连续正确的奖励
- **难度倍数**：基于游戏难度的倍数
- **技能奖励**：基于历史表现的奖励
- **活跃奖励**：基于用户活跃度的奖励

### ✅ 排名系统 (`ranking-engine.ts`)
**多维度实时排名系统**

- **全球排名**：基于总积分的全球排名
- **游戏专项排名**：每个游戏的独立排名
- **时间段排名**：日/周/月排名
- **实时更新**：排名变化实时通知
- **缓存优化**：高性能排名计算

### ✅ 数据一致性保证 (`user-storage.ts`)
**事务性数据操作，确保数据完整性**

- **原子性操作**：多系统数据同时更新
- **自动回滚**：操作失败时自动恢复
- **并发安全**：防止数据竞争和冲突
- **事件发布**：操作完成后自动发布事件

## 🎯 等级系统

### 等级体系

#### **总体等级**
- **计算方式**：基于总经验值的主等级
- **等级范围**：1-100级
- **经验增长**：非线性增长（基础1000，增长率1.2）
- **称号系统**：每10级一个新称号

#### **等级称号表**
| 等级 | 称号 | 经验范围 | 颜色 | 图标 |
|------|------|----------|------|------|
| 1-10 | 初学者 | 0-999 | 灰色 | 🌱 |
| 11-20 | 入门弟子 | 1,000-2,499 | 绿色 | 📚 |
| 21-30 | 勤学者 | 2,500-4,999 | 深绿 | 🎓 |
| 31-40 | 小有所成 | 5,000-9,999 | 蓝色 | ⭐ |
| 41-50 | 颇有心得 | 10,000-19,999 | 深蓝 | 🔮 |
| 51-60 | 融会贯通 | 20,000-39,999 | 紫色 | 💎 |
| 61-70 | 登堂入室 | 40,000-79,999 | 粉色 | 🏆 |
| 71-80 | 炉火纯青 | 80,000-159,999 | 橙红 | 🔥 |
| 81-90 | 大师 | 160,000-319,999 | 橙色 | 👑 |
| 91-99 | 宗师 | 320,000-639,999 | 黄色 | 🌟 |
| 100 | 易学泰斗 | 640,000+ | 金色 | 🌞 |

### 经验值系统

#### **经验来源**
```typescript
// 基础经验
游戏完成: 50经验
分数经验: 分数 × 0.1

// 奖励经验
准确率奖励: 30经验 (≥90%准确率)
速度奖励: 20经验 (≤30秒完成)
连击奖励: 连击数 × 2
每日首次: 100经验
成就解锁: 50经验
```

#### **每日限制**
- **最大经验**：1000经验/天
- **防刷机制**：超出限制的经验不计入
- **重置时间**：每日0点重置

### 奖励系统

#### **称号奖励**
```
等级5: 易学新手
等级10: 勤学者
等级20: 颇有心得
等级30: 登堂入室
等级50: 易学大师
等级70: 易学宗师
等级100: 易学泰斗
```

#### **徽章奖励**
```
等级10: 青铜徽章
等级25: 白银徽章
等级50: 黄金徽章
等级75: 铂金徽章
等级100: 钻石徽章
```

#### **功能奖励**
```
等级5: 自定义头像
等级10: 游戏统计详情
等级15: 成就进度追踪
等级20: 高级排行榜
等级30: 专属游戏模式
等级50: 大师挑战赛
```

#### **积分倍数奖励**
```
等级20: 1.1倍
等级40: 1.2倍
等级60: 1.3倍
等级80: 1.5倍
等级100: 2.0倍
```

## 🎖️ 成就系统

### ✅ 已实现功能

#### 1. 成就判定引擎 (`achievement-engine.ts`)
- ✅ **多类型成就**：积分、游戏次数、准确率、速度、连击、特殊成就
- ✅ **智能判定**：单次、累计、连续、复杂条件判定
- ✅ **稀有度系统**：普通、稀有、史诗、传说四个等级
- ✅ **奖励机制**：经验值、称号、徽章、倍数奖励
- ✅ **进度追踪**：实时更新成就进度
- ✅ **每日限制**：防止刷成就，保持游戏平衡

#### 2. 预定义成就列表
```typescript
// 积分成就
'score_100': 百分达人 - 单局获得100分
'score_500': 五百强者 - 单局获得500分
'score_1000': 千分大师 - 单局获得1000分
'total_score_10000': 万分传说 - 累计获得10000分

// 游戏次数成就
'first_game': 初次体验 - 完成第一个游戏
'games_10': 勤学者 - 完成10局游戏
'games_100': 游戏达人 - 完成100局游戏

// 准确率成就
'accuracy_90': 精准射手 - 单局准确率达到90%
'accuracy_100': 完美主义 - 单局准确率达到100%

// 速度成就
'speed_30': 闪电侠 - 30秒内完成游戏
'speed_15': 光速传说 - 15秒内完成游戏

// 连击成就
'combo_5': 连击新手 - 达成5连击
'combo_10': 连击大师 - 达成10连击

// 特殊成就
'daily_streak_7': 七日坚持 - 连续7天游戏
'all_games_played': 全能玩家 - 尝试所有游戏类型
```

### 成就类型详解

#### **积分成就**
- **单局积分**：基于单次游戏分数
- **累计积分**：基于总积分里程碑
- **奖励**：5-100积分不等

#### **游戏次数成就**
- **参与度奖励**：鼓励持续游戏
- **里程碑设置**：1, 10, 100局等
- **奖励**：5-50积分不等

#### **准确率成就**
- **技能水平**：体现游戏技巧
- **阈值设置**：90%, 100%准确率
- **奖励**：30-75积分不等

#### **速度成就**
- **反应速度**：快速完成游戏
- **时间限制**：30秒、15秒等
- **奖励**：40-80积分不等

#### **连击成就**
- **连续表现**：连续正确答题
- **连击数量**：5连击、10连击等
- **奖励**：20-45积分不等

#### **特殊成就**
- **复杂条件**：多维度综合判定
- **独特体验**：连续登录、全游戏体验等
- **奖励**：60-100积分不等

### 成就稀有度系统

#### **普通 (Common)** 🌱
- **获得难度**：容易获得
- **积分奖励**：5-20积分
- **图标颜色**：灰色/绿色
- **示例**：初次体验、百分达人

#### **稀有 (Rare)** ⭐
- **获得难度**：需要一定努力
- **积分奖励**：25-50积分
- **图标颜色**：蓝色
- **示例**：精准射手、闪电侠

#### **史诗 (Epic)** 💎
- **获得难度**：较难获得
- **积分奖励**：50-80积分
- **图标颜色**：紫色
- **示例**：完美主义、光速传说

#### **传说 (Legendary)** 👑
- **获得难度**：极难获得
- **积分奖励**：100积分
- **图标颜色**：金色
- **示例**：万分传说

## 📈 排行榜系统

### 排行榜类型
1. **单游戏排行榜**：每个游戏独立排名
2. **难度排行榜**：按难度分别排名
3. **全网积分榜**：总积分排名
4. **时间段排行榜**：日/周/月排名

### 排行榜特性
- 本地存储，支持1000名玩家
- 实时更新排名
- 支持查看用户周围排名
- 记录最高分和游戏时间

## 🚀 游戏开发流程

### 新游戏开发步骤

#### 步骤1: 创建游戏目录结构
```
src/games/{game-name}/
├── index.vue              # 游戏页面
├── config.ts              # 游戏配置
├── components/            # 游戏专用组件
└── logic/                 # 游戏逻辑
    ├── game-logic.ts      # 主要游戏逻辑
    ├── question-generator.ts # 题目生成器
    └── types.ts           # 类型定义
```

#### 步骤2: 配置游戏信息 (`config.ts`)
```typescript
import type { GameInfo } from '@/types/game';

export const GAME_CONFIG: GameInfo = {
  id: 'game-name',
  name: '游戏名称',
  description: '游戏描述',
  icon: '🎮',
  difficulty: 'medium',
  category: 'puzzle',
  estimatedTime: 120,
  maxScore: 1000,
  rules: ['规则1', '规则2'],
  features: ['特性1', '特性2'],
};
```

#### 步骤3: 实现游戏逻辑 (`logic/game-logic.ts`)
```typescript
import { gameService } from '@/games/shared/services/game-service';
import { GAME_CONFIG } from '../config';

export class GameLogic {
  private state: GameState;

  constructor() {
    this.state = this.createInitialState();
  }

  async startGame(): Promise<GameState> {
    // ✅ 使用统一服务通知游戏开始
    await gameService.startGame(GAME_CONFIG.id!, GAME_CONFIG.difficulty!);
    // 初始化游戏
  }

  answerQuestion(optionId: string): AnswerResult {
    // 处理答题
  }

  async endGame(): Promise<GameResult> {
    // 结束游戏并使用统一服务处理结果
    const result = this.calculateResult();
    // ✅ 使用统一服务自动处理积分、排名、成就等
    await gameService.endGame(result);
    return result;
  }
}
```

#### 步骤4: 创建游戏页面 (`index.vue`)
```vue
<template>
  <PageWrapper :title="GAME_CONFIG.name">
    <!-- 游戏UI -->
  </PageWrapper>
</template>

<script setup lang="ts">
import { gameService } from '@/games/shared/services/game-service';
import { GAME_CONFIG } from './config';
import { GameLogic } from './logic/game-logic';

// 游戏逻辑实现
</script>
```

#### 步骤5: 注册游戏 (`games/registry.ts`)
```typescript
import { GAME_CONFIG } from './game-name/config';

export const GAMES: GameInfo[] = [
  // 其他游戏...
  GAME_CONFIG,
];
```

#### 步骤6: 添加路由 (`pages.json`)
```json
{
  "path": "games/game-name/index",
  "style": {
    "navigationBarTitleText": "游戏名称"
  }
}
```

## 💻 使用示例

### 基础游戏服务使用

```typescript
import { gameService } from '@/games/shared/services/game-service';

// 初始化用户
const userProfile = await gameService.initializeUser();

// 获取用户统计
const userStats = gameService.getUserStats();
console.log(`等级: ${userStats.level}, 总分: ${userStats.totalScore}`);

// 获取等级信息
const levelInfo = gameService.getUserLevel();
console.log(`${levelInfo.level}级 - ${levelInfo.title}`);

// 获取等级进度
const progress = gameService.getLevelProgress();
const expNeeded = gameService.getExperienceToNextLevel();
console.log(`进度: ${progress}%, 还需经验: ${expNeeded}`);
```

### 奖励管理

```typescript
// 获取用户奖励
const rewards = gameService.getUserRewards();
console.log(`称号数量: ${rewards.titles.length}`);
console.log(`徽章数量: ${rewards.badges.length}`);

// 激活称号/徽章
await gameService.activateTitle('title_10_勤学者');
await gameService.activateBadge('badge_25_白银徽章');
```

### 排行榜查询

```typescript
// 获取全球排行榜
const globalRanking = await gameService.getRanking('global', undefined, 10);

// 获取游戏专项排行榜
const gameRanking = await gameService.getRanking('game', 'hexagram-match', 10);

// 获取用户排名
const userRank = await gameService.getUserRank('user123', 'global');
```

### 成就系统使用

```typescript
import { gameService } from '@/games/shared/services/game-service';

// 检查游戏成就
const gameResult = {
  gameId: 'hexagram-match',
  score: 500,
  accuracy: 95,
  duration: 25,
  // ... 其他游戏数据
};

const newAchievements = await gameService.checkAchievements(gameResult);
console.log('新解锁成就:', newAchievements);

// 获取用户成就
const userAchievements = gameService.getUserAchievements();
console.log('用户成就列表:', userAchievements);

// 获取所有可用成就
const allAchievements = gameService.getAllAchievements();
console.log('所有成就:', allAchievements);

// 获取成就统计
const stats = gameService.getAchievementStats();
console.log(`完成度: ${stats.completed}/${stats.total}`);
console.log(`总积分: ${stats.totalPoints}`);
```

### 事件监听

```typescript
import { eventBus } from '@/games/shared/services/user/user-events';

// 监听等级提升
eventBus.on('LEVEL_UP', async (event) => {
  console.log(`恭喜升级到 ${event.data.newLevel} 级！`);
  console.log(`获得奖励:`, event.data.rewards);
});

// 监听成就解锁
eventBus.on('ACHIEVEMENT_UNLOCK', async (event) => {
  console.log(`🎉 解锁成就: ${event.data.achievementName}`);
  console.log(`成就ID: ${event.data.achievementId}`);
  console.log(`获得积分: ${event.data.points || 0}`);

  // 显示成就解锁动画
  showAchievementUnlockAnimation(event.data);
});

// 监听经验获得
eventBus.on('EXPERIENCE_GAINED', async (event) => {
  console.log(`获得 ${event.data.experienceGained} 经验`);
});
```

## 🔧 开发规范

### 代码规范
1. **TypeScript优先** - 所有新代码使用TypeScript
2. **组件化开发** - UI组件与逻辑分离
3. **统一命名** - 使用kebab-case命名文件和组件
4. **注释完整** - 关键逻辑必须有注释说明

### 游戏规范
1. **配置驱动** - 游戏参数通过config.ts配置
2. **状态管理** - 游戏状态统一管理，避免全局变量
3. **错误处理** - 完善的错误捕获和用户提示
4. **性能优化** - 避免内存泄漏，及时清理资源

### 测试规范
1. **功能测试** - 每个游戏必须经过完整测试
2. **兼容性测试** - 确保在不同平台正常运行
3. **性能测试** - 验证游戏流畅度和响应速度
4. **数据测试** - 验证积分计算和数据存储正确性

## 📊 数据结构

### 游戏结果 (GameResult)
```typescript
interface GameResult {
  gameId: string;           // 游戏ID
  score: number;            // 游戏分数
  baseScore: number;        // 基础分数
  bonusScore: number;       // 奖励分数
  finalScore: number;       // 最终分数
  duration: number;         // 游戏时长
  accuracy: number;         // 准确率
  difficulty: string;       // 难度
  correctAnswers: number;   // 正确答案数
  totalQuestions: number;   // 总题目数
  maxCombo: number;         // 最大连击
  timeBonus: number;        // 时间奖励
  accuracyBonus: number;    // 准确率奖励
  comboBonus: number;       // 连击奖励
  difficultyMultiplier: number; // 难度倍数
  timestamp: number;        // 时间戳
  isNewRecord?: boolean;    // 是否新纪录
  details?: Record<string, any>; // 游戏特定详情
}
```

### 用户档案 (UserProfile)
```typescript
interface UserProfile {
  userId: string;           // 用户ID
  username: string;         // 用户名
  avatar?: string;          // 头像
  level: number;            // 等级
  experience: number;       // 经验值
  totalScore: number;       // 总积分
  gamesPlayed: Record<string, number>; // 游戏次数
  bestScores: Record<string, number>;  // 最佳分数
  achievements: string[];   // 成就列表
  createdAt: number;        // 创建时间
  lastActiveAt: number;     // 最后活跃时间
  preferences: UserPreferences; // 用户偏好
  statistics: UserStatistics;   // 用户统计
  levelInfo: UserLevel;     // 等级信息
  gameSpecificLevels: Record<string, GameSpecificLevel>; // 游戏专项等级
  skillLevels: Record<string, SkillLevel>; // 技能等级
  rewards: UserRewards;     // 用户奖励
}
```

## 🎯 系统优化

### 已完成优化

#### ✅ 高优先级优化
1. **统一事件系统** - 事件驱动架构，松耦合通信
2. **数据一致性保证** - 事务性操作和自动回滚
3. **基础性能优化** - 缓存策略和批量处理
4. **多维度排名系统** - 实时排名更新和缓存机制
5. **等级奖励机制** - 完整的等级体系和奖励系统

#### 🔄 中优先级优化
1. **丰富的成就类型** - 成就事件框架已就绪
2. **多维度排名** - 部分完成，待扩展地区和好友排名

#### ⏸️ 低优先级优化（长期规划）
1. **社交功能** - 好友系统、公会系统
2. **个性化系统** - 个人资料定制、推荐系统
3. **高级分析功能** - 用户行为分析、游戏平衡性分析

### 技术改进效果

#### 开发效率提升
- **统一接口**：游戏开发者只需调用 `gameService`
- **自动处理**：积分、排名、成就等自动计算和更新
- **类型安全**：完整的 TypeScript 类型支持
- **错误处理**：统一的错误处理和恢复机制

#### 系统可靠性提升
- **数据一致性**：事务性操作保证数据完整性
- **错误隔离**：单个组件失败不影响整个系统
- **自动恢复**：失败操作自动回滚
- **监控能力**：完整的事件日志和错误追踪

#### 性能优化效果
- **响应速度**：缓存和异步处理提高响应速度
- **资源利用**：批量操作减少资源消耗
- **扩展性**：支持大量用户和游戏数据
- **稳定性**：资源锁和事务保证系统稳定

## 🚀 未来扩展

### 短期扩展（1-2周）
- **完善成就系统**：实现具体的成就规则和检查逻辑
- **数据迁移**：将现有数据迁移到新的服务架构
- **等级系统测试**：测试等级计算和奖励发放功能

### 中期扩展（1个月）
- **多维度排名**：实现地区排名和好友排名
- **高级成就**：实现技能成就、探索成就等
- **性能监控**：添加性能指标和监控

### 长期扩展（3个月）
- **社交功能**：好友系统和社交分享
- **个性化系统**：个人资料定制和推荐
- **数据分析**：用户行为和游戏平衡性分析

### 全网积分榜单准备
- 用户ID系统已就绪
- 数据结构支持网络同步
- 排行榜系统可扩展到服务器端
- 成就系统支持全网统计

## 📝 注意事项

### 开发注意事项
1. **数据一致性**：所有积分计算都通过统一引擎
2. **性能优化**：排行榜限制1000条记录
3. **错误处理**：完善的异常捕获和恢复机制
4. **数据备份**：支持用户数据导出/导入
5. **扩展性**：模块化设计，易于添加新游戏和规则

### 维护注意事项
1. **定期清理**：清理过期的经验记录和排名数据
2. **版本兼容**：确保数据结构向后兼容
3. **监控告警**：关键指标异常时及时告警
4. **备份策略**：定期备份用户数据和配置

## 🎉 总结

这个游戏系统为易经小程序提供了完整的游戏框架，包括：

- **🏗️ 模块化架构**：高内聚、低耦合的设计
- **🎯 统一服务**：一站式游戏服务接口
- **📊 完整数据**：积分、等级、成就、排名全覆盖
- **🔧 事件驱动**：松耦合的系统间通信
- **🚀 高性能**：缓存、批量处理、异步操作
- **📈 可扩展**：支持新游戏快速接入

通过这个系统，开发者可以专注于游戏玩法的创新，而无需关心底层的积分计算、等级管理、数据存储等复杂逻辑。系统既保证了功能的完整性，又提供了良好的开发体验和用户体验。
