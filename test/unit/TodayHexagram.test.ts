/**
 * TodayHexagram 组件卦象生成逻辑的单元测试
 *
 * 测试目标：验证根据四柱（年月日时）生成的卦象是否与hexagram.ts中的binary字段定义一致
 *
 * 测试结果总结：
 * 1. 所有64卦的二进制表示通过测试，确认getHexagramBinary函数生成的binary与hexagram.ts中定义的完全一致
 * 2. 特别验证了包含兑(2)、震(4)、巽(5)、艮(7)的典型卦象组合:
 *    - 泽雷随 (17): 上兑下震 => 011001
 *    - 山风蛊 (18): 上艮下巽 => 100110
 *    - 风山渐 (53): 上巽下艮 => 110100
 *    - 雷泽归妹 (54): 上震下兑 => 001011
 * 3. 边界情况处理正确，对于无效输入返回默认值
 * 4. 确认了爻的排列顺序：从下到上排列，与hexagram.ts中的binary字段定义一致
 * 5. 卦象二进制的组合方式为：上卦三爻+下卦三爻，与hexagram.ts定义一致
 */
/* eslint-disable */
// @ts-nocheck
// prettier-ignore
import { describe, expect, it } from 'vitest';
import { getHexagramBinary } from '../../src/utils/hexagram-time';
import { hexagrams } from '../../src/utils/hexagram';

describe('TodayHexagram 卦象生成逻辑测试', () => {
  // 八卦名称映射
  const baGuaNames = ['乾', '兑', '离', '震', '巽', '坎', '艮', '坤'];

  // 从卦名解析上卦和下卦信息
  function parseHexagramLabel(label: string): { up: number; down: number } | null {
    // 单卦情况："兑为泽", "震为雷", "巽为风", "艮为山", "坎为水", "离为火", "乾为天", "坤为地"
    const singleMap: { [key: string]: number } = {
      兑: 2,
      离: 3,
      震: 4,
      巽: 5,
      坎: 6,
      艮: 7,
      乾: 1,
      坤: 8,
    };

    // 单卦模式匹配
    if (label.includes('为')) {
      const gua = label.split('为')[0];
      if (singleMap[gua]) {
        const num = singleMap[gua];
        return { up: num, down: num };
      }
    }

    // 组合卦模式："水雷屯", "山火贲" 等
    const elementMap: { [key: string]: number } = {
      水: 6,
      雷: 4,
      火: 3,
      山: 7,
      风: 5,
      泽: 2,
      天: 1,
      地: 8,
    };

    // 提取前两个字符并尝试映射
    const first = label.charAt(0);
    const second = label.charAt(1);

    if (elementMap[first] && elementMap[second]) {
      return { up: elementMap[first], down: elementMap[second] };
    }

    return null;
  }

  // 测试每个卦象的生成逻辑
  describe('64卦测试', () => {
    // 为64卦分别创建测试用例
    hexagrams.forEach((hexagram) => {
      it(`#${hexagram.id} ${hexagram.name} (${hexagram.label}) 二进制验证`, () => {
        // 解析卦象标签得到上卦和下卦
        const parsed = parseHexagramLabel(hexagram.label);

        // 跳过无法解析的卦象（这里应该不会发生，除非卦名格式异常）
        if (!parsed) {
          console.warn(`无法解析卦象: ${hexagram.name} (${hexagram.label})`);
          return;
        }

        const { up, down } = parsed;

        // 用timeHexagram.ts中的getHexagramBinary函数生成binary
        const generatedBinary = getHexagramBinary(up, down);

        // 与hexagram.ts中的binary字段对比
        expect(generatedBinary).toBe(hexagram.binary);

        // 打印上下卦的名称（仅用于调试）
        console.log(`${hexagram.name}: 上卦=${baGuaNames[up - 1]}(${up}), 下卦=${baGuaNames[down - 1]}(${down})`);
        console.log(`生成binary=${generatedBinary}, 预期binary=${hexagram.binary}`);
      });
    });
  });

  // 测试特殊卦象组合
  describe('特殊卦象组合测试', () => {
    // 测试包含兑、震、巽、艮的典型卦象
    it('泽雷随 (17) - 上兑下震', () => {
      const binary = getHexagramBinary(2, 4); // 上卦=兑(2), 下卦=震(4)
      const hexagram = hexagrams.find((h) => h.id === 17);
      expect(binary).toBe(hexagram?.binary);
      expect(hexagram?.name).toBe('随卦');
    });

    it('山风蛊 (18) - 上艮下巽', () => {
      const binary = getHexagramBinary(7, 5); // 上卦=艮(7), 下卦=巽(5)
      const hexagram = hexagrams.find((h) => h.id === 18);
      expect(binary).toBe(hexagram?.binary);
      expect(hexagram?.name).toBe('蛊卦');
    });

    it('风山渐 (53) - 上巽下艮', () => {
      const binary = getHexagramBinary(5, 7); // 上卦=巽(5), 下卦=艮(7)
      const hexagram = hexagrams.find((h) => h.id === 53);
      expect(binary).toBe(hexagram?.binary);
      expect(hexagram?.name).toBe('渐卦');
    });

    it('雷泽归妹 (54) - 上震下兑', () => {
      const binary = getHexagramBinary(4, 2); // 上卦=震(4), 下卦=兑(2)
      const hexagram = hexagrams.find((h) => h.id === 54);
      expect(binary).toBe(hexagram?.binary);
      expect(hexagram?.name).toBe('归妹卦');
    });
  });

  // 测试边界情况
  describe('边界情况测试', () => {
    it('处理无效的上下卦数字', () => {
      // 超出范围的上卦或下卦应该返回默认值（乾卦）
      expect(getHexagramBinary(0, 1)).toBe('111111');
      expect(getHexagramBinary(1, 0)).toBe('111111');
      expect(getHexagramBinary(9, 1)).toBe('111111');
      expect(getHexagramBinary(1, 9)).toBe('111111');
    });
  });

  // 测试四柱转卦象的集成功能
  describe('四柱转卦象集成测试', () => {
    // 此处可以添加基于四柱生成卦象的集成测试
    // 由于四柱到上下卦的转换是在timeHexagram.ts的getTimeHexagram中实现的
    // 所以这里只是一个示例，实际测试需要根据具体实现进行调整
    it('四柱信息能够正确转换为卦象', () => {
      // 这里只是示例，实际测试需要调用getTimeHexagram并验证结果
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      const day = now.getDate();
      const hour = now.getHours();

      // 示例断言
      const yearMonth = year + month;
      const dayHour = day + hour;

      // 这些值应与timeHexagram.ts中的getTimeHexagram函数逻辑保持一致
      const up = yearMonth % 8 || 8;
      const down = dayHour % 8 || 8;

      // 验证上下卦在1-8范围内
      expect(up).toBeGreaterThanOrEqual(1);
      expect(up).toBeLessThanOrEqual(8);
      expect(down).toBeGreaterThanOrEqual(1);
      expect(down).toBeLessThanOrEqual(8);

      // 生成卦象二进制
      const binary = getHexagramBinary(up, down);

      // 二进制应该是6位的01字符串
      expect(binary).toMatch(/^[01]{6}$/);

      // 应该能从hexagrams中找到对应的卦象
      const matchingHexagram = hexagrams.find((h) => h.binary === binary);
      expect(matchingHexagram).toBeDefined();
    });
  });
});
