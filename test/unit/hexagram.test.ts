import { describe, it, expect } from 'vitest';
import { getHexagramById } from '../../src/utils/hexagram';

describe('getHexagramById', () => {
  it('should return the correct hexagram for a valid ID', () => {
    const hexagram = getHexagramById(1);
    expect(hexagram).toBeDefined();
    expect(hexagram.id).toBe(1);
    expect(hexagram.name).toBe('乾卦');
  });

  it('should return a default hexagram for an invalid ID', () => {
    const hexagram = getHexagramById(99);
    expect(hexagram).toBeDefined();
    expect(hexagram.id).toBe(1);
    expect(hexagram.name).toBe('乾卦');
  });
});
