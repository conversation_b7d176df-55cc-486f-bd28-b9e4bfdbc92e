/**
 * 等级计算修复测试
 */

import { describe, it, expect } from 'vitest';
import { LevelCalculator } from '@/games/shared/services/level/level-calculator';

describe('等级计算修复测试', () => {
  const levelCalculator = new LevelCalculator();

  it('应该正确计算等级1的经验值范围', () => {
    const level1Info = levelCalculator.getLevelInfo(1);

    console.log('等级1信息:', level1Info);

    // 等级1应该从0开始
    expect(level1Info.minExperience).toBe(0);

    // 等级1的最大经验值应该是升到等级2所需的经验（1000）
    expect(level1Info.maxExperience).toBe(1000);

    // 确保最大经验值大于最小经验值
    expect(level1Info.maxExperience).toBeGreaterThan(level1Info.minExperience);
  });

  it('应该正确计算等级2的经验值范围', () => {
    const level2Info = levelCalculator.getLevelInfo(2);

    console.log('等级2信息:', level2Info);

    // 等级2应该从1000开始（等级1的最大经验值）
    expect(level2Info.minExperience).toBe(1000);

    // 等级2的最大经验值应该是1000 + 1200 = 2200
    expect(level2Info.maxExperience).toBe(2200);

    // 确保最大经验值大于最小经验值
    expect(level2Info.maxExperience).toBeGreaterThan(level2Info.minExperience);
  });

  it('应该正确计算升级所需经验值', () => {
    // 测试等级1用户，当前经验73
    const experienceToNext = levelCalculator.getExperienceToNextLevel(73);

    console.log('当前经验73，升级所需:', experienceToNext);

    // 升级所需应该是 1000 - 73 = 927
    expect(experienceToNext).toBe(927);
    expect(experienceToNext).toBeGreaterThan(0);
  });

  it('应该正确计算等级进度', () => {
    // 测试等级1用户，当前经验73
    const progress = levelCalculator.getLevelProgress(73);

    console.log('当前经验73，等级进度:', progress);

    // 进度应该是 73 / 1000 * 100 = 7.3%
    expect(progress).toBeCloseTo(7.3, 1);
    expect(progress).toBeGreaterThan(0);
    expect(progress).toBeLessThan(100);
  });

  it('应该正确处理边界情况', () => {
    // 测试经验值为0的情况
    const level0 = levelCalculator.calculateLevel(0);
    expect(level0.level).toBe(1);
    expect(level0.minExperience).toBe(0);
    expect(level0.maxExperience).toBe(1000);

    // 测试刚好升级的情况
    const level1000 = levelCalculator.calculateLevel(1000);
    expect(level1000.level).toBe(2);
    expect(level1000.minExperience).toBe(1000);
    expect(level1000.maxExperience).toBe(2200);
  });

  it('应该修复负数升级经验的问题', () => {
    // 测试各种经验值，确保升级所需经验都是正数
    const testExperiences = [0, 50, 100, 500, 999, 1000, 1500, 2000];

    testExperiences.forEach((exp) => {
      const experienceToNext = levelCalculator.getExperienceToNextLevel(exp);
      console.log(`经验${exp}，升级所需:`, experienceToNext);

      // 升级所需经验应该总是非负数
      expect(experienceToNext).toBeGreaterThanOrEqual(0);
    });
  });

  it('应该正确计算总积分179的情况', () => {
    // 模拟用户当前状态：总积分179，但经验值可能不同
    // 假设经验值是73（从控制台日志推断）
    const currentExperience = 73;
    const currentLevel = levelCalculator.calculateLevel(currentExperience);
    const experienceToNext = levelCalculator.getExperienceToNextLevel(currentExperience);

    console.log('模拟用户状态:');
    console.log('- 当前经验:', currentExperience);
    console.log('- 当前等级:', currentLevel.level);
    console.log('- 等级范围:', currentLevel.minExperience, '-', currentLevel.maxExperience);
    console.log('- 升级所需:', experienceToNext);

    // 验证计算结果
    expect(currentLevel.level).toBe(1);
    expect(experienceToNext).toBe(927); // 1000 - 73
    expect(experienceToNext).toBeGreaterThan(0);
  });
});
