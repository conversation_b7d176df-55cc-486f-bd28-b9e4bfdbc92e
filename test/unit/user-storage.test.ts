/**
 * user-storage.ts 单元测试
 * 测试游戏数据存储系统的各项功能
 */

import { describe, it, expect, beforeEach, afterEach, vi, type Mock } from 'vitest';
import { GameDataManager, gameDataAccessor } from '@/games/shared/services/user/user-storage';
import { storage, storageManager, StorageErrorType } from '@/utils/storage';
import { userProfileManager } from '@/games/shared/services/user/user-profile';
import { scoreCalculator } from '@/games/shared/services/scoring/score-calculator';
import { experienceTracker } from '@/games/shared/services/level/experience-tracker';
import { rankingEngine } from '@/games/shared/services/ranking/ranking-engine';
import type { GameResult } from '@/types/game';

// Mock 所有依赖
vi.mock('@/utils/storage');
vi.mock('@/games/shared/services/user/user-profile');
vi.mock('@/games/shared/services/scoring/score-calculator');
vi.mock('@/games/shared/services/level/experience-tracker');
vi.mock('@/games/shared/services/ranking/ranking-engine');

const mockStorage = storage as unknown as {
  get: Mock;
  set: Mock;
  getOrNull: Mock;
};

const mockStorageManager = storageManager as unknown as {
  checkQuota: Mock;
  cleanup: Mock;
};

const mockUserProfileManager = userProfileManager as unknown as {
  getCurrentProfile: Mock;
  updateBestScore: Mock;
  updateProfile: Mock;
  incrementGamePlayed: Mock;
  checkGameAchievements: Mock;
  addAchievement: Mock;
};

const mockScoreCalculator = scoreCalculator as unknown as {
  calculateScore: Mock;
};

const mockExperienceTracker = experienceTracker as unknown as {
  calculateGameExperience: Mock;
  addExperience: Mock;
};

const mockRankingEngine = rankingEngine as unknown as {
  updateUserScore: Mock;
};

// 测试工具函数
const createMockGameResult = (overrides: Partial<GameResult> = {}): GameResult => {
  return {
    gameId: 'test-game',
    score: 100,
    baseScore: 80,
    bonusScore: 20,
    finalScore: 100,
    duration: 30000,
    accuracy: 80,
    difficulty: 'medium',
    correctAnswers: 8,
    totalQuestions: 10,
    maxCombo: 3,
    timeBonus: 10,
    accuracyBonus: 5,
    comboBonus: 5,
    difficultyMultiplier: 1.2,
    timestamp: Date.now(),
    ...overrides,
  };
};

const createMockUserProfile = (overrides: any = {}) => {
  return {
    userId: 'test-user',
    username: 'TestUser',
    totalScore: 500,
    experience: 1000,
    level: 5,
    bestScores: { 'test-game': 80 },
    statistics: {
      currentStreak: 3,
      dailyGoalProgress: 50,
    },
    lastActiveAt: Date.now() - 86400000, // 1天前
    ...overrides,
  };
};

describe('GameDataManager', () => {
  let gameDataManager: GameDataManager;
  let mockGameResult: GameResult;
  let mockUserProfile: any;

  beforeEach(() => {
    gameDataManager = GameDataManager.getInstance();

    // 重置所有 mock
    vi.clearAllMocks();

    mockGameResult = createMockGameResult();
    mockUserProfile = createMockUserProfile();

    // 设置默认 mock 返回值
    mockUserProfileManager.getCurrentProfile.mockReturnValue(mockUserProfile);
    mockStorageManager.checkQuota.mockResolvedValue(true);
    mockStorageManager.cleanup.mockResolvedValue(undefined);
    mockStorage.get.mockReturnValue([]);
    mockStorage.set.mockReturnValue(true);

    mockScoreCalculator.calculateScore.mockReturnValue({
      baseScore: 100,
      bonusScore: 20,
      finalScore: 120,
    });

    mockExperienceTracker.calculateGameExperience.mockReturnValue([{ source: 'game_completion', amount: 50 }]);

    mockExperienceTracker.addExperience.mockResolvedValue({
      newTotalExperience: 1050,
      newLevel: 5,
      leveledUp: false,
    });

    mockUserProfileManager.updateBestScore.mockResolvedValue(true);
    mockUserProfileManager.checkGameAchievements.mockResolvedValue(['first_win']);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('processGameResult', () => {
    it('应该成功处理游戏结果', async () => {
      await gameDataManager.processGameResult(mockGameResult);

      // 验证各个步骤都被调用
      expect(mockScoreCalculator.calculateScore).toHaveBeenCalledWith(
        mockGameResult,
        expect.objectContaining({
          gameHistory: mockUserProfile.bestScores,
          currentStreak: 3,
          isFirstGameToday: true,
          consecutiveDays: 3,
          dailyGoalCompleted: false,
        })
      );

      expect(mockExperienceTracker.calculateGameExperience).toHaveBeenCalled();
      expect(mockUserProfileManager.updateBestScore).toHaveBeenCalledWith('test-game', 100);
      expect(mockExperienceTracker.addExperience).toHaveBeenCalled();
      expect(mockUserProfileManager.updateProfile).toHaveBeenCalledWith({
        totalScore: 620, // 500 + 120
        experience: 1050,
        level: 5,
        lastActiveAt: expect.any(Number),
      });
    });

    it('应该在用户档案未初始化时抛出错误', async () => {
      mockUserProfileManager.getCurrentProfile.mockReturnValue(null);

      await expect(gameDataManager.processGameResult(mockGameResult)).rejects.toThrow('用户档案未初始化');
    });

    it('应该处理存储配额不足的情况', async () => {
      mockStorageManager.checkQuota.mockResolvedValue(false);
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      await gameDataManager.processGameResult(mockGameResult);

      expect(consoleSpy).toHaveBeenCalledWith('存储空间不足，跳过分数历史保存');
      consoleSpy.mockRestore();
    });

    it('应该处理分数历史数据损坏的情况', async () => {
      const mockError = {
        type: StorageErrorType.PARSE_ERROR,
        message: 'Parse error',
        key: 'score_history_test-game',
      };

      mockStorage.get.mockImplementation((key, defaultValue, options) => {
        if (options?.onError) {
          options.onError(mockError);
        }
        return defaultValue;
      });

      await gameDataManager.processGameResult(mockGameResult);

      expect(mockStorage.set).toHaveBeenCalledWith('score_history_test-game', []);
    });

    it('应该处理存储空间不足时的自动清理', async () => {
      const mockError = {
        type: StorageErrorType.QUOTA_EXCEEDED,
        message: 'Quota exceeded',
      };

      mockStorage.set.mockImplementation((key, value, options) => {
        if (options?.onError && key.startsWith('score_history_')) {
          options.onError(mockError);
          return false;
        }
        return true;
      });

      await gameDataManager.processGameResult(mockGameResult);

      expect(mockStorageManager.cleanup).toHaveBeenCalledWith({
        clearExpiredCache: true,
        clearOldHistory: true,
      });
    });

    it('应该正确处理成就解锁', async () => {
      mockUserProfileManager.checkGameAchievements.mockResolvedValue(['first_win', 'perfect_score']);

      await gameDataManager.processGameResult(mockGameResult);

      expect(mockUserProfileManager.addAchievement).toHaveBeenCalledWith('first_win');
      expect(mockUserProfileManager.addAchievement).toHaveBeenCalledWith('perfect_score');
    });

    it('应该更新排名信息', async () => {
      await gameDataManager.processGameResult(mockGameResult);

      expect(mockRankingEngine.updateUserScore).toHaveBeenCalledTimes(3);
      expect(mockRankingEngine.updateUserScore).toHaveBeenCalledWith('test-user', 'TestUser', 620, 'global');
      expect(mockRankingEngine.updateUserScore).toHaveBeenCalledWith('test-user', 'TestUser', 100, 'game', 'test-game');
      expect(mockRankingEngine.updateUserScore).toHaveBeenCalledWith('test-user', 'TestUser', 620, 'daily');
    });
  });

  describe('辅助方法测试', () => {
    it('应该正确判断是否为今天第一次游戏', () => {
      const manager = gameDataManager as any;

      // 今天第一次游戏
      const profile1 = { lastActiveAt: Date.now() - 86400000 }; // 1天前
      expect(manager.isFirstGameToday(profile1)).toBe(true);

      // 今天已经玩过
      const profile2 = { lastActiveAt: Date.now() - 3600000 }; // 1小时前
      expect(manager.isFirstGameToday(profile2)).toBe(false);
    });

    it('应该正确获取连续天数', () => {
      const manager = gameDataManager as any;

      const profile1 = { statistics: { currentStreak: 5 } };
      expect(manager.getConsecutiveDays(profile1)).toBe(5);

      const profile2 = { statistics: {} };
      expect(manager.getConsecutiveDays(profile2)).toBe(1);
    });

    it('应该正确判断每日目标是否完成', () => {
      const manager = gameDataManager as any;

      const profile1 = { statistics: { dailyGoalProgress: 100 } };
      expect(manager.isDailyGoalCompleted(profile1)).toBe(true);

      const profile2 = { statistics: { dailyGoalProgress: 50 } };
      expect(manager.isDailyGoalCompleted(profile2)).toBe(false);
    });
  });
});

describe('gameDataAccessor', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getScoreHistory', () => {
    it('应该返回正确的分数历史', () => {
      const mockHistory = [
        { score: 80, breakdown: {}, timestamp: Date.now() - 86400000 },
        { score: 90, breakdown: {}, timestamp: Date.now() - 43200000 },
        { score: 100, breakdown: {}, timestamp: Date.now() },
      ];

      mockStorage.get.mockReturnValue(mockHistory);

      const result = gameDataAccessor.getScoreHistory('test-game');

      expect(result).toEqual(mockHistory.reverse()); // 最新的在前
      expect(mockStorage.get).toHaveBeenCalledWith(
        'score_history_test-game',
        [],
        expect.objectContaining({
          onError: expect.any(Function),
        })
      );
    });

    it('应该处理数据损坏的情况', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      mockStorage.get.mockImplementation((key, defaultValue, options) => {
        if (options?.onError) {
          options.onError({
            type: StorageErrorType.PARSE_ERROR,
            message: 'Parse error',
          });
        }
        return defaultValue;
      });

      const result = gameDataAccessor.getScoreHistory('test-game');

      expect(result).toEqual([]);
      expect(consoleSpy).toHaveBeenCalledWith('游戏 test-game 分数历史数据损坏，返回空数组');
      consoleSpy.mockRestore();
    });
  });

  describe('getGameStats', () => {
    it('应该返回正确的游戏统计', () => {
      const mockHistory = [
        { score: 80, breakdown: {}, timestamp: Date.now() - 86400000 },
        { score: 90, breakdown: {}, timestamp: Date.now() - 43200000 },
        { score: 100, breakdown: {}, timestamp: Date.now() },
      ];

      vi.spyOn(gameDataAccessor, 'getScoreHistory').mockReturnValue(mockHistory);

      const stats = gameDataAccessor.getGameStats('test-game');

      expect(stats).toEqual({
        totalGames: 3,
        bestScore: 100,
        averageScore: 90, // (80+90+100)/3 = 90
        recentAverage: 90,
        lastPlayed: mockHistory[0].timestamp,
      });
    });

    it('应该处理空历史记录', () => {
      vi.spyOn(gameDataAccessor, 'getScoreHistory').mockReturnValue([]);

      const stats = gameDataAccessor.getGameStats('test-game');

      expect(stats).toEqual({
        totalGames: 0,
        bestScore: 0,
        averageScore: 0,
        recentAverage: 0,
        lastPlayed: null,
      });
    });
  });

  describe('cleanupGameData', () => {
    it('应该正确清理过期数据', async () => {
      const now = Date.now();
      const mockHistory = [
        { score: 80, breakdown: {}, timestamp: now - 86400000 * 40 }, // 40天前
        { score: 90, breakdown: {}, timestamp: now - 86400000 * 20 }, // 20天前
        { score: 100, breakdown: {}, timestamp: now - 86400000 * 10 }, // 10天前
      ];

      vi.spyOn(gameDataAccessor, 'getScoreHistory').mockReturnValue(mockHistory);
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      await gameDataAccessor.cleanupGameData('test-game', {
        keepRecentDays: 30,
        maxRecords: 100,
      });

      expect(mockStorage.set).toHaveBeenCalledWith(
        'score_history_test-game',
        expect.arrayContaining([expect.objectContaining({ score: 90 }), expect.objectContaining({ score: 100 })])
      );

      consoleSpy.mockRestore();
    });
  });

  describe('exportGameData', () => {
    it('应该正确导出游戏数据', () => {
      const mockHistory = [{ score: 100, breakdown: {}, timestamp: Date.now() }];
      const mockStats = {
        totalGames: 1,
        bestScore: 100,
        averageScore: 100,
        recentAverage: 100,
        lastPlayed: Date.now(),
      };

      vi.spyOn(gameDataAccessor, 'getScoreHistory').mockReturnValue(mockHistory);
      vi.spyOn(gameDataAccessor, 'getGameStats').mockReturnValue(mockStats);

      const exportData = gameDataAccessor.exportGameData('test-game');

      expect(exportData).toEqual({
        gameId: 'test-game',
        exportTime: expect.any(Number),
        history: mockHistory,
        stats: mockStats,
      });
    });
  });
});
