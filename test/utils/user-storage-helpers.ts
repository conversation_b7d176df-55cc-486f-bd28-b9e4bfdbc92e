/**
 * user-storage 测试辅助工具
 */

import { expect, vi } from 'vitest';
import type { GameResult } from '@/types/game';

/**
 * 创建模拟的游戏结果
 */
export const createMockGameResult = (overrides: Partial<GameResult> = {}): GameResult => {
  return {
    gameId: 'test-game',
    score: 100,
    baseScore: 80,
    bonusScore: 20,
    finalScore: 100,
    duration: 30000,
    accuracy: 80,
    difficulty: 'medium',
    correctAnswers: 8,
    totalQuestions: 10,
    maxCombo: 3,
    timeBonus: 10,
    accuracyBonus: 5,
    comboBonus: 5,
    difficultyMultiplier: 1.2,
    timestamp: Date.now(),
    ...overrides,
  };
};

/**
 * 创建模拟的用户档案
 */
export const createMockUserProfile = (overrides: any = {}) => {
  return {
    userId: 'test-user',
    username: 'TestUser',
    totalScore: 500,
    experience: 1000,
    level: 5,
    bestScores: { 'test-game': 80 },
    statistics: {
      currentStreak: 3,
      dailyGoalProgress: 50,
    },
    lastActiveAt: Date.now() - 86400000, // 1天前
    ...overrides,
  };
};

/**
 * 创建模拟的分数历史记录
 */
export const createMockScoreHistory = (count: number = 5, gameId: string = 'test-game') => {
  return Array.from({ length: count }, (_, i) => ({
    score: Math.floor(Math.random() * 100) + 50,
    breakdown: {
      baseScore: 50,
      bonus: i * 5,
    },
    timestamp: Date.now() - i * 3600000, // 每小时一条记录
  }));
};

/**
 * 创建模拟的存储错误
 */
export const createMockStorageError = (type: string, message: string, key?: string) => {
  return {
    type,
    message,
    key,
    originalError: new Error(message),
  };
};

/**
 * 测试数据生成器
 */
export const testDataGenerator = {
  /**
   * 生成大量历史数据（用于性能测试）
   */
  generateLargeHistory: (count: number = 1000) => {
    return Array.from({ length: count }, (_, i) => ({
      score: Math.floor(Math.random() * 100) + 50,
      breakdown: {
        baseScore: 50,
        bonus: i % 10,
      },
      timestamp: Date.now() - i * 3600000,
    }));
  },

  /**
   * 生成边界条件数据
   */
  generateBoundaryData: () => ({
    empty: [],
    single: [{ score: 100, breakdown: {}, timestamp: Date.now() }],
    extreme: [{ score: Number.MAX_SAFE_INTEGER, breakdown: {}, timestamp: Date.now() }],
    negative: [{ score: -50, breakdown: {}, timestamp: Date.now() }],
    invalidTimestamp: [
      { score: 100, breakdown: {}, timestamp: null },
      { score: 90, breakdown: {}, timestamp: undefined },
      { score: 80, breakdown: {}, timestamp: 'invalid' },
    ],
  }),

  /**
   * 生成时间序列数据
   */
  generateTimeSeriesData: (days: number = 30) => {
    const now = Date.now();
    const dayMs = 24 * 60 * 60 * 1000;

    return Array.from({ length: days }, (_, i) => ({
      score: Math.floor(Math.random() * 50) + 50,
      breakdown: { baseScore: 50 },
      timestamp: now - i * dayMs,
    }));
  },
};

/**
 * 断言工具
 */
export const testAssertions = {
  /**
   * 验证游戏统计数据的完整性
   */
  assertGameStats: (stats: any, expectedTotalGames: number) => {
    expect(stats).toHaveProperty('totalGames', expectedTotalGames);
    expect(stats).toHaveProperty('bestScore');
    expect(stats).toHaveProperty('averageScore');
    expect(stats).toHaveProperty('recentAverage');
    expect(stats).toHaveProperty('lastPlayed');

    expect(typeof stats.totalGames).toBe('number');
    expect(typeof stats.bestScore).toBe('number');
    expect(typeof stats.averageScore).toBe('number');
    expect(typeof stats.recentAverage).toBe('number');

    if (expectedTotalGames > 0) {
      expect(stats.bestScore).toBeGreaterThanOrEqual(0);
      expect(stats.averageScore).toBeGreaterThanOrEqual(0);
      expect(stats.lastPlayed).not.toBeNull();
    } else {
      expect(stats.bestScore).toBe(0);
      expect(stats.averageScore).toBe(0);
      expect(stats.lastPlayed).toBeNull();
    }
  },

  /**
   * 验证分数历史数据的格式
   */
  assertScoreHistory: (history: any[], expectedLength?: number) => {
    expect(Array.isArray(history)).toBe(true);

    if (expectedLength !== undefined) {
      expect(history).toHaveLength(expectedLength);
    }

    history.forEach((record) => {
      expect(record).toHaveProperty('score');
      expect(record).toHaveProperty('breakdown');
      expect(record).toHaveProperty('timestamp');
      expect(typeof record.score).toBe('number');
      expect(typeof record.breakdown).toBe('object');
    });
  },

  /**
   * 验证导出数据的完整性
   */
  assertExportData: (exportData: any, gameId: string) => {
    expect(exportData).toHaveProperty('gameId', gameId);
    expect(exportData).toHaveProperty('exportTime');
    expect(exportData).toHaveProperty('history');
    expect(exportData).toHaveProperty('stats');

    expect(typeof exportData.exportTime).toBe('number');
    expect(Array.isArray(exportData.history)).toBe(true);
    expect(typeof exportData.stats).toBe('object');
  },
};

/**
 * Mock 工具
 */
export const mockUtils = {
  /**
   * 创建存储 Mock
   */
  createStorageMock: () => ({
    get: vi.fn(),
    set: vi.fn(),
    getOrNull: vi.fn(),
    remove: vi.fn(),
    clear: vi.fn(),
    setCache: vi.fn(),
    getCache: vi.fn(),
    cleanExpiredCache: vi.fn(),
  }),

  /**
   * 创建存储管理器 Mock
   */
  createStorageManagerMock: () => ({
    getUsage: vi.fn(),
    cleanup: vi.fn(),
    checkQuota: vi.fn(),
    onQuotaExceeded: null,
  }),

  /**
   * 创建用户档案管理器 Mock
   */
  createUserProfileManagerMock: () => ({
    getCurrentProfile: vi.fn(),
    updateBestScore: vi.fn(),
    updateProfile: vi.fn(),
    incrementGamePlayed: vi.fn(),
    checkGameAchievements: vi.fn(),
    addAchievement: vi.fn(),
    refreshCurrentProfile: vi.fn(),
  }),

  /**
   * 重置所有 Mock
   */
  resetAllMocks: (...mocks: any[]) => {
    mocks.forEach((mock) => {
      Object.values(mock).forEach((fn) => {
        if (typeof fn === 'function' && 'mockReset' in fn) {
          (fn as any).mockReset();
        }
      });
    });
  },
};

/**
 * 性能测试工具
 */
export const performanceUtils = {
  /**
   * 测量函数执行时间
   */
  measureTime: async (fn: () => Promise<any> | any): Promise<{ result: any; duration: number }> => {
    const start = performance.now();
    const result = await fn();
    const end = performance.now();
    return { result, duration: end - start };
  },

  /**
   * 验证性能要求
   */
  assertPerformance: (duration: number, maxDuration: number, operation: string) => {
    expect(duration).toBeLessThan(maxDuration);
    if (duration > maxDuration * 0.8) {
      console.warn(`${operation} 执行时间接近上限: ${duration.toFixed(2)}ms (上限: ${maxDuration}ms)`);
    }
  },
};
