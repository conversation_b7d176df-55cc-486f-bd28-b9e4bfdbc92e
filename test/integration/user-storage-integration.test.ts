/**
 * user-storage 集成测试
 * 测试存储系统与其他模块的集成
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { GameDataManager, gameDataAccessor } from '@/games/shared/services/user/user-storage';
import { storage, storageManager } from '@/utils/storage';
import {
  createMockGameResult,
  createMockUserProfile,
  testDataGenerator,
  performanceUtils,
} from '../utils/user-storage-helpers';

// 模拟 uni-app API
(global as any).uni = {
  getStorageSync: vi.fn(),
  setStorageSync: vi.fn(),
  removeStorageSync: vi.fn(),
  clearStorageSync: vi.fn(),
  getStorageInfo: vi.fn(),
};

describe('用户存储系统集成测试', () => {
  let gameDataManager: GameDataManager;

  beforeEach(() => {
    gameDataManager = GameDataManager.getInstance();
    vi.clearAllMocks();

    // 模拟 uni.getStorageInfo
    ((global as any).uni.getStorageInfo as any).mockImplementation((options: any) => {
      options.success({
        keys: ['test_key_1', 'test_key_2'],
        currentSize: 100,
        limitSize: 10240,
      });
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('存储系统基础功能', () => {
    it('应该正确存储和读取数据', () => {
      const testData = { name: '测试数据', value: 123, timestamp: Date.now() };

      // 模拟存储操作
      ((global as any).uni.setStorageSync as any).mockImplementation((key: string, value: string) => {
        ((global as any).uni.getStorageSync as any).mockReturnValue(value);
      });

      storage.set('test_key', testData);
      const retrieved = storage.get('test_key', {});

      expect((global as any).uni.setStorageSync).toHaveBeenCalledWith('test_key', JSON.stringify(testData));
      expect((global as any).uni.getStorageSync).toHaveBeenCalledWith('test_key');
    });

    it('应该处理存储错误', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // 模拟存储失败
      ((global as any).uni.setStorageSync as any).mockImplementation(() => {
        throw new Error('Storage quota exceeded');
      });

      const result = storage.set('test_key', { data: 'test' });

      expect(result).toBe(false);
      expect(consoleSpy).toHaveBeenCalled();

      consoleSpy.mockRestore();
    });

    it('应该正确处理缓存过期', async () => {
      const testData = { value: '临时数据' };

      // 模拟缓存设置和获取
      let storedData: any = null;
      ((global as any).uni.setStorageSync as any).mockImplementation((key: string, value: string) => {
        storedData = value;
      });

      ((global as any).uni.getStorageSync as any).mockImplementation(() => storedData);

      // 设置1毫秒过期的缓存
      storage.setCache('temp_key', testData, 0.001);

      // 立即获取应该有数据
      let cached = storage.getCache('temp_key');
      expect(cached).toEqual(testData);

      // 等待过期
      await new Promise((resolve) => setTimeout(resolve, 10));

      // 模拟过期的缓存数据
      const expiredCacheData = {
        value: testData,
        expire: Date.now() - 1000, // 1秒前过期
      };
      ((global as any).uni.getStorageSync as any).mockReturnValue(JSON.stringify(expiredCacheData));

      // 过期后应该返回 null
      cached = storage.getCache('temp_key');
      expect(cached).toBeNull();
    });
  });

  describe('游戏数据访问器集成', () => {
    it('应该正确处理完整的游戏数据流程', () => {
      const gameId = 'integration_test_game';
      const mockHistory = testDataGenerator.generateTimeSeriesData(10);

      // 模拟存储
      ((global as any).uni.getStorageSync as any).mockReturnValue(JSON.stringify(mockHistory));

      // 获取游戏统计
      const stats = gameDataAccessor.getGameStats(gameId);
      const history = gameDataAccessor.getScoreHistory(gameId);
      const bestScore = gameDataAccessor.getBestScore(gameId);

      // 验证数据一致性
      expect(stats.totalGames).toBe(history.length);
      expect(stats.bestScore).toBe(bestScore);
      expect(stats.bestScore).toBe(Math.max(...history.map((h) => h.score)));

      const calculatedAverage = Math.round(history.reduce((sum, h) => sum + h.score, 0) / history.length);
      expect(stats.averageScore).toBe(calculatedAverage);
    });

    it('应该正确处理数据清理', async () => {
      const gameId = 'cleanup_test_game';
      const mockHistory = testDataGenerator.generateTimeSeriesData(50); // 50天的数据

      let storedData = JSON.stringify(mockHistory);
      ((global as any).uni.getStorageSync as any).mockReturnValue(storedData);
      ((global as any).uni.setStorageSync as any).mockImplementation((key: string, value: string) => {
        storedData = value;
      });

      // 清理30天以前的数据，最多保留20条
      await gameDataAccessor.cleanupGameData(gameId, {
        keepRecentDays: 30,
        maxRecords: 20,
      });

      // 验证清理结果
      const cleanedHistory = JSON.parse(storedData);
      expect(cleanedHistory.length).toBeLessThanOrEqual(20);

      // 验证保留的都是最近的数据
      const cutoffTime = Date.now() - 30 * 24 * 60 * 60 * 1000;
      cleanedHistory.forEach((record: any) => {
        expect(record.timestamp).toBeGreaterThan(cutoffTime);
      });
    });

    it('应该正确导出和验证数据', () => {
      const gameId = 'export_test_game';
      const mockHistory = testDataGenerator.generateTimeSeriesData(5);

      ((global as any).uni.getStorageSync as any).mockReturnValue(JSON.stringify(mockHistory));

      const exportData = gameDataAccessor.exportGameData(gameId);

      // 验证导出数据结构
      expect(exportData).toHaveProperty('gameId', gameId);
      expect(exportData).toHaveProperty('exportTime');
      expect(exportData).toHaveProperty('history');
      expect(exportData).toHaveProperty('stats');

      // 验证数据完整性
      expect(exportData.history.length).toBe(mockHistory.length);
      expect(exportData.stats.totalGames).toBe(mockHistory.length);

      // 验证时间戳
      expect(exportData.exportTime).toBeCloseTo(Date.now(), -2); // 允许2位数差异
    });
  });

  describe('性能测试', () => {
    it('应该在合理时间内处理大量数据', async () => {
      const largeHistory = testDataGenerator.generateLargeHistory(1000);

      ((global as any).uni.getStorageSync as any).mockReturnValue(JSON.stringify(largeHistory));

      const { duration } = await performanceUtils.measureTime(() => {
        return gameDataAccessor.getGameStats('performance_test');
      });

      performanceUtils.assertPerformance(duration, 100, '处理1000条记录');
    });

    it('应该高效执行数据清理', async () => {
      const largeHistory = testDataGenerator.generateLargeHistory(500);

      ((global as any).uni.getStorageSync as any).mockReturnValue(JSON.stringify(largeHistory));
      ((global as any).uni.setStorageSync as any).mockImplementation(() => {});

      const { duration } = await performanceUtils.measureTime(async () => {
        await gameDataAccessor.cleanupGameData('performance_cleanup_test', {
          keepRecentDays: 30,
          maxRecords: 100,
        });
      });

      performanceUtils.assertPerformance(duration, 50, '清理500条记录');
    });
  });

  describe('存储配额管理', () => {
    it('应该正确获取存储使用情况', async () => {
      const usage = await storageManager.getUsage();

      expect(usage).toHaveProperty('used');
      expect(usage).toHaveProperty('total');
      expect(usage).toHaveProperty('percentage');
      expect(typeof usage.used).toBe('number');
      expect(typeof usage.total).toBe('number');
      expect(typeof usage.percentage).toBe('number');
      expect(usage.percentage).toBe(Math.round((usage.used / usage.total) * 100));
    });

    it('应该正确执行清理策略', async () => {
      const cleanupSpy = vi.spyOn(storage, 'cleanExpiredCache').mockImplementation(() => {});

      await storageManager.cleanup({
        clearExpiredCache: true,
        clearOldHistory: true,
        maxHistoryDays: 30,
      });

      expect(cleanupSpy).toHaveBeenCalled();
      cleanupSpy.mockRestore();
    });

    it('应该正确检查配额状态', async () => {
      // 模拟高使用率
      ((global as any).uni.getStorageInfo as any).mockImplementation((options: any) => {
        options.success({
          keys: [],
          currentSize: 9500, // 95% 使用率
          limitSize: 10000,
        });
      });

      const hasQuota = await storageManager.checkQuota();
      expect(hasQuota).toBe(false);

      // 模拟正常使用率
      ((global as any).uni.getStorageInfo as any).mockImplementation((options: any) => {
        options.success({
          keys: [],
          currentSize: 5000, // 50% 使用率
          limitSize: 10000,
        });
      });

      const hasQuotaNormal = await storageManager.checkQuota();
      expect(hasQuotaNormal).toBe(true);
    });
  });

  describe('边界条件和错误处理', () => {
    it('应该处理空存储情况', () => {
      ((global as any).uni.getStorageSync as any).mockReturnValue('');

      const stats = gameDataAccessor.getGameStats('empty_game');
      const history = gameDataAccessor.getScoreHistory('empty_game');

      expect(stats.totalGames).toBe(0);
      expect(history).toEqual([]);
    });

    it('应该处理损坏的JSON数据', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      ((global as any).uni.getStorageSync as any).mockReturnValue('invalid json');

      const history = gameDataAccessor.getScoreHistory('corrupted_game');

      expect(history).toEqual([]);
      consoleSpy.mockRestore();
    });

    it('应该处理存储API异常', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      ((global as any).uni.getStorageSync as any).mockImplementation(() => {
        throw new Error('Storage access denied');
      });

      const result = storage.get('error_key', { default: 'value' });

      expect(result).toEqual({ default: 'value' });
      consoleSpy.mockRestore();
    });
  });
});
