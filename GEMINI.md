# Gemini Project Configuration

This file provides context and guidelines for the Gemini AI assistant to ensure its actions align with the project's standards and goals.

所有回复请使用中文

## 1. Project Overview

This is a divination application based on the I Ching (Book of Changes), built using the `uni-app` framework. The primary technologies are Vue.js and TypeScript.

### Target Platforms

This application is designed to be compatible with multiple platforms, including H5 (Web), WeChat Mini Program, Android, and iOS. All code should be written with cross-platform compatibility in mind, leveraging `uni-app`'s capabilities to ensure consistent functionality and user experience across all supported environments.

## 2. Technology Stack

- **Framework:** uni-app, Vue 3
- **Language:** TypeScript
- **Package Manager:** pnpm
- **UI Library:** uni-ui

## 3. Important Commands

- **Type Checking:** `pnpm run type-check`
- **Development (H5):** `pnpm run dev:h5`
- **Build (H5):** `pnpm run build:h5`

## 4. Code Style & Structure

- **Content Fetching Strategy:** The method for fetching hexagram details is platform-dependent. 
    - For the **local dev environment**, content is loaded directly from local HTML files located in `src/assets/`. This is an intentional design for demonstration purposes.
    - For **all other scenario** , the content is fetched from a remote API.
    - **Do not** suggest refactoring the local HTML files into a unified data structure, as this separation is a deliberate architectural choice.

- **TypeScript Declarations:** All global type declaration files (`.d.ts`) should be placed in the `src/types/` directory.
- **Platform-Specific Code:** Code that provides compatibility or polyfills for specific platforms (like WeChat Mini Program) should be located in the `src/platform/` directory, organized by platform (e.g., `src/platform/mp-weixin/`).
- **State Management:** (Future consideration) If a state management library is needed, prefer Pinia.
