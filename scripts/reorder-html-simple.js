#!/usr/bin/env node
/**
 * HTML文件代码片段重排序脚本 (简化版，无外部依赖)
 * 将每个HTML文件中的6个<h4>...</h4><div>...</div>...代码片段从123456顺序调整为654321
 */

const fs = require('fs');
const path = require('path');

/**
 * 递归查找目录下的所有HTML文件
 */
function findHtmlFiles(dir) {
  const files = [];

  try {
    const items = fs.readdirSync(dir);

    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        // 递归查找子目录
        files.push(...findHtmlFiles(fullPath));
      } else if (stat.isFile() && item.toLowerCase().endsWith('.html')) {
        files.push(fullPath);
      }
    }
  } catch (err) {
    console.error(`读取目录 ${dir} 时出错: ${err.message}`);
  }

  return files;
}

/**
 * 提取并重排序HTML代码片段
 */
function processHtmlContent(content) {
  // 找到所有<h4>标签的位置
  const h4Positions = [];
  const h4Regex = /<h4[^>]*>/gi;
  let match;

  while ((match = h4Regex.exec(content)) !== null) {
    h4Positions.push(match.index);
  }

  if (h4Positions.length < 6) {
    return {
      success: false,
      error: `找到 ${h4Positions.length} 个<h4>标签，至少需要6个`
    };
  }

  // 查找文档尾部标签的位置（</body>和</html>）
  const bodyEndMatch = content.match(/<\/body\s*>/i);
  const htmlEndMatch = content.match(/<\/html\s*>/i);

  // 确定内容区域的结束位置（排除</body>和</html>）
  let contentEndPos = content.length;
  if (bodyEndMatch) {
    contentEndPos = Math.min(contentEndPos, bodyEndMatch.index);
  }
  if (htmlEndMatch) {
    contentEndPos = Math.min(contentEndPos, htmlEndMatch.index);
  }

  // 计算要处理的片段范围（后6个）
  const totalSections = h4Positions.length;
  const startIndex = totalSections - 6; // 从倒数第6个开始
  const targetPositions = h4Positions.slice(startIndex); // 取后6个位置

  console.log(`  找到 ${totalSections} 个<h4>标签，将处理后6个片段（第${startIndex + 1}-${totalSections}个）`);

  // 提取后6个代码片段
  const sections = [];
  for (let i = 0; i < targetPositions.length; i++) {
    const startPos = targetPositions[i];
    let endPos;

    if (i < targetPositions.length - 1) {
      // 不是最后一个片段，结束位置是下一个<h4>的开始
      endPos = targetPositions[i + 1];
    } else {
      // 最后一个片段，结束位置是内容区域结束（不包括</body>和</html>）
      endPos = contentEndPos;
    }

    const section = content.substring(startPos, endPos).trimEnd();
    sections.push(section);
  }



  // 重新排序: 654321（对后6个片段进行重排序）
  const reorderedSections = [
    sections[5], // 第6个片段
    sections[4], // 第5个片段
    sections[3], // 第4个片段
    sections[2], // 第3个片段
    sections[1], // 第2个片段
    sections[0]  // 第1个片段
  ];

  // 获取第一个要处理的<h4>之前的内容（文档头部 + 前面未处理的片段）
  let header;
  if (startIndex > 0) {
    // 有前面未处理的片段，包含它们
    header = content.substring(0, targetPositions[0]);
  } else {
    // 没有前面的片段，只取到第一个<h4>之前
    header = content.substring(0, h4Positions[0]);
  }

  // 获取文档尾部（</body>和</html>等）
  const footer = content.substring(contentEndPos);

  // 重新组装内容
  const newContent = header + reorderedSections.join('\n') + footer;

  return {
    success: true,
    content: newContent
  };
}

/**
 * 处理单个HTML文件
 */
function processFile(filePath) {
  console.log(`处理: ${filePath}`);

  try {
    // 读取文件
    const originalContent = fs.readFileSync(filePath, 'utf-8');

    // 处理内容
    const result = processHtmlContent(originalContent);

    if (!result.success) {
      console.log(`  跳过: ${result.error}`);
      return false;
    }

    // 写回文件
    fs.writeFileSync(filePath, result.content, 'utf-8');

    console.log(`  ✓ 成功重排序`);
    return true;

  } catch (err) {
    console.log(`  ✗ 错误: ${err.message}`);
    return false;
  }
}

/**
 * 主函数
 */
function main() {
  // 获取目标目录
  const args = process.argv.slice(2);
  let targetDir = 'html';

  if (args.length > 0) {
    targetDir = args[0];
  }

  // 检查目录是否存在
  if (!fs.existsSync(targetDir)) {
    console.error(`错误: 目录 ${targetDir} 不存在`);
    console.log('用法: node reorder-html-simple.js [目录路径]');
    console.log('示例: node reorder-html-simple.js src/assets');
    process.exit(1);
  }

  // 查找HTML文件
  console.log(`在 ${targetDir} 目录下查找HTML文件...`);
  const htmlFiles = findHtmlFiles(targetDir);

  if (htmlFiles.length === 0) {
    console.log(`在 ${targetDir} 目录下未找到HTML文件`);
    return;
  }

  console.log(`找到 ${htmlFiles.length} 个HTML文件:`);
  htmlFiles.sort().forEach(file => {
    console.log(`  - ${path.relative(process.cwd(), file)}`);
  });
  console.log();

  // 处理每个文件
  let successCount = 0;
  htmlFiles.forEach(htmlFile => {
    if (processFile(htmlFile)) {
      successCount++;
    }
    console.log();
  });

  console.log(`处理完成: ${successCount}/${htmlFiles.length} 个文件成功处理`);

  if (successCount > 0) {
    console.log('\n重排序规则: 原顺序 1,2,3,4,5,6 -> 新顺序 6,5,4,3,2,1');
    console.log('所有<h4>和<div>中的文本内容保持不变，只调整了代码片段的顺序');
  }
}

// 运行主函数
if (require.main === module) {
  main();
}
