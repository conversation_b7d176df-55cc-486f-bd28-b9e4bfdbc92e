/* eslint-disable  */
const fs = require('fs');
const path = require('path');
const { JSDOM } = require('jsdom');

// 处理目录
// const processDirectory = async (dirPath) => {
try {
  // 检查目录是否存在
  if (!fs.existsSync(dirPath)) {
    console.error(`目录 ${dirPath} 不存在`);
    return;
  }

  // 获取目录中的所有文件
  const files = fs.readdirSync(dirPath);

  // 筛选出HTML文件
  const htmlFiles = files.filter((file) => path.extname(file).toLowerCase() === '.html');

  console.log(`找到 ${htmlFiles.length} 个HTML文件`);

  // 处理每个HTML文件
  for (const file of htmlFiles) {
    const filePath = path.join(dirPath, file);
    await processHtmlFile(filePath);
  }

  console.log('所有文件处理完成');
} catch (error) {
  console.error('处理目录时出错:', error);
}

// 处理单个HTML文件
const processHtmlFile = async (filePath) => {
  try {
    console.log(`处理文件: ${filePath}`);

    // 读取文件内容
    let html = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // 使用JSDOM解析HTML
    const dom = new JSDOM(html);
    const document = dom.window.document;

    // 查找所有div元素
    const divs = document.querySelectorAll('div');

    for (const div of divs) {
      // 获取div的文本内容
      const text = div.textContent.trim();

      // 检查是否包含"傅佩荣解"
      if (text.includes('傅佩荣解') || text.includes('傅佩荣解:') || text.includes('傅佩荣解：')) {
        console.log(`  找到傅佩荣解内容: ${text.substring(0, 50)}...`);

        // 处理内容，在四个关键词前添加换行符
        let content = div.innerHTML;

        // 使用正则表达式添加换行符，注意保留原有的空白
        content = content.replace(/([^<>]*)(时运[：:])/g, '$1<br>$2');
        content = content.replace(/([^<>]*)(财运[：:])/g, '$1<br>$2');
        content = content.replace(/([^<>]*)(家宅[：:])/g, '$1<br>$2');
        content = content.replace(/([^<>]*)(身体[：:])/g, '$1<br>$2');

        // 更新div内容
        div.innerHTML = content;
        modified = true;

        console.log('  已添加换行符');
      }
    }

    if (!modified) {
      console.log(`  未找到需要修改的内容`);
      return;
    }

    // 将修改后的HTML写回文件
    const modifiedHtml = dom.serialize();
    fs.writeFileSync(filePath, modifiedHtml, 'utf8');
    console.log(`  文件已更新: ${filePath}`);
  } catch (error) {
    console.error(`处理文件 ${filePath} 时出错:`, error);
  }
};

// 主函数
const main = async () => {
  const dirPath = path.resolve('scripts');
  await processDirectory(dirPath);
};

// 执行主函数
main().catch((error) => {
  console.error('执行过程中出错:', error);
});
