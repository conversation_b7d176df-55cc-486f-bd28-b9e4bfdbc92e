/**
 * 提取卦象解释脚本
 *
 * 功能：
 * 1. 遍历指定目录下所有HTML文件
 * 2. 从每个文件中提取"梅花易数"相关解卦辞 - 第一个包含"梅花易数"的div元素后的下一个div中的文本
 * 3. 从每个文件中提取"傅佩荣解"相关解卦辞 - 第一个包含"傅佩荣解"的div中，第一个<br>标签后的内容直到</div>
 * 4. 将提取的解卦辞整理成JSON格式，用于TodayHexagram组件
 */

/* eslint-disable */
const fs = require('fs');
const path = require('path');
const { JSDOM } = require('jsdom');

// 源HTML文件目录
const HTML_DIR = path.resolve('src/assets');
// 输出文件路径
const OUTPUT_FILE_JSON = path.resolve('src/utils/hexagram-interpretation.json');
const OUTPUT_FILE_TS = path.resolve('src/utils/hexagram-interpretation.ts');

// 存储所有卦象解释
const hexagramInterpretations = {};

/**
 * 从HTML文件中提取卦象ID
 * @param {string} filename - HTML文件名
 * @returns {number} 卦象ID
 */
function extractHexagramId(filename) {
  // 从文件名中提取数字作为卦象ID
  const match = filename.match(/(\d+)\.html$/);
  if (match && match[1]) {
    return parseInt(match[1], 10);
  }
  return null;
}

/**
 * 从HTML内容中提取梅花易数解卦辞
 * 严格按照要求：提取第一个"梅花易数"的div后的下一个div中的文本
 * @param {string} html - HTML内容
 * @returns {string|null} 提取的解卦辞
 */
function extractMeihuaInterpretation(html) {
  try {
    // 使用DOM方式查找
    const dom = new JSDOM(html);
    const document = dom.window.document;

    // 直接查找梅花易数的容器
    const meihuaContainer = document.querySelector('.v-container.mh');
    if (meihuaContainer) {
      // 获取第二个div，即解卦辞内容
      const contentDiv = meihuaContainer.querySelectorAll('div')[1];
      if (contentDiv) {
        // 获取div的文本内容
        const content = contentDiv.textContent.trim();
        console.log(`  提取梅花易数解卦辞: ${content.substring(0, 30)}...`);
        return content;
      }
    }

    return null;
  } catch (error) {
    console.error('提取梅花易数解卦辞时出错:', error);
    return null;
  }
}

/**
 * 从HTML内容中提取傅佩荣解卦辞
 * 严格按照要求：提取第一个"傅佩荣解"的地方，从后面第一个<br>标签后截取文本，一直到下一个</div>
 * @param {string} html - HTML内容
 * @returns {string|null} 提取的解卦辞
 */
function extractFupeirongInterpretation(html) {
  try {
    // 使用DOM方式查找
    const dom = new JSDOM(html);
    const document = dom.window.document;

    // 查找所有div元素
    const divs = Array.from(document.querySelectorAll('div'));

    // 查找第一个包含"傅佩荣解"的div元素
    for (const div of divs) {
      if (div.textContent.includes('傅佩荣解')) {
        console.log(`  找到傅佩荣解div: ${div.textContent.substring(0, 30)}...`);

        // 获取div的HTML内容
        const html = div.innerHTML;

        // 查找第一个<br>标签后的内容
        const brIndex = html.indexOf('<br>');
        if (brIndex !== -1) {
          // 提取<br>后到</div>前的内容，移除结束的</div>标签
          let content = html.substring(brIndex + 4);
          // 移除可能的</div>结束标签及其前面的所有空白字符
          content = content.replace(/\s*<\/div>\s*$/i, '').trim();
          console.log(`  提取傅佩荣解卦辞: ${content.substring(0, 30)}...`);
          return content;
        } else {
          // 如果没有<br>标签，尝试提取冒号后的内容
          const colonIndex = html.indexOf(':');
          if (colonIndex !== -1) {
            let content = html.substring(colonIndex + 1);
            // 移除可能的</div>结束标签及其前面的所有空白字符
            content = content.replace(/\s*<\/div>\s*$/i, '').trim();
            console.log(`  提取傅佩荣解卦辞(冒号后): ${content.substring(0, 30)}...`);
            return content;
          }
        }
        break;
      }
    }

    return null;
  } catch (error) {
    console.error('提取傅佩荣解卦辞时出错:', error);
    return null;
  }
}

/**
 * 处理单个HTML文件
 * @param {string} filePath - HTML文件路径
 */
function processHtmlFile(filePath) {
  try {
    const hexagramId = extractHexagramId(path.basename(filePath));
    if (!hexagramId) {
      console.warn(`无法从文件名提取卦象ID: ${filePath}`);
      return;
    }

    console.log(`处理文件: ${filePath} (卦象ID: ${hexagramId})`);

    // 读取文件内容
    const html = fs.readFileSync(filePath, 'utf8');

    // 提取梅花易数解卦辞
    const meihuaInterpretation = extractMeihuaInterpretation(html);

    // 提取傅佩荣解卦辞
    const fupeirongInterpretation = extractFupeirongInterpretation(html);

    // 存储提取的解卦辞
    if (!hexagramInterpretations[hexagramId]) {
      hexagramInterpretations[hexagramId] = {};
    }

    if (meihuaInterpretation) {
      hexagramInterpretations[hexagramId].meihua = meihuaInterpretation;
    } else {
      console.warn(`  未找到梅花易数解卦辞`);
    }

    if (fupeirongInterpretation) {
      hexagramInterpretations[hexagramId].fpr = fupeirongInterpretation;
    } else {
      console.warn(`  未找到傅佩荣解卦辞`);
    }
  } catch (error) {
    console.error(`处理文件 ${filePath} 时出错:`, error);
  }
}

/**
 * 处理目录中的所有HTML文件
 * @param {string} dirPath - 目录路径
 */
function processDirectory(dirPath) {
  try {
    // 检查目录是否存在
    if (!fs.existsSync(dirPath)) {
      console.error(`目录 ${dirPath} 不存在`);
      return;
    }

    // 获取目录中的所有文件
    const files = fs.readdirSync(dirPath);

    // 筛选出HTML文件
    const htmlFiles = files.filter((file) => path.extname(file).toLowerCase() === '.html');

    console.log(`找到 ${htmlFiles.length} 个HTML文件`);

    // 处理每个HTML文件
    for (const file of htmlFiles) {
      const filePath = path.join(dirPath, file);
      processHtmlFile(filePath);
    }

    console.log('所有文件处理完成');
  } catch (error) {
    console.error('处理目录时出错:', error);
  }
}

/**
 * 生成JSON输出文件
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
function generateJsonOutput() {
  try {
    const jsonContent = JSON.stringify(hexagramInterpretations, null, 2);
    fs.writeFileSync(OUTPUT_FILE_JSON, jsonContent, 'utf8');
    console.log(`JSON文件已生成: ${OUTPUT_FILE_JSON}`);
  } catch (error) {
    console.error('生成JSON文件时出错:', error);
  }
}

/**
 * 生成TypeScript输出文件
 */
function generateTsOutput() {
  try {
    // 创建TypeScript接口和数据
    const tsContent = `/**
 * 卦象解释数据
 * 自动生成，请勿手动修改
 */

export interface HexagramInterpretation {
  meihua?: string;  // 梅花易数解卦辞
  fpr?: string;  // 傅佩荣解卦辞
}

export type HexagramInterpretations = {
  [key: number]: HexagramInterpretation;
};

export const hexagramInterpretations: HexagramInterpretations = ${JSON.stringify(hexagramInterpretations, null, 2)};

/**
 * 获取指定卦象的解释
 * @param id 卦象ID (1-64)
 * @returns 卦象解释对象
 */
export function getHexagramInterpretation(id: number): HexagramInterpretation {
  return hexagramInterpretations[id] || {};
}
`;

    fs.writeFileSync(OUTPUT_FILE_TS, tsContent, 'utf8');
    console.log(`TypeScript文件已生成: ${OUTPUT_FILE_TS}`);
  } catch (error) {
    console.error('生成TypeScript文件时出错:', error);
  }
}

/**
 * 主函数
 */
function main() {
  console.log('开始提取卦象解释...');

  // 处理HTML文件目录
  processDirectory(HTML_DIR);

  // 生成输出文件
  // generateJsonOutput();
  generateTsOutput();

  console.log('提取完成！');
}

// 执行主函数
main();
