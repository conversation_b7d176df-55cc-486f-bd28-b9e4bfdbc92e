#!/usr/bin/env node
/**
 * HTML文件压缩脚本
 * 将HTML文件压缩并编码，准备部署到远程服务器
 */

const fs = require('fs');
const path = require('path');
const zlib = require('zlib');
const crypto = require('crypto');

// 配置
const CONFIG = {
  // 输入目录（原始HTML文件）
  inputDir: 'src/assets',

  // 输出目录（压缩后的文件）
  outputDir: 'dist/hexagram',

  // 压缩选项
  compression: {
    level: 9, // 最高压缩级别
    windowBits: 15,
    memLevel: 8,
  },

  // 加密密钥（可选）
  encryptionKey: 'yi-hexagram-2024',

  // 输出文件扩展名
  outputExtension: '.dat',

  // 是否启用加密
  enableEncryption: false,
};

/**
 * 压缩HTML内容
 */
function compressContent(htmlContent) {
  try {
    // 1. 移除多余的空白字符（可选的预处理）
    const minified = htmlContent
      .replace(/\s+/g, ' ')           // 多个空格合并为一个
      .replace(/>\s+</g, '><')        // 移除标签间的空白
      .trim();

    // 2. Gzip压缩
    const compressed = zlib.gzipSync(minified, CONFIG.compression);

    console.log(`  原始大小: ${htmlContent.length} bytes`);
    console.log(`  压缩后: ${compressed.length} bytes`);
    console.log(`  压缩率: ${((1 - compressed.length / htmlContent.length) * 100).toFixed(1)}%`);

    return compressed;
  } catch (error) {
    console.error('压缩失败:', error);
    return null;
  }
}

/**
 * 加密内容（可选）
 */
function encryptContent(buffer, key) {
  try {
    const algorithm = 'aes-256-cbc';
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(algorithm, key);

    let encrypted = Buffer.concat([cipher.update(buffer), cipher.final()]);

    // 将IV和加密内容合并
    const result = Buffer.concat([iv, encrypted]);

    console.log(`  加密后: ${result.length} bytes`);
    return result;
  } catch (error) {
    console.error('加密失败:', error);
    return buffer; // 加密失败时返回原内容
  }
}

/**
 * 编码为Base64
 */
function encodeToBase64(buffer) {
  return buffer.toString('base64');
}

/**
 * 处理单个HTML文件
 */
function processHtmlFile(inputPath, outputPath) {
  console.log(`处理: ${path.basename(inputPath)}`);

  try {
    // 1. 读取原始HTML文件
    const htmlContent = fs.readFileSync(inputPath, 'utf-8');

    // 2. 压缩
    const compressed = compressContent(htmlContent);
    if (!compressed) {
      console.log('  跳过: 压缩失败');
      return false;
    }

    // 3. 加密（可选）
    let processed = compressed;
    if (CONFIG.enableEncryption) {
      processed = encryptContent(compressed, CONFIG.encryptionKey);
    }

    // 4. Base64编码
    const encoded = encodeToBase64(processed);

    // 5. 写入输出文件
    fs.writeFileSync(outputPath, encoded, 'utf-8');

    console.log(`  ✓ 输出: ${path.basename(outputPath)}`);
    console.log(`  最终大小: ${encoded.length} bytes`);

    return true;
  } catch (error) {
    console.log(`  ✗ 错误: ${error.message}`);
    return false;
  }
}

/**
 * 创建解压缩的JavaScript代码
 */
function generateDecompressCode() {
  const decompressCode = `
/**
 * HTML内容解压缩工具
 * 在客户端使用此代码解压缩远程加载的内容
 */

// 检查是否是压缩内容
function isCompressedContent(data) {
  return typeof data === 'string' && /^[A-Za-z0-9+/]+=*$/.test(data) && data.length > 100;
}

// 解压缩内容
function decompressContent(encodedData) {
  try {
    // 1. Base64解码
    const binaryString = atob(encodedData);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    
    ${CONFIG.enableEncryption ? `
    // 2. 解密（如果启用了加密）
    // 注意：这里需要实现对应的解密逻辑
    // const decrypted = decryptContent(bytes, '${CONFIG.encryptionKey}');
    ` : ''}
    
    // ${CONFIG.enableEncryption ? '3' : '2'}. 解压缩
    const decompressed = pako.ungzip(bytes, { to: 'string' });
    
    return decompressed;
  } catch (error) {
    console.error('解压缩失败:', error);
    return null;
  }
}

// 使用示例：
// const content = decompressContent(res.data);
`;

  const outputPath = path.join(CONFIG.outputDir, 'decompress-utils.js');
  fs.writeFileSync(outputPath, decompressCode.trim(), 'utf-8');
  console.log(`生成解压缩工具: ${outputPath}`);
}

/**
 * 主函数
 */
function main() {
  console.log('🗜️  HTML文件压缩脚本');
  console.log('=====================================');

  // 检查输入目录
  if (!fs.existsSync(CONFIG.inputDir)) {
    console.error(`❌ 输入目录不存在: ${CONFIG.inputDir}`);
    process.exit(1);
  }

  // 创建输出目录
  if (!fs.existsSync(CONFIG.outputDir)) {
    fs.mkdirSync(CONFIG.outputDir, { recursive: true });
    console.log(`📁 创建输出目录: ${CONFIG.outputDir}`);
  }

  // 查找HTML文件
  const htmlFiles = fs.readdirSync(CONFIG.inputDir)
    .filter(file => file.toLowerCase().endsWith('.html'))
    .map(file => ({
      input: path.join(CONFIG.inputDir, file),
      output: path.join(CONFIG.outputDir, file.replace('.html', CONFIG.outputExtension))
    }));

  if (htmlFiles.length === 0) {
    console.log(`📭 在 ${CONFIG.inputDir} 中未找到HTML文件`);
    return;
  }

  console.log(`📋 找到 ${htmlFiles.length} 个HTML文件`);
  console.log(`🔧 压缩级别: ${CONFIG.compression.level}`);
  console.log(`🔐 加密: ${CONFIG.enableEncryption ? '启用' : '禁用'}`);
  console.log('');

  // 处理每个文件
  let successCount = 0;
  let totalOriginalSize = 0;
  let totalCompressedSize = 0;

  htmlFiles.forEach(({ input, output }) => {
    const originalSize = fs.statSync(input).size;
    totalOriginalSize += originalSize;

    if (processHtmlFile(input, output)) {
      successCount++;
      const compressedSize = fs.statSync(output).size;
      totalCompressedSize += compressedSize;
    }
    console.log('');
  });

  // 生成解压缩工具
  generateDecompressCode();

  // 总结
  console.log('=====================================');
  console.log(`✅ 处理完成: ${successCount}/${htmlFiles.length} 个文件`);
  console.log(`📊 总体压缩率: ${((1 - totalCompressedSize / totalOriginalSize) * 100).toFixed(1)}%`);
  console.log(`💾 节省空间: ${(totalOriginalSize - totalCompressedSize)} bytes`);
  console.log(`📤 输出目录: ${CONFIG.outputDir}`);

  if (successCount > 0) {
    console.log('');
    console.log('🚀 下一步:');
    console.log('1. 将输出目录中的文件上传到CDN或服务器');
    console.log('2. 在客户端使用 decompress-utils.js 中的函数解压缩');
    console.log('3. 确保客户端已引入 pako.js 库用于gzip解压缩');
  }
}

// 运行脚本
if (require.main === module) {
  main();
}
