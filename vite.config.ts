import { defineConfig } from 'vite';
import uni from '@dcloudio/vite-plugin-uni';
import path from 'path';
import { CDN_DOMAIN } from './src/config';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [uni()],
  resolve: {
    alias: {
      '@': path.join(process.cwd(), './src'),
      pako: 'pako/dist/pako.min.js',
    },
  },
  server: {
    proxy: {
      '/hexagram': {
        target: CDN_DOMAIN,
        changeOrigin: true,
        // rewrite: (path) => path.replace(/^\/hexagram/, '')
      },
    },
  },
  build: {
    outDir: 'unpackage', // 强制使用 unpackage
    // 基础构建优化
    target: 'es2015',
    // 压缩配置 - 生产环境移除console
    // minify: 'terser',
    // terserOptions: {
    //   compress: {
    //     drop_console: true,
    //     drop_debugger: true,
    //   },
    // },
    // 确保pako库被正确打包
    rollupOptions: {
      external: [],
      output: {
        globals: {},
      },
    },
  },
  // 优化依赖处理
  optimizeDeps: {
    include: ['pako'],
    esbuildOptions: {
      target: 'esnext',
      format: 'esm',
    },
  },
});
