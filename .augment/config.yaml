# Augment Configuration for uni-yi Project
# 易境万象项目配置

# 规则文件配置
rules:
  main_file: "rules.yaml"
  rule_files:
    - "rules/general.yaml"
    - "rules/typescript.yaml"
    - "rules/vue.yaml"
    - "rules/uni-app.yaml"

# 语言设置
language:
  response_language: "chinese"  # 始终使用中文回复

project:
  name: "uni-yi"
  description: "易境时空 - 易经智慧应用"
  type: "uni-app"
  framework: "Vue3 + TypeScript + SCSS"
  platforms:
    - "H5"
    - "MP-WEIXIN"
    - "APP-PLUS"

architecture:
  frontend: "Vue3 Composition API"
  styling: "SCSS with component-based organization"
  state_management: "Pinia (lightweight)"
  storage: "localStorage + Pinia"
  icons: "SVG with platform adaptation"
  safe_area: "PageWrapper component approach"

preferences:
  # 代码组织偏好
  code_organization:
    style_location: "component_based"  # 样式放在组件中
    directory_structure: "simplified"  # 简化的目录结构
    file_naming: "consistent"          # 一致的命名规范
  
  # 平台适配偏好
  platform_adaptation:
    strategy: "conditional_compilation"  # 使用条件编译
    tabbar_approach: "custom_component"  # 自定义TabBar组件
    safe_area_approach: "wrapper_component"  # 包装器组件方案
  
  # 开发偏好
  development:
    typescript_usage: "strict"         # 严格TypeScript
    component_api: "composition"       # Composition API
    styling_approach: "scoped_scss"    # Scoped SCSS
    icon_system: "unified_svg"         # 统一SVG图标系统

patterns:
  # 需要特别关注的文件模式
  focus_patterns:
    - "src/components/*.vue"
    - "src/pages/**/*.vue"
    - "src/utils/*.ts"
    - "src/stores/*.ts"
    - "src/uni.scss"
  
  # 忽略的文件模式
  ignore_patterns:
    - "node_modules/**"
    - "dist/**"
    - "unpackage/**"
    - "*.log"
    - ".git/**"
    - "src/assets/*.html"  # 卦象HTML文件

# 项目特定的最佳实践
best_practices:
  components:
    - "使用PageWrapper包装所有页面"
    - "TabBar样式集中在YiTabBar.vue中"
    - "安全区域样式集中在PageWrapper.vue中"
    - "使用YiIcon统一管理图标"
  
  styling:
    - "避免单独的styles文件夹"
    - "uni.scss保持在src根目录"
    - "使用条件编译处理平台差异"
    - "全局样式放在组件的非scoped style中"
  
  state_management:
    - "运行时状态使用Pinia"
    - "持久化数据使用localStorage"
    - "存储键名统一在constants.ts中定义"
    - "避免复杂的持久化插件"
  
  platform_adaptation:
    - "H5环境隐藏原生tabBar"
    - "小程序使用CSS mask图标"
    - "H5使用SVG图标"
    - "安全区域适配只在小程序和APP中生效"

# 代码质量标准
quality_standards:
  typescript:
    - "所有组件使用TypeScript"
    - "Props定义要有类型和默认值"
    - "使用严格的类型检查"
  
  vue:
    - "使用Composition API"
    - "组件名使用PascalCase"
    - "使用setup语法糖"
    - "合理使用响应式API"
  
  scss:
    - "使用scoped样式避免污染"
    - "合理使用SCSS变量和混入"
    - "保持样式的可维护性"
