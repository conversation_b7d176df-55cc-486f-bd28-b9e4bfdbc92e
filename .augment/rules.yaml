# Augment Rules for uni-yi Project
# 易境万象项目代码规范和最佳实践
# 主规则文件，引用分场景的规则文件

# 规则文件引用
includes:
  - "./rules/general.yaml"      # 通用开发规范
  - "./rules/typescript.yaml"  # TypeScript规范
  - "./rules/vue.yaml"         # Vue.js规范
  - "./rules/uni-app.yaml"     # uni-app项目特定规范

# 规则应用配置
rule_application:
  # 始终应用的规则
  always_apply:
    - "general_principles"
    - "uni_app_specific"

  # 按需应用的规则
  agent_requested:
    - "typescript_standards"
    - "vuejs_standards"

  # 文件模式匹配 (简化重复规则)
  file_patterns:
    "**/*.ts":
      - "general_principles"
      - "typescript_standards"
      - "uni_app_specific"

    "**/*.tsx":
      - "general_principles"
      - "typescript_standards"
      - "uni_app_specific"

    "**/*.d.ts":
      - "general_principles"
      - "typescript_standards"

    "**/*.vue":
      - "general_principles"
      - "vuejs_standards"
      - "typescript_standards"
      - "uni_app_specific"

    "**/*.scss":
      - "general_principles"
      - "uni_app_specific"

# 项目特定配置
project_config:
  name: "uni-yi"
  type: "uni-app"
  platforms: ["H5", "MP-WEIXIN", "APP-PLUS"]

  # 优先级设置
  rule_priority:
    1: "uni_app_specific"     # 项目特定规则优先级最高
    2: "vuejs_standards"      # Vue.js规范
    3: "typescript_standards" # TypeScript规范
    4: "general_principles"   # 通用规范优先级最低

