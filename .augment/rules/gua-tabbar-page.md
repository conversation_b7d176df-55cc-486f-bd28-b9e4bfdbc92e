# 卦典 Tabbar 页面开发规范

## 页面结构

### 主页面：`src/pages/gua/index.vue`
- **功能**：卦典主入口页面，提供导航到六十四卦和八卦的功能
- **路径**：作为 tabbar 页面，路径为 `pages/gua/index`
- **设计**：双导航卡片布局，包含功能特色和最近浏览

### 子页面：`src/pages/gua/bagua.vue`
- **功能**：先天八卦展示页面
- **路径**：`pages/gua/bagua`
- **设计**：八卦网格布局，包含详细属性说明

## 导航逻辑

### 主要导航
1. **六十四卦导航**
   - 目标页面：`/pages/hexagram/list`
   - 功能：完整的六十四卦体系浏览
   - 特色：文王卦序和八宫卦序切换

2. **八卦导航**
   - 目标页面：`/pages/gua/bagua`
   - 功能：先天八卦基础理论学习
   - 特色：八卦属性详细说明

### 辅助功能
- **最近浏览**：显示最近查看的卦象，支持快速跳转
- **功能特色**：展示卦典的核心功能亮点

## 设计规范

### 视觉风格
- **主色调**：#8B4513（棕色系，与易经主题一致）
- **辅助色**：#2c3e50（深蓝灰）、#7f8c8d（中性灰）
- **背景**：渐变白色到浅灰 `linear-gradient(135deg, #fff 0%, #f8f9fa 100%)`

### 布局原则
- **卡片设计**：圆角 24rpx，阴影效果增强层次感
- **网格布局**：响应式网格，适配不同屏幕尺寸
- **间距统一**：30rpx 外边距，32rpx 内边距

### 交互效果
- **点击反馈**：`transform: scale(0.98)` 缩放效果
- **阴影变化**：点击时阴影加深，增强反馈感
- **过渡动画**：`transition: all 0.3s ease` 平滑过渡

## 组件使用

### PageWrapper
- **标题**：卦典主页使用 "卦典"，八卦页使用 "先天八卦"
- **返回按钮**：主页不显示（tabbar页面），子页面显示
- **Logo**：主页显示，子页面不显示

### YiIcon
- **箭头图标**：使用 `arrow-right`，尺寸 20，颜色 #8B4513
- **统一风格**：保持与项目整体图标风格一致

## 数据管理

### 最近浏览
- **存储键**：`recent_hexagrams`
- **数据格式**：数组，存储卦象 ID
- **显示数量**：最多显示 3 个最近浏览的卦象
- **清理策略**：超出限制时移除最旧的记录

### 八卦数据
- **数据结构**：包含名称、符号、属性、方位、家庭关系等
- **展示方式**：2x4 网格布局，每个卦占一个卡片
- **交互方式**：点击显示详细信息的模态框

## 路由配置

### pages.json 配置
```json
{
  "path": "pages/gua/index",
  "style": {
    "navigationBarTitleText": "卦典",
    "navigationStyle": "custom"
  }
},
{
  "path": "pages/gua/bagua",
  "style": {
    "navigationBarTitleText": "先天八卦",
    "navigationStyle": "custom"
  }
}
```

### tabBar 配置
```json
{
  "pagePath": "pages/gua/index",
  "text": "卦典"
}
```

## 开发注意事项

### 性能优化
- **图片懒加载**：如有图片资源，使用懒加载
- **数据缓存**：最近浏览数据使用本地存储
- **组件复用**：复用现有的 PageWrapper 和 YiIcon 组件

### 兼容性
- **小程序适配**：确保在微信小程序中正常显示
- **响应式设计**：适配不同屏幕尺寸
- **字体兼容**：使用系统字体栈，确保跨平台一致性

### 错误处理
- **存储异常**：try-catch 包装本地存储操作
- **导航异常**：检查页面路径有效性
- **数据异常**：提供默认值和错误提示

## 扩展规划

### 功能扩展
- **搜索功能**：支持卦象名称和描述搜索
- **收藏功能**：用户可收藏常用卦象
- **学习进度**：记录用户学习进度和偏好

### 内容扩展
- **后天八卦**：增加后天八卦对比学习
- **卦象关系**：展示卦象之间的相互关系
- **历史典故**：增加卦象相关的历史故事

### 交互优化
- **手势操作**：支持滑动切换卦象
- **语音朗读**：支持卦象内容语音播报
- **夜间模式**：提供深色主题选项
