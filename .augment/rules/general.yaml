# 通用开发规范
# 适用于所有文件和场景

metadata:
  name: "general_principles"
  description: "通用开发规范和最佳实践"
  type: "always_apply"
  applies_to: "*"

rules:
  platform_support:
    name: "多平台支持要求"
    pattern: "*"
    description: "uni-app多平台开发规范"
    rule: |
      - 必须支持H5、微信小程序、Android、iOS平台
      - 基于uni-app开发，使用TypeScript语言
      - 使用条件编译处理平台差异
      - 注：具体平台适配规则见uni-app.yaml

  project_structure:
    name: "项目结构组织原则"
    pattern: "*"
    description: "项目目录和文件组织规范"
    rule: |
      - 按功能或领域划分目录，遵循"关注点分离"原则
      - 使用一致且描述性的目录和文件命名
      - 相关功能放在同一模块，减少跨模块依赖
      - 避免过深的目录嵌套，一般不超过3-4层
      - 区分代码、资源、配置文件
      - 集中管理依赖，避免多处声明
      - 遵循uni-app框架的标准项目结构约定

  development_principles:
    name: "通用开发原则"
    pattern: "*.ts,*.vue,*.js"
    description: "代码质量和开发规范"
    rule: |
      - 编写可测试的代码，组件保持单一职责
      - 避免重复代码，提取共用逻辑到工具函数
      - 保持代码简洁明了，遵循KISS原则
      - 使用描述性的变量、函数和类名
      - 为复杂逻辑添加注释和文档
      - 遵循项目风格指南和代码约定
      - 优先使用成熟的库和工具
      - 正确处理边缘情况和错误
      - 编写有意义的提交信息
      - 保持逻辑相关的更改在同一提交中

  naming_conventions:
    name: "通用命名规范"
    pattern: "*"
    description: "文件和目录命名约定"
    rule: |
      - 目录名使用小写，多词用连字符分隔
      - 文件名要反映其用途和内容
      - 保持命名的一致性和描述性
      - 注：具体语言命名规范见对应的语言规则文件

  code_quality:
    name: "代码质量标准"
    pattern: "*.ts,*.vue,*.js"
    description: "代码质量和可维护性要求"
    rule: |
      - 保持函数和方法的简洁性
      - 避免深层嵌套，使用早期返回
      - 使用有意义的变量名，避免缩写
      - 保持一致的代码风格
      - 及时重构重复或复杂的代码
      - 编写自文档化的代码

language:
  response_language: "chinese"  # 始终使用中文回复用户
