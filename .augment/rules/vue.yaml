# Vue.js开发规范
# 适用于所有Vue组件文件

metadata:
  name: "vuejs_standards"
  description: "Vue.js编码规则和最佳实践"
  type: "agent_requested"
  applies_to: "**/*.vue"

rules:
  component_structure:
    name: "组件结构规范"
    pattern: "*.vue"
    description: "Vue组件的基本结构和组织"
    rule: |
      - 使用组合式API而非选项式API
      - 使用<script setup>语法糖
      - 保持组件小巧且专注单一职责
      - 正确集成TypeScript
      - 实现适当的props验证和类型定义
      - 使用正确的emit声明
      - 保持模板逻辑简洁，复杂逻辑移到computed或methods

  composition_api:
    name: "组合式API使用规范"
    pattern: "*.vue"
    description: "Composition API的正确使用方式"
    rule: |
      - 正确使用ref和reactive
      - ref用于基本类型，reactive用于对象
      - 实现适当的生命周期钩子
      - 使用composables实现可复用逻辑
      - 保持setup函数整洁，逻辑分组
      - 正确使用计算属性，避免副作用
      - 实现适当的侦听器，注意清理

  template_best_practices:
    name: "模板最佳实践"
    pattern: "*.vue"
    description: "Vue模板编写规范"
    rule: |
      - 使用语义化的HTML标签
      - 合理使用v-if和v-show
      - 为列表渲染提供唯一的key
      - 避免在模板中使用复杂表达式
      - 使用计算属性处理复杂逻辑
      - 事件处理器保持简洁
      - 合理使用插槽和作用域插槽

  state_management:
    name: "状态管理规范"
    pattern: "*.vue,stores/*.ts"
    description: "Vue状态管理最佳实践"
    rule: |
      - 使用Pinia进行全局状态管理
      - 保持stores模块化，按功能划分
      - 使用适当的状态组合
      - 实现适当的actions处理异步操作
      - 正确使用getters进行状态计算
      - 适当处理异步状态和错误状态
      - 避免在组件中直接修改store状态

  performance_optimization:
    name: "性能优化规范"
    pattern: "*.vue"
    description: "Vue组件性能优化指南"
    rule: |
      - 正确使用组件懒加载
      - 实现适当的缓存策略
      - 正确使用计算属性缓存
      - 避免不必要的侦听器
      - 正确使用v-show与v-if
      - 实现适当的key管理
      - 使用v-memo优化重复渲染
      - 合理使用异步组件

  typescript_integration:
    name: "Vue TypeScript集成"
    pattern: "*.vue"
    description: "Vue组件中TypeScript的使用规范"
    rule: |
      - 使用适当的组件类型定义
      - 实现适当的prop类型和默认值
      - 使用正确的emit声明和类型
      - 处理适当的类型推断
      - 使用适当的composable类型
      - 实现适当的store类型
      - 为复杂props定义接口

  styling_guidelines:
    name: "样式编写指南"
    pattern: "*.vue"
    description: "Vue组件样式编写规范"
    rule: |
      - 优先使用scoped样式避免污染
      - 全局样式放在非scoped的style标签中
      - 使用SCSS预处理器
      - 合理使用CSS变量
      - 遵循BEM命名规范
      - 避免深层选择器嵌套
      - 使用CSS模块化方案

  event_handling:
    name: "事件处理规范"
    pattern: "*.vue"
    description: "Vue事件处理最佳实践"
    rule: |
      - 使用描述性的事件名称
      - 正确使用事件修饰符
      - 避免在模板中使用复杂的事件处理
      - 合理使用自定义事件
      - 正确处理表单事件
      - 实现适当的事件清理

  testing_guidelines:
    name: "测试指南"
    pattern: "*.vue"
    description: "Vue组件测试规范"
    rule: |
      - 编写适当的单元测试
      - 实现适当的组件测试
      - 正确使用Vue Test Utils
      - 适当测试composables
      - 实现适当的模拟
      - 测试异步操作和错误处理
      - 保持测试的可读性和维护性
