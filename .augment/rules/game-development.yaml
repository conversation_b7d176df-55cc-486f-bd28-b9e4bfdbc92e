# 游戏开发规范
# 基于uni-yi项目游戏系统的新游戏开发指南

metadata:
  name: "game_development"
  description: "uni-yi项目新游戏开发规范和最佳实践"
  type: "always_apply"
  applies_to: "src/games/**/*"

rules:
  project_structure:
    name: "游戏项目结构规范"
    pattern: "src/games/**/*"
    description: "新游戏的标准目录结构和文件组织"
    rule: |
      # 标准游戏目录结构
      src/games/[game-name]/
      ├── index.vue              # 游戏主页面（必需）
      ├── config.ts              # 游戏配置文件（必需）
      └── logic/                 # 游戏逻辑目录（必需）
          ├── question-generator.ts # 题目生成器（必需）
          └── types.ts           # 游戏特定类型定义（必需）

      # 共享基础类位置
      src/games/shared/base/
      ├── base-game-logic.ts     # 基础游戏逻辑类
      └── base-question-generator.ts # 基础题目生成器

      # 命名规范
      - 游戏目录名使用kebab-case：hexagram-image-to-name
      - 文件名使用kebab-case：question-generator.ts
      - 类名使用PascalCase：HexagramImageToNameGameLogic
      - 配置常量使用UPPER_SNAKE_CASE：HEXAGRAM_IMAGE_TO_NAME_CONFIG

      # 重要变更说明
      - 不再需要单独的 game-logic.ts 文件
      - 游戏逻辑类直接在 index.vue 中定义，继承 BaseGameLogic
      - 基础类已移动到 shared/base 目录

  game_configuration:
    name: "游戏配置规范"
    pattern: "src/games/*/config.ts"
    description: "游戏配置文件的标准结构和必需字段"
    rule: |
      # 必需配置字段
      - id: 唯一游戏标识符（kebab-case）
      - name: 游戏显示名称
      - description: 游戏描述
      - icon: 游戏图标（emoji或图标名）
      - difficulty: 游戏难度（easy/medium/hard）
      - category: 游戏分类（hexagram/quiz/puzzle等）
      
      # 计时器配置（必需）
      timer: {
        totalTime: 总时间（秒）
        warningThreshold: 警告阈值（秒）
        dangerThreshold: 危险阈值（秒）
        autoStart: 是否自动开始
        paused: 是否暂停
      }
      
      # 游戏特定配置
      gameSpecific: {
        questionCount: 题目数量
        optionCount: 选项数量
        minScore: 最低分数
        maxScore: 最高分数
      }
      
      # 评分配置
      scoring: {
        baseScore: 基础分数
        timeBonus: 时间奖励
        accuracyBonus: 准确率奖励
        comboBonus: 连击奖励
        difficultyMultiplier: 难度倍数
        perfectBonus: 完美奖励
      }

  game_difficulty_rules:
    name: "游戏难度设置规范"
    pattern: "src/games/**/*"
    description: "游戏难度级别的定义和使用规范"
    rule: |
      # 重要规则：单独游戏不需要有难度设置
      - 每个游戏应该有固定的难度级别，在配置文件中预设
      - 不应该在游戏内提供难度选择功能
      - 难度应该通过不同的游戏模式或关卡来体现
      - 如需难度调整，应该创建不同的游戏变体

      # 标准难度级别
      - easy: 简单模式，适合新手，使用随机干扰项
      - medium: 中等模式，平衡挑战，使用混合策略
      - hard: 困难模式，高级挑战，使用相似干扰项

      # 难度配置位置
      - 难度配置应在游戏配置文件(config.ts)中定义
      - 游戏页面应该从配置中读取难度，而不是提供难度选择界面
      - 题目生成器应该根据配置的难度自动调整生成策略

  game_logic_architecture:
    name: "游戏逻辑架构规范"
    pattern: "src/games/*/index.vue"
    description: "游戏逻辑类的标准实现模式"
    rule: |
      # 新架构：游戏逻辑类直接在页面中定义
      # 在 index.vue 的 <script setup> 中定义：

      import { BaseGameLogic } from '@/games/shared/base/base-game-logic';
      import { YourQuestionGenerator } from './logic/question-generator';
      import { YOUR_GAME_CONFIG } from './config';
      import type { GameQuestion, GameOption } from './logic/types';

      class YourGameLogic extends BaseGameLogic<
        GameQuestion,
        GameOption,
        YourQuestionGenerator
      > {
        constructor() {
          super(
            YOUR_GAME_CONFIG,
            YourQuestionGenerator,
            'your-game-type'
          );
        }
      }

      # BaseGameLogic 已提供的核心方法
      - initGame(): 初始化游戏
      - startGame(): 开始游戏
      - answerQuestion(): 处理答题
      - getCurrentQuestion(): 获取当前题目
      - getGameState(): 获取游戏状态
      - endGame(): 结束游戏
      - selectOption(): 选择选项
      - confirmAnswer(): 确认答题
      - resetCurrentQuestion(): 重置当前题目
      - moveToNextQuestion(): 进入下一题

      # 集成要求
      - 必须继承 BaseGameLogic 基础类
      - 必须提供正确的泛型参数
      - BaseGameLogic 已自动集成 gameService
      - 无需手动调用 gameService 方法

  base_class_usage:
    name: "基础类使用规范"
    pattern: "src/games/**/*"
    description: "基础类的正确使用方式和继承模式"
    rule: |
      # 基础类位置
      src/games/shared/base/
      ├── base-game-logic.ts      # 基础游戏逻辑类
      └── base-question-generator.ts # 基础题目生成器

      # BaseGameLogic 使用模式
      import { BaseGameLogic } from '@/games/shared/base/base-game-logic';

      class YourGameLogic extends BaseGameLogic<
        GameQuestion,        # 游戏特定题目类型
        GameOption,         # 游戏特定选项类型
        YourQuestionGenerator # 游戏特定题目生成器
      > {
        constructor() {
          super(
            YOUR_GAME_CONFIG,      # 游戏配置
            YourQuestionGenerator, # 题目生成器类
            'your-game-type'       # 游戏类型标识
          );
        }
      }

      # BaseQuestionGenerator 使用模式
      import { BaseQuestionGenerator } from '@/games/shared/base/base-question-generator';

      export class YourQuestionGenerator extends BaseQuestionGenerator<
        GameQuestion,
        GameOption
      > {
        generateQuestion(index: number): GameQuestion | null {
          // 实现具体的题目生成逻辑
        }
      }

      # 基础类提供的功能
      BaseGameLogic:
      - 完整的游戏生命周期管理
      - 答题状态和会话管理
      - 自动集成 gameService
      - 错误处理和状态同步

      BaseQuestionGenerator:
      - 批量题目生成
      - 工具方法（随机打乱、ID生成等）
      - 项目使用状态管理
      - 配置参数访问

  type_definitions:
    name: "类型定义规范"
    pattern: "src/games/*/logic/types.ts"
    description: "游戏特定类型定义的标准模式"
    rule: |
      # 基础类型扩展模式
      - 扩展BaseGameOption定义游戏选项类型
      - 扩展BaseGameQuestion定义游戏题目类型
      - 重新导出共享类型以保持导入一致性

      # 示例结构
      import type { BaseGameOption, BaseGameQuestion } from '@/games/shared/types';

      export interface GameOption extends BaseGameOption {
        // 游戏特定字段，如：
        text?: string;      # 文字选项
        hexagram?: Hexagram; # 卦象选项
        image?: string;     # 图片选项
      }

      export interface GameQuestion extends BaseGameQuestion<GameOption> {
        // 游戏特定字段，如：
        hexagram?: Hexagram; # 题目卦象
        image?: string;      # 题目图片
      }

      # 重新导出共享类型
      export type { AnswerResult, GameDifficulty, GameState } from '@/games/shared/types';

  ui_component_integration:
    name: "UI组件集成规范"
    pattern: "src/games/*/index.vue"
    description: "游戏页面的标准UI结构和组件使用"
    rule: |
      # 新架构：插槽系统页面结构模板
      <template>
        <PageWrapper :show-back="true" customTitle="🎮 游戏名称">
          <GameController
            :game-logic-class="YourGameLogic"
            :config="YOUR_GAME_CONFIG.timer!"
          >
            <!-- 游戏内容插槽 -->
            <template #game-content="{
              question, disabled, gameActive, selectedOptionId, isAnswered,
              correctOptionId, buttonStates, currentQuestionIndex, totalQuestions,
              onSelect, onConfirm, onNext, onReset
            }">
              <GameContent
                :question="question"
                :disabled="disabled"
                :game-active="gameActive"
                :selected-option-id="selectedOptionId"
                :is-answered="isAnswered"
                :correct-option-id="correctOptionId"
                :button-states="buttonStates"
                :current-question-index="currentQuestionIndex"
                :total-questions="totalQuestions"
                @select="onSelect"
                @confirm="onConfirm"
                @next="onNext"
                @reset="onReset"
              >
                <!-- 题目内容插槽 -->
                <template #question-content="{ question }">
                  <!-- 自定义题目展示内容 -->
                </template>

                <!-- 选项内容插槽 -->
                <template #option-content="{ option }">
                  <!-- 自定义选项展示内容 -->
                </template>
              </GameContent>
            </template>
          </GameController>
        </PageWrapper>
      </template>

      # 必需组件导入
      import PageWrapper from '@/components/PageWrapper.vue';
      import GameController from '@/games/shared/components/GameController.vue';
      import GameContent from '@/games/shared/components/GameContent.vue';

      # 架构优势
      - GameController 管理游戏状态和生命周期
      - GameContent 提供统一的答题界面
      - 插槽系统支持完全自定义的题目和选项内容
      - 自动处理答题状态、结果反馈和按钮控制

  unified_question_area:
    name: "统一答题区域系统规范"
    pattern: "src/games/**/*"
    description: "游戏统一答题区域组件和开发规范"
    rule: |
      # 统一答题区域组件架构
      GameController → GameContent → 插槽系统
      - GameController: 管理游戏状态和生命周期
      - GameContent: 提供统一的答题界面和交互逻辑
      - 插槽系统: 支持完全自定义的题目和选项内容

      # 核心功能特性
      1. 智能按钮状态管理
         - 重置按钮：默认禁用，选择答案后激活
         - 确定按钮：默认禁用，选择答案后激活
         - 下一题按钮：默认禁用，答题后激活

      2. 原地结果反馈系统
         - 答对时：正确选项显示绿色 ✓ 标记
         - 答错时：错误选项显示红色 ✗ 标记，正确选项显示绿色 ✓ 标记
         - 表情反馈：答题后在标题旁显示 😊 或 😢
         - 结果提示：底部显示详细的答题结果信息

      3. 完整的样式系统
         - 保留所有现有的题目展示区样式
         - 保留所有现有的选项区域样式
         - 统一的按钮控制区域样式
         - 响应式布局适配不同屏幕

      4. 灵活的插槽系统
         - question-content: 自定义题目展示（图片、卦象、文字等）
         - option-content: 自定义选项展示（文字、图片、卦象等）
         - 支持任意复杂的内容结构

      # 开发优势
      - 游戏页面代码减少 90%，只需定义插槽内容
      - 统一的交互逻辑和视觉设计
      - 自动处理所有答题状态和结果反馈
      - 完全保留现有样式，无需修改 CSS
      - 跨平台兼容性保证

      # 性能要求
      - 反馈动画流畅度 >= 60fps
      - 反馈响应时间 <= 100ms
      - 统一组件减少内存占用
      - 支持低性能设备的降级处理

  question_text_standards:
    name: "题目文字规范"
    pattern: "src/games/*/logic/question-generator.ts"
    description: "游戏题目文字的编写规范和最佳实践"
    rule: |
      # 题目文字格式规范
      - 题目文字应以相关emoji开头，增强视觉识别
      - emoji与文字之间用一个空格分隔
      - 使用简洁明了的描述性语言
      - 保持语言风格的一致性

      # 常用emoji指南
      - 📖 卦名配图类游戏
      - 🔮 看图识卦类游戏
      - 🎯 选择题类游戏
      - 🎨 创意类游戏
      - 🧩 拼图类游戏
      - ⚡ 快速反应类游戏
      - 🎪 娱乐类游戏

      # 示例格式
      正确：'📖 请选择卦名对应的卦象图形'
      正确：'🔮 根据卦象图形选择正确的卦名'
      错误：'请选择卦名对应的卦象图形📖'
      错误：'📖请选择卦名对应的卦象图形'（缺少空格）

      # 文字内容要求
      - 指令清晰明确，用户一看就懂
      - 避免使用过于复杂的词汇
      - 保持积极正面的语调
      - 符合游戏主题和氛围

  styling_standards:
    name: "样式规范"
    pattern: "src/games/**/*.vue"
    description: "游戏样式的编写规范和设计系统"
    rule: |
      # CSS单位规范
      - 所有尺寸单位必须使用rpx
      - 特殊情况（如1px边框）可使用px，需注释说明

      # 颜色系统
      - 主题色：#8b4513（棕色）
      - 辅助色：#d4af37（金色）
      - 背景色：#f8f4e9（米色）
      - 文字色：#333（深灰）

      # 样式组织
      - 优先使用共享样式类（game-common.scss）
      - 游戏特定样式写在组件内部
      - 使用scoped样式避免污染
      - 遵循响应式设计原则

      # 设计原则
      - 保持视觉一致性
      - 适配不同屏幕尺寸
      - 考虑无障碍访问
      - 优化动画性能

  game_registration:
    name: "游戏注册规范"
    pattern: "src/games/registry.ts"
    description: "新游戏的注册和配置管理"
    rule: |
      # 注册步骤
      1. 在registry.ts中导入游戏配置
      2. 使用createGameFromConfig()生成注册信息
      3. 添加到REGISTERED_GAMES数组
      4. 在pages.json中添加页面路由
      
      # 路由配置
      - path: "games/[game-name]/index"
      - navigationStyle: "custom"
      - navigationBarTitleText: 游戏名称
      
      # 分类管理
      - 选择合适的游戏分类
      - 如需新分类，在GAME_CATEGORIES中添加

  testing_requirements:
    name: "测试要求"
    pattern: "src/games/**/*"
    description: "游戏开发的测试规范和质量保证"
    rule: |
      # 必需测试
      1. 游戏逻辑单元测试
         - 题目生成正确性
         - 答题逻辑准确性
         - 分数计算正确性
         - 边界条件处理
      
      2. 组件集成测试
         - 游戏流程完整性
         - 状态管理正确性
         - 错误处理机制
      
      3. 跨平台兼容性测试
         - H5环境测试
         - 微信小程序测试
         - 响应式布局测试
      
      # 性能要求
      - 游戏启动时间 < 2秒
      - 答题响应时间 < 500ms
      - 内存使用合理，无泄漏
      - 动画流畅度 >= 60fps
      
      # 质量标准
      - 代码覆盖率 >= 90%
      - 无TypeScript错误
      - 通过ESLint检查
      - 符合无障碍标准

  error_handling:
    name: "错误处理规范"
    pattern: "src/games/**/*.ts"
    description: "游戏中的错误处理和用户体验"
    rule: |
      # 错误处理策略
      1. 数据加载失败
         - 显示友好错误提示
         - 提供重试机制
         - 记录错误日志
      
      2. 网络异常
         - 离线模式支持
         - 数据本地缓存
         - 自动重连机制
      
      3. 游戏逻辑错误
         - 状态回滚机制
         - 用户操作撤销
         - 数据一致性保证
      
      # 用户体验
      - 加载状态指示
      - 操作反馈提示
      - 错误信息本地化
      - 优雅降级处理

  performance_optimization:
    name: "性能优化规范"
    pattern: "src/games/**/*"
    description: "游戏性能优化的最佳实践"
    rule: |
      # 代码优化
      - 避免不必要的重新渲染
      - 合理使用computed和watch
      - 及时清理事件监听器
      - 优化大数据列表渲染
      
      # 资源优化
      - 图片懒加载
      - 组件按需加载
      - 减少包体积
      - 缓存策略优化
      
      # 内存管理
      - 避免内存泄漏
      - 及时释放资源
      - 合理使用对象池
      - 监控内存使用

  documentation_standards:
    name: "文档规范"
    pattern: "src/games/**/*"
    description: "游戏开发文档的编写标准"
    rule: |
      # 代码注释
      - 类和方法必须有JSDoc注释
      - 复杂逻辑必须有行内注释
      - 配置参数必须有说明
      - 算法实现必须有解释
      
      # 文档文件
      - README.md: 游戏介绍和使用说明
      - CHANGELOG.md: 版本更新记录
      - API.md: 接口文档（如适用）
      
      # 注释语言
      - 统一使用中文注释
      - 技术术语可使用英文
      - 保持注释简洁明了

  data_management:
    name: "数据管理规范"
    pattern: "src/games/**/*.ts"
    description: "游戏数据的存储、同步和管理规范"
    rule: |
      # 数据存储策略
      - 游戏配置：静态配置文件
      - 用户数据：使用gameService统一管理
      - 临时数据：组件内部状态管理
      - 缓存数据：使用storage工具函数

      # 数据同步
      - 游戏开始：调用gameService.startGame()
      - 游戏结束：调用gameService.endGame()
      - 实时更新：使用eventBus事件系统
      - 离线支持：本地数据缓存机制

      # 数据验证
      - 输入数据类型检查
      - 业务逻辑验证
      - 数据完整性校验
      - 异常数据处理

  service_integration:
    name: "服务集成规范"
    pattern: "src/games/**/*.ts"
    description: "与共享服务系统的集成规范"
    rule: |
      # 必需服务集成
      1. gameService: 统一游戏服务接口
         - 用户管理和游戏生命周期
         - 积分计算和排名更新
         - 成就检查和等级提升

      2. eventBus: 事件驱动通信
         - 游戏状态变化通知
         - 跨组件数据同步
         - 系统级事件处理

      3. storage: 数据持久化
         - 游戏进度保存
         - 用户偏好设置
         - 缓存数据管理

      # 服务使用模式
      - 依赖注入而非直接导入
      - 错误处理和降级策略
      - 异步操作的正确处理
      - 服务状态的监听和响应

  security_guidelines:
    name: "安全规范"
    pattern: "src/games/**/*.ts"
    description: "游戏开发中的安全考虑和实现"
    rule: |
      # 数据安全
      - 敏感数据加密存储
      - 用户输入验证和过滤
      - 防止XSS和注入攻击
      - 数据传输加密

      # 业务安全
      - 防止作弊和刷分
      - 游戏逻辑服务端验证
      - 异常行为检测
      - 数据完整性校验

      # 隐私保护
      - 最小化数据收集
      - 用户同意机制
      - 数据匿名化处理
      - 符合隐私法规要求

  development_workflow:
    name: "开发工作流规范"
    pattern: "src/games/**/*"
    description: "新游戏开发的标准工作流程"
    rule: |
      # 开发阶段
      1. 需求分析和设计
         - 游戏玩法设计
         - 技术方案评估
         - UI/UX设计稿
         - 数据结构设计

      2. 开发实现
         - 创建游戏目录结构
         - 实现配置文件
         - 开发游戏逻辑
         - 实现UI组件
         - 集成共享服务

      3. 测试验证
         - 单元测试编写
         - 集成测试验证
         - 跨平台兼容性测试
         - 性能测试
         - 用户体验测试

      4. 部署发布
         - 游戏注册
         - 路由配置
         - 构建优化
         - 发布验证

  best_practices:
    name: "最佳实践指南"
    pattern: "src/games/**/*"
    description: "基于现有游戏总结的开发最佳实践"
    rule: |
      # 架构设计最佳实践
      - 继承 BaseGameLogic 而不是从零开始
      - 使用插槽系统而不是硬编码UI
      - 游戏逻辑类直接在页面中定义，简化文件结构
      - 充分利用 GameController 和 GameContent 的统一架构

      # 代码组织最佳实践
      - 保持单一职责原则，每个文件职责明确
      - 使用TypeScript严格模式，确保类型安全
      - 遵循现有代码风格，保持项目一致性
      - 合理使用设计模式，避免过度设计
      - 题目生成器继承 BaseQuestionGenerator

      # 性能优化最佳实践
      - 避免在render函数中进行复杂计算
      - 合理使用Vue的响应式系统
      - 及时清理定时器和事件监听器
      - 优化图片和资源加载
      - 利用统一组件减少重复渲染

      # 用户体验最佳实践
      - 提供清晰的操作反馈（已由 GameContent 自动处理）
      - 实现优雅的加载状态
      - 处理网络异常情况
      - 保持界面响应流畅
      - 题目文字使用emoji增强视觉效果

      # 维护性最佳实践
      - 编写清晰的注释和文档
      - 使用有意义的变量和函数名
      - 保持函数简洁，避免过长函数
      - 定期重构和优化代码
      - 充分利用基础类，避免重复实现

      # 开发效率最佳实践
      - 新游戏开发只需关注：配置、题目生成、类型定义、插槽内容
      - 90%的UI和交互逻辑由统一架构自动处理
      - 使用现有游戏作为模板，快速创建新游戏
      - 遵循标准目录结构和命名规范

  common_patterns:
    name: "常用模式参考"
    pattern: "src/games/**/*"
    description: "基于现有游戏提取的常用开发模式"
    rule: |
      # 题目生成模式
      - 使用QuestionGenerator类管理题目生成
      - 实现随机化和去重逻辑
      - 支持难度分级和自定义参数
      - 提供题目验证和质量检查

      # 状态管理模式
      - 使用GameState接口统一状态结构
      - 实现状态的不可变更新
      - 提供状态快照和回滚机制
      - 使用事件驱动更新UI

      # 评分计算模式
      - 基础分数 + 时间奖励 + 准确率奖励
      - 连击奖励和完美奖励机制
      - 难度系数和个人最佳记录
      - 多维度评分和排名系统

      # 错误处理模式
      - try-catch包装关键操作
      - 提供用户友好的错误提示
      - 实现自动重试和降级策略
      - 记录错误日志用于分析

  migration_guide:
    name: "迁移指南"
    pattern: "src/games/**/*"
    description: "从旧版本或其他系统迁移游戏的指导"
    rule: |
      # 数据迁移
      - 分析现有数据结构
      - 设计迁移脚本
      - 实现数据转换逻辑
      - 验证迁移结果

      # 代码迁移
      - 适配新的架构模式
      - 更新依赖和导入
      - 重构不兼容的代码
      - 更新测试用例

      # 配置迁移
      - 转换配置格式
      - 更新路由配置
      - 适配新的服务接口
      - 验证功能完整性

  troubleshooting:
    name: "故障排除指南"
    pattern: "src/games/**/*"
    description: "常见问题的诊断和解决方案"
    rule: |
      # 常见问题
      1. 游戏无法启动
         - 检查配置文件格式
         - 验证依赖导入
         - 查看控制台错误
         - 检查路由配置

      2. 状态更新异常
         - 检查响应式数据绑定
         - 验证事件触发逻辑
         - 查看状态管理流程
         - 检查异步操作处理

      3. 性能问题
         - 分析渲染性能
         - 检查内存使用
         - 优化计算逻辑
         - 减少不必要的更新

      4. 兼容性问题
         - 测试不同平台表现
         - 检查条件编译
         - 验证API兼容性
         - 适配平台特性

  deployment_standards:
    name: "部署规范"
    pattern: "src/games/**/*"
    description: "游戏部署和发布的标准流程"
    rule: |
      # 构建优化
      - 代码压缩和混淆
      - 资源优化和压缩
      - 依赖分析和清理
      - 构建产物验证

      # 版本管理
      - 语义化版本号
      - 变更日志维护
      - 向后兼容性检查
      - 回滚策略准备

      # 发布流程
      - 多环境测试验证
      - 灰度发布策略
      - 监控和告警设置
      - 用户反馈收集
