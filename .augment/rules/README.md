# Augment Rules 规则文件说明

## 📋 规则文件结构

```
.augment/
├── rules.yaml              # 主规则文件，定义规则应用逻辑
├── config.yaml             # 项目配置文件
└── rules/                  # 分场景规则文件夹
    ├── general.yaml        # 通用开发规范
    ├── typescript.yaml     # TypeScript规范
    ├── vue.yaml           # Vue.js规范
    ├── uni-app.yaml       # uni-app项目特定规范
    └── README.md          # 本文件
```

## 🎯 各规则文件职责分工

### **general.yaml - 通用开发规范**
- ✅ 项目结构组织原则
- ✅ 通用开发原则 (DRY, KISS等)
- ✅ 代码质量标准
- ✅ 通用命名规范 (目录、文件)
- ✅ 多平台支持概述
- ❌ 不包含：具体语言的命名规范、具体平台适配细节

### **typescript.yaml - TypeScript规范**
- ✅ 类型系统规范
- ✅ TypeScript特定命名约定
- ✅ 代码组织规范
- ✅ 函数和方法规范
- ✅ 错误处理规范
- ✅ 设计模式应用
- ❌ 不包含：Vue组件相关的TypeScript用法

### **vue.yaml - Vue.js规范**
- ✅ 组件结构规范
- ✅ 组合式API使用规范
- ✅ 模板最佳实践
- ✅ 状态管理规范
- ✅ 性能优化规范
- ✅ Vue TypeScript集成
- ✅ 样式编写指南
- ✅ 事件处理规范
- ❌ 不包含：项目特定的组件使用方式

### **uni-app.yaml - 项目特定规范**
- ✅ 样式组织规范 (组件内样式)
- ✅ 平台适配规范 (条件编译)
- ✅ 项目特定组件使用 (PageWrapper, YiIcon)
- ✅ 安全区域适配规范
- ✅ 存储和状态管理规范
- ✅ 图标系统规范
- ✅ TabBar处理规范
- ✅ 性能优化考虑
- ❌ 不包含：通用的Vue或TypeScript规范

## 🔄 规则应用优先级

1. **uni_app_specific** (最高优先级)
   - 项目特定规则优先于通用规则
   
2. **vuejs_standards**
   - Vue.js框架特定规则
   
3. **typescript_standards**
   - TypeScript语言特定规则
   
4. **general_principles** (最低优先级)
   - 通用开发规范作为基础

## 📝 规则交叉引用

### **命名规范分工**
- `general.yaml`: 目录和文件命名的通用原则
- `typescript.yaml`: TypeScript特定的命名约定 (类型、接口、变量等)
- `vue.yaml`: Vue组件相关的命名规范
- `uni-app.yaml`: 项目特定的命名要求

### **组件开发分工**
- `vue.yaml`: Vue组件的基础开发规范
- `typescript.yaml`: 组件中TypeScript的使用规范
- `uni-app.yaml`: 项目特定组件的使用方式

### **平台适配分工**
- `general.yaml`: 多平台支持的概述
- `uni-app.yaml`: 具体的平台适配实现细节

## ⚠️ 避免重复的原则

1. **单一职责**: 每个规则文件专注于特定领域
2. **层次分明**: 通用 → 语言特定 → 框架特定 → 项目特定
3. **交叉引用**: 使用注释说明相关规则的位置
4. **优先级明确**: 项目特定规则可以覆盖通用规则

## 🔧 维护指南

### **添加新规则时**
1. 确定规则的适用范围和优先级
2. 选择合适的规则文件
3. 检查是否与现有规则重复
4. 添加交叉引用注释

### **修改现有规则时**
1. 检查是否影响其他规则文件
2. 更新相关的交叉引用
3. 确保优先级逻辑正确

### **定期检查**
- 检查规则文件间的重复内容
- 验证规则应用的逻辑正确性
- 更新交叉引用文档
