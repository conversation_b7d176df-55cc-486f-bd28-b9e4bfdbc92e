# uni-app项目特定规范
# 适用于uni-yi项目的特定规则

metadata:
  name: "uni_app_specific"
  description: "uni-yi项目特定的开发规范"
  type: "always_apply"
  applies_to: "src/**/*"

rules:
  style_organization:
    name: "样式组织规范"
    pattern: "*.vue,*.scss"
    description: "uni-yi项目样式组织方式"
    rule: |
      - 样式与组件紧密结合，避免单独的styles文件夹
      - TabBar相关样式应放在YiTabBar.vue中
      - 安全区域样式应放在PageWrapper.vue中
      - uni.scss必须在src根目录（uni-app要求）
      - 组件样式使用scoped，全局样式放在组件的非scoped style中
      - 使用条件编译处理平台样式差异

  css_units:
    name: "CSS单位规范"
    pattern: "*.vue,*.scss,*.css"
    description: "uni-app项目CSS单位使用规范"
    rule: |
      - 所有CSS尺寸单位必须使用rpx，避免使用px
      - rpx是uni-app的响应式单位，能自动适配不同屏幕尺寸
      - 1rpx = 屏幕宽度/750，确保在不同设备上的一致性
      - 特殊情况（如1px边框）可以使用px，但需要明确注释原因
      - 字体大小、边距、内边距、宽高等都应使用rpx
      - 避免混用px和rpx，保持代码一致性

  platform_adaptation:
    name: "平台适配规范"
    pattern: "*.vue,*.ts,*.scss"
    description: "多平台适配的处理方式"
    rule: |
      - 使用条件编译处理平台差异
      - H5环境需要隐藏原生tabBar
      - 小程序和APP使用SVG图标，H5可使用emoji后备
      - 安全区域适配只在小程序和APP中生效
      - H5环境禁用安全区域适配
      - 使用统一的图标系统处理平台差异

  component_development:
    name: "uni-yi项目组件规范"
    pattern: "components/*.vue"
    description: "uni-yi项目特定的组件开发标准"
    rule: |
      - 使用PageWrapper包装所有页面
      - 使用YiIcon组件统一管理图标
      - 注：基础组件开发规范见vue.yaml和typescript.yaml

  safe_area_adaptation:
    name: "安全区域适配规范"
    pattern: "*.vue"
    description: "安全区域处理的统一方案"
    rule: |
      - 使用PageWrapper组件包装页面
      - 安全区域样式集中在PageWrapper.vue中
      - H5环境完全禁用安全区域适配
      - 小程序和APP使用env()和constant()函数
      - 使用统一的安全区域CSS类名
      - 避免在单个页面中重复安全区域处理

  storage_management:
    name: "存储和状态管理规范"
    pattern: "*.ts,stores/*.ts"
    description: "数据存储和状态管理方案"
    rule: |
      - 存储键名在utils/constants.ts中统一定义
      - 使用Pinia管理运行时状态
      - 使用localStorage管理持久化数据
      - 应用状态使用stores/app.ts
      - 用户设置使用stores/settings.ts
      - 避免复杂的持久化插件，保持轻量级

  file_organization:
    name: "文件组织规范"
    pattern: "*"
    description: "uni-yi项目文件和目录组织"
    rule: |
      - components/ 存放公共组件
      - pages/ 存放页面文件，按功能模块划分
      - utils/ 存放工具函数和常量
      - stores/ 存放Pinia状态管理
      - static/ 存放静态资源
      - assets/ 存放需要处理的资源
      - 避免过深的目录嵌套
      - 相关文件放在同一目录下

  icon_system:
    name: "图标系统规范"
    pattern: "*.vue"
    description: "统一的图标使用方案"
    rule: |
      - 使用YiIcon组件统一管理图标
      - SVG图标定义在UniSvgIcon.vue中
      - 小程序使用CSS mask，其他平台使用SVG
      - H5环境可以使用emoji作为后备方案
      - 图标名称使用kebab-case
      - 为图标提供合适的尺寸和颜色属性

  tabbar_handling:
    name: "TabBar处理规范"
    pattern: "*TabBar*.vue"
    description: "TabBar跨平台处理方案"
    rule: |
      - TabBar相关样式集中在YiTabBar.vue中
      - H5环境隐藏原生tabBar，显示自定义TabBar
      - 使用统一的图标系统
      - 激活状态使用底部指示条，不使用背景高亮
      - 保持与小程序TabBar的一致性
      - 使用颜色继承机制统一管理颜色

  performance_considerations:
    name: "性能优化考虑"
    pattern: "*.vue,*.ts"
    description: "uni-yi项目性能优化指南"
    rule: |
      - 合理使用组件懒加载
      - 优化图片资源，使用适当的格式和尺寸
      - 避免在模板中使用复杂计算
      - 合理使用缓存策略
      - 注意内存泄漏，及时清理事件监听器
      - 使用防抖和节流优化用户交互
