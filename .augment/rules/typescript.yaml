# TypeScript开发规范
# 适用于所有TypeScript文件

metadata:
  name: "typescript_standards"
  description: "TypeScript编码规则和最佳实践"
  type: "agent_requested"
  applies_to: "**/*.ts,**/*.tsx,**/*.d.ts"

rules:
  type_system:
    name: "类型系统规范"
    pattern: "*.ts,*.tsx,*.d.ts"
    description: "TypeScript类型定义和使用规范"
    rule: |
      - 对象定义优先使用interface而非type
      - 联合类型、交叉类型和映射类型使用type
      - 避免使用any，未知类型优先使用unknown
      - 使用严格的TypeScript配置
      - 充分利用TypeScript的内置工具类型
      - 使用泛型实现可复用的类型模式
      - 为复杂类型添加注释说明

  naming_conventions:
    name: "TypeScript命名约定"
    pattern: "*.ts,*.tsx,*.d.ts"
    description: "TypeScript特定的命名规范"
    rule: |
      - 类型名称和接口使用PascalCase (UserInfo, ButtonProps)
      - 变量和函数使用camelCase (userName, getUserInfo)
      - 常量使用UPPER_SNAKE_CASE (API_BASE_URL)
      - 使用描述性名称（如isLoading, hasError）
      - Props接口后缀使用'Props'（如ButtonProps）
      - 泛型参数使用单个大写字母，从T开始 (T, U, K, V)
      - 枚举使用PascalCase，成员使用UPPER_SNAKE_CASE
      - 组件文件使用PascalCase (YiTabBar.vue)
      - 工具文件使用kebab-case (safe-area.ts)

  code_organization:
    name: "代码组织规范"
    pattern: "*.ts,*.tsx,*.d.ts"
    description: "TypeScript代码结构和组织"
    rule: |
      - 类型定义应靠近使用它们的地方
      - 共享类型从专用类型文件导出
      - 使用桶导出（index.ts）组织导出
      - 共享类型放在types目录中
      - 组件props与其组件共同放置
      - 按功能分组导入语句
      - 先导入第三方库，再导入本地模块

  functions_and_methods:
    name: "函数和方法规范"
    pattern: "*.ts,*.tsx,*.d.ts"
    description: "函数定义和实现规范"
    rule: |
      - 公共函数使用显式返回类型
      - 回调和方法使用箭头函数
      - 复杂类型场景使用函数重载
      - 优先使用async/await而非Promises
      - 函数参数超过3个时使用对象参数
      - 为函数添加JSDoc注释

  error_handling:
    name: "错误处理规范"
    pattern: "*.ts,*.tsx,*.d.ts"
    description: "TypeScript错误处理最佳实践"
    rule: |
      - 创建领域特定错误类型
      - 对可能失败的操作使用Result类型
      - 实现适当的错误边界
      - 使用带有类型化catch子句的try-catch块
      - 正确处理Promise拒绝
      - 避免静默忽略错误

  best_practices:
    name: "TypeScript最佳实践"
    pattern: "*.ts,*.tsx,*.d.ts"
    description: "TypeScript开发最佳实践"
    rule: |
      - 在tsconfig.json中启用严格模式
      - 不可变属性使用readonly
      - 利用可辨识联合类型提高类型安全性
      - 使用类型守卫进行运行时类型检查
      - 实现适当的空值检查
      - 避免不必要的类型断言
      - 使用const assertions提高类型推断

  patterns:
    name: "设计模式应用"
    pattern: "*.ts,*.tsx,*.d.ts"
    description: "TypeScript中的设计模式"
    rule: |
      - 复杂对象创建使用构建者模式
      - 数据访问实现仓储模式
      - 对象创建使用工厂模式
      - 利用依赖注入提高可测试性
      - 使用模块模式实现封装
      - 合理使用单例模式
